import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// مدير الأخطاء المركزي للتطبيق
/// 
/// يوفر معالجة موحدة وذكية للأخطاء مع رسائل واضحة باللغة العربية
/// ويتضمن آليات إعادة المحاولة والتعافي من الأخطاء
class ErrorHandler {
  /// أنواع الأخطاء المختلفة في التطبيق
  static const String networkError = 'network_error';
  static const String authError = 'auth_error';
  static const String firestoreError = 'firestore_error';
  static const String validationError = 'validation_error';
  static const String permissionError = 'permission_error';
  static const String unknownError = 'unknown_error';

  /// تحليل الخطأ وإرجاع معلومات مفصلة عنه
  /// 
  /// يحلل نوع الخطأ ويحدد الرسالة المناسبة والإجراءات الممكنة
  static ErrorInfo analyzeError(dynamic error) {
    if (error is FirebaseAuthException) {
      return _handleFirebaseAuthError(error);
    } else if (error is FirebaseException) {
      return _handleFirebaseError(error);
    } else if (error is FormatException) {
      return _handleValidationError(error);
    } else {
      return _handleUnknownError(error);
    }
  }

  /// معالجة أخطاء Firebase Auth
  static ErrorInfo _handleFirebaseAuthError(FirebaseAuthException error) {
    String message;
    String actionText = 'إعادة المحاولة';
    bool canRetry = true;

    switch (error.code) {
      case 'user-not-found':
        message = 'لا يوجد حساب مسجل بهذا البريد الإلكتروني';
        canRetry = false;
        break;
      case 'wrong-password':
        message = 'كلمة المرور غير صحيحة';
        canRetry = false;
        break;
      case 'email-already-in-use':
        message = 'هذا البريد الإلكتروني مستخدم بالفعل';
        canRetry = false;
        break;
      case 'weak-password':
        message = 'كلمة المرور ضعيفة جداً';
        canRetry = false;
        break;
      case 'invalid-email':
        message = 'البريد الإلكتروني غير صحيح';
        canRetry = false;
        break;
      case 'user-disabled':
        message = 'تم تعطيل هذا الحساب. تواصل مع الإدارة';
        canRetry = false;
        break;
      case 'too-many-requests':
        message = 'تم تجاوز عدد المحاولات المسموح. حاول لاحقاً';
        actionText = 'حاول لاحقاً';
        break;
      case 'network-request-failed':
        message = 'مشكلة في الاتصال بالإنترنت';
        actionText = 'تحقق من الاتصال';
        break;
      default:
        message = 'خطأ في المصادقة: ${error.message ?? 'خطأ غير معروف'}';
    }

    return ErrorInfo(
      type: authError,
      message: message,
      actionText: actionText,
      canRetry: canRetry,
      originalError: error,
    );
  }

  /// معالجة أخطاء Firebase (Firestore, Storage, etc.)
  static ErrorInfo _handleFirebaseError(FirebaseException error) {
    String message;
    String actionText = 'إعادة المحاولة';
    bool canRetry = true;

    switch (error.code) {
      case 'permission-denied':
        message = 'ليس لديك صلاحية للوصول لهذه البيانات';
        canRetry = false;
        break;
      case 'not-found':
        message = 'البيانات المطلوبة غير موجودة';
        canRetry = false;
        break;
      case 'already-exists':
        message = 'البيانات موجودة بالفعل';
        canRetry = false;
        break;
      case 'resource-exhausted':
        message = 'تم تجاوز الحد المسموح للعمليات';
        actionText = 'حاول لاحقاً';
        break;
      case 'failed-precondition':
        message = 'لا يمكن تنفيذ العملية في الوقت الحالي';
        break;
      case 'aborted':
        message = 'تم إلغاء العملية بسبب تعارض';
        break;
      case 'out-of-range':
        message = 'البيانات المدخلة خارج النطاق المسموح';
        canRetry = false;
        break;
      case 'unimplemented':
        message = 'هذه الميزة غير متوفرة حالياً';
        canRetry = false;
        break;
      case 'internal':
        message = 'خطأ داخلي في الخادم';
        break;
      case 'unavailable':
        message = 'الخدمة غير متوفرة حالياً';
        actionText = 'حاول لاحقاً';
        break;
      case 'deadline-exceeded':
        message = 'انتهت مهلة الاتصال';
        break;
      default:
        message = 'خطأ في قاعدة البيانات: ${error.message ?? 'خطأ غير معروف'}';
    }

    return ErrorInfo(
      type: firestoreError,
      message: message,
      actionText: actionText,
      canRetry: canRetry,
      originalError: error,
    );
  }

  /// معالجة أخطاء التحقق من صحة البيانات
  static ErrorInfo _handleValidationError(FormatException error) {
    return ErrorInfo(
      type: validationError,
      message: 'البيانات المدخلة غير صحيحة: ${error.message}',
      actionText: 'تصحيح البيانات',
      canRetry: false,
      originalError: error,
    );
  }

  /// معالجة الأخطاء غير المعروفة
  static ErrorInfo _handleUnknownError(dynamic error) {
    String message = 'حدث خطأ غير متوقع';
    
    if (error.toString().contains('SocketException') || 
        error.toString().contains('NetworkException')) {
      message = 'مشكلة في الاتصال بالإنترنت';
    } else if (error.toString().contains('TimeoutException')) {
      message = 'انتهت مهلة الاتصال';
    } else if (error.toString().contains('FormatException')) {
      message = 'خطأ في تنسيق البيانات';
    }

    return ErrorInfo(
      type: unknownError,
      message: message,
      actionText: 'إعادة المحاولة',
      canRetry: true,
      originalError: error,
    );
  }

  /// عرض رسالة خطأ مع إمكانية إعادة المحاولة
  /// 
  /// يعرض SnackBar مع رسالة الخطأ وزر إجراء إذا كان متاحاً
  static void showErrorSnackBar(
    BuildContext context,
    dynamic error, {
    VoidCallback? onRetry,
    Duration duration = const Duration(seconds: 4),
  }) {
    final errorInfo = analyzeError(error);
    
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              _getErrorIcon(errorInfo.type),
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                errorInfo.message,
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ],
        ),
        backgroundColor: _getErrorColor(errorInfo.type),
        duration: duration,
        behavior: SnackBarBehavior.floating,
        action: errorInfo.canRetry && onRetry != null
            ? SnackBarAction(
                label: errorInfo.actionText,
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
      ),
    );
  }

  /// الحصول على أيقونة مناسبة لنوع الخطأ
  static IconData _getErrorIcon(String errorType) {
    switch (errorType) {
      case authError:
        return Icons.lock_outline;
      case firestoreError:
        return Icons.cloud_off;
      case networkError:
        return Icons.wifi_off;
      case validationError:
        return Icons.error_outline;
      case permissionError:
        return Icons.security;
      default:
        return Icons.warning;
    }
  }

  /// الحصول على لون مناسب لنوع الخطأ
  static Color _getErrorColor(String errorType) {
    switch (errorType) {
      case authError:
        return Colors.red[700]!;
      case firestoreError:
        return Colors.orange[700]!;
      case networkError:
        return Colors.blue[700]!;
      case validationError:
        return Colors.amber[700]!;
      case permissionError:
        return Colors.purple[700]!;
      default:
        return Colors.red[600]!;
    }
  }
}

/// معلومات الخطأ المحللة
class ErrorInfo {
  final String type;
  final String message;
  final String actionText;
  final bool canRetry;
  final dynamic originalError;

  const ErrorInfo({
    required this.type,
    required this.message,
    required this.actionText,
    required this.canRetry,
    this.originalError,
  });

  @override
  String toString() {
    return 'ErrorInfo(type: $type, message: $message, canRetry: $canRetry)';
  }
}
