import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:school_management_system/constants/yemen_data_constants.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/utils/helpers.dart';

/// ديالوج محسن لإضافة أو تعديل بيانات مسؤول مع الحقول الجديدة.
class AdminFormDialog extends ConsumerStatefulWidget {
  final UserModel? admin; // يكون null في حالة الإضافة

  const AdminFormDialog({super.key, this.admin});

  @override
  ConsumerState<AdminFormDialog> createState() => _AdminFormDialogState();
}

class _AdminFormDialogState extends ConsumerState<AdminFormDialog>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();

  // ===== المتحكمات الأساسية =====
  late final TextEditingController _nameController;
  late final TextEditingController _emailController;
  late final TextEditingController _passwordController;

  // ===== المتحكمات الجديدة =====
  late final TextEditingController _phoneController;
  late final TextEditingController _addressController;
  late final TextEditingController _nationalIdController;

  // ===== المتغيرات للحقول المنسدلة =====
  String? _selectedGovernorate;
  String? _selectedNationality;
  String? _selectedGender;
  DateTime? _selectedDateOfBirth;

  // ===== متغيرات الصورة =====
  File? _imageFile;
  Uint8List? _imageBytes;
  String? _existingImageUrl;

  // ===== متحكم التبويبات =====
  late TabController _tabController;
  bool _isLoading = false;

  bool get _isEditMode => widget.admin != null;

  @override
  void initState() {
    super.initState();

    // تهيئة متحكم التبويبات
    _tabController = TabController(length: 3, vsync: this);

    // تهيئة المتحكمات الأساسية
    _nameController = TextEditingController(text: widget.admin?.name ?? '');
    _emailController = TextEditingController(text: widget.admin?.email ?? '');
    _passwordController = TextEditingController();

    // تهيئة المتحكمات الجديدة
    _phoneController = TextEditingController(
      text: widget.admin?.phoneNumber ?? '',
    );
    _addressController = TextEditingController(
      text: widget.admin?.address ?? '',
    );
    _nationalIdController = TextEditingController(
      text: widget.admin?.nationalId ?? '',
    );

    // تهيئة القيم المنسدلة
    _selectedGovernorate = widget.admin?.governorate;
    _selectedNationality = widget.admin?.nationality ?? 'يمني';
    _selectedGender = widget.admin?.gender;
    _selectedDateOfBirth = widget.admin?.dateOfBirth;
    _existingImageUrl = widget.admin?.profileImageUrl;
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    _nationalIdController.dispose();
    super.dispose();
  }

  /// ===== دالة اختيار الصورة =====
  Future<void> _pickImage() async {
    try {
      if (kIsWeb) {
        final result = await FilePicker.platform.pickFiles(
          type: FileType.image,
          allowMultiple: false,
        );

        if (result != null && result.files.isNotEmpty) {
          final file = result.files.first;
          setState(() {
            _imageBytes = file.bytes;
            _imageFile = null;
          });
        }
      } else {
        final ImagePicker picker = ImagePicker();
        final XFile? image = await picker.pickImage(
          source: ImageSource.gallery,
          maxWidth: 800,
          maxHeight: 800,
          imageQuality: 85,
        );

        if (image != null) {
          setState(() {
            _imageFile = File(image.path);
            _imageBytes = null;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar(context, 'فشل في اختيار الصورة: $e');
      }
    }
  }

  /// ===== دالة اختيار تاريخ الميلاد =====
  Future<void> _selectDateOfBirth() async {
    try {
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: _selectedDateOfBirth ?? DateTime(1980),
        firstDate: DateTime(1950),
        lastDate: DateTime.now().subtract(const Duration(days: 365 * 18)),
        locale: const Locale('ar', 'YE'),
        helpText: 'اختر تاريخ الميلاد',
        cancelText: 'إلغاء',
        confirmText: 'تأكيد',
        fieldLabelText: 'تاريخ الميلاد',
        fieldHintText: 'يوم/شهر/سنة',
      );

      if (picked != null && picked != _selectedDateOfBirth) {
        setState(() {
          _selectedDateOfBirth = picked;
        });
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar(context, 'فشل في اختيار التاريخ: $e');
      }
    }
  }

  /// ===== دالة الإرسال المحدثة =====
  Future<void> _submit() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);

      try {
        final firebaseService = ref.read(firebaseServiceProvider);

        if (_isEditMode) {
          // ===== وضع التعديل - استخدام الدالة المحسنة =====
          await firebaseService.updateAdminWithFullData(
            adminId: widget.admin!.id,
            name: _nameController.text.trim(),
            phoneNumber:
                _phoneController.text.trim().isEmpty
                    ? null
                    : _phoneController.text.trim(),
            address:
                _addressController.text.trim().isEmpty
                    ? null
                    : _addressController.text.trim(),
            governorate: _selectedGovernorate,
            nationality: _selectedNationality,
            nationalId:
                _nationalIdController.text.trim().isEmpty
                    ? null
                    : _nationalIdController.text.trim(),
            gender: _selectedGender,
            dateOfBirth: _selectedDateOfBirth,
            profileImage: _imageFile ?? _imageBytes,
          );
          if (mounted) {
            showSuccessSnackBar(context, 'تم تحديث بيانات المسؤول بنجاح.');
          }
        } else {
          // ===== وضع الإضافة - استخدام الدالة المحسنة =====
          await firebaseService.addAdminWithFullData(
            name: _nameController.text.trim(),
            email: _emailController.text.trim(),
            password: _passwordController.text,
            phoneNumber:
                _phoneController.text.trim().isEmpty
                    ? null
                    : _phoneController.text.trim(),
            address:
                _addressController.text.trim().isEmpty
                    ? null
                    : _addressController.text.trim(),
            governorate: _selectedGovernorate,
            nationality: _selectedNationality ?? 'يمني',
            nationalId:
                _nationalIdController.text.trim().isEmpty
                    ? null
                    : _nationalIdController.text.trim(),
            gender: _selectedGender,
            dateOfBirth: _selectedDateOfBirth,
            profileImage: _imageFile ?? _imageBytes,
          );
          if (mounted) {
            showSuccessSnackBar(context, 'تمت إضافة المسؤول بنجاح.');
          }
        }

        if (mounted) {
          Navigator.of(context).pop();
        }
      } catch (e) {
        if (mounted) {
          showErrorSnackBar(context, 'حدث خطأ: ${e.toString()}');
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.85,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // ===== العنوان =====
            Row(
              children: [
                Icon(
                  Icons.admin_panel_settings,
                  color: Theme.of(context).primaryColor,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _isEditMode ? 'تعديل بيانات المسؤول' : 'إضافة مسؤول جديد',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                  tooltip: 'إغلاق',
                ),
              ],
            ),

            const SizedBox(height: 20),
            const Divider(),
            const SizedBox(height: 16),

            // ===== شريط التبويبات =====
            TabBar(
              controller: _tabController,
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Theme.of(context).primaryColor,
              tabs: const [
                Tab(icon: Icon(Icons.person), text: 'المعلومات الأساسية'),
                Tab(icon: Icon(Icons.contact_page), text: 'المعلومات الشخصية'),
                Tab(icon: Icon(Icons.photo_camera), text: 'الصورة الشخصية'),
              ],
            ),

            const SizedBox(height: 20),

            // ===== محتوى التبويبات =====
            Expanded(
              child: Form(
                key: _formKey,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildBasicInfoTab(),
                    _buildPersonalInfoTab(),
                    _buildImageTab(),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),
            const Divider(),

            // ===== أزرار الإجراءات =====
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _isLoading ? null : _submit,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                  child:
                      _isLoading
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : Text(
                            _isEditMode ? 'حفظ التعديلات' : 'إضافة المسؤول',
                          ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// ===== تبويب المعلومات الأساسية =====
  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ===== الاسم الكامل =====
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'الاسم الكامل *',
              prefixIcon: Icon(Icons.person),
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'الرجاء إدخال الاسم الكامل';
              }
              if (value.trim().length < 3) {
                return 'الاسم يجب أن يكون 3 أحرف على الأقل';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // ===== البريد الإلكتروني =====
          if (!_isEditMode) ...[
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني *',
                prefixIcon: Icon(Icons.email),
                border: OutlineInputBorder(),
                helperText: 'سيتم استخدامه لتسجيل الدخول',
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'الرجاء إدخال البريد الإلكتروني';
                }
                if (!RegExp(
                  r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                ).hasMatch(value)) {
                  return 'الرجاء إدخال بريد إلكتروني صالح';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            // ===== كلمة المرور =====
            TextFormField(
              controller: _passwordController,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور *',
                prefixIcon: Icon(Icons.lock),
                border: OutlineInputBorder(),
                helperText: 'يجب أن تكون 6 أحرف على الأقل',
              ),
              obscureText: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'الرجاء إدخال كلمة المرور';
                }
                if (value.length < 6) {
                  return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),
          ],

          // ===== ملاحظة للتعديل =====
          if (_isEditMode)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                border: Border.all(color: Colors.orange.shade200),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.orange.shade600),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'تعديل البريد الإلكتروني وكلمة المرور يتطلب إجراءات أمان إضافية.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// ===== تبويب المعلومات الشخصية =====
  Widget _buildPersonalInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ===== رقم الهاتف =====
          TextFormField(
            controller: _phoneController,
            decoration: const InputDecoration(
              labelText: 'رقم الهاتف',
              prefixIcon: Icon(Icons.phone),
              border: OutlineInputBorder(),
              helperText: 'مثال: 777123456',
            ),
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                if (!RegExp(r'^(77|73|70|71)\d{7}$').hasMatch(value)) {
                  return 'رقم الهاتف غير صالح (يجب أن يبدأ بـ 77، 73، 70، أو 71)';
                }
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // ===== تاريخ الميلاد =====
          InkWell(
            onTap: _selectDateOfBirth,
            child: InputDecorator(
              decoration: const InputDecoration(
                labelText: 'تاريخ الميلاد',
                prefixIcon: Icon(Icons.cake),
                border: OutlineInputBorder(),
                helperText: 'اضغط لاختيار التاريخ',
              ),
              child: Text(
                _selectedDateOfBirth != null
                    ? '${_selectedDateOfBirth!.day}/${_selectedDateOfBirth!.month}/${_selectedDateOfBirth!.year}'
                    : 'لم يتم اختيار تاريخ',
                style: TextStyle(
                  color:
                      _selectedDateOfBirth != null
                          ? Colors.black87
                          : Colors.grey.shade600,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // ===== الجنس =====
          DropdownButtonFormField<String>(
            value: _selectedGender,
            decoration: const InputDecoration(
              labelText: 'الجنس',
              prefixIcon: Icon(Icons.wc),
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: 'ذكر', child: Text('ذكر')),
              DropdownMenuItem(value: 'أنثى', child: Text('أنثى')),
            ],
            onChanged: (value) {
              setState(() {
                _selectedGender = value;
              });
            },
          ),

          const SizedBox(height: 16),

          // ===== المحافظة =====
          DropdownButtonFormField<String>(
            value: _selectedGovernorate,
            decoration: const InputDecoration(
              labelText: 'المحافظة',
              prefixIcon: Icon(Icons.location_city),
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: 'صنعاء', child: Text('صنعاء')),
              DropdownMenuItem(value: 'عدن', child: Text('عدن')),
              DropdownMenuItem(value: 'تعز', child: Text('تعز')),
              DropdownMenuItem(value: 'الحديدة', child: Text('الحديدة')),
              DropdownMenuItem(value: 'إب', child: Text('إب')),
              DropdownMenuItem(value: 'ذمار', child: Text('ذمار')),
              DropdownMenuItem(value: 'حجة', child: Text('حجة')),
              DropdownMenuItem(value: 'صعدة', child: Text('صعدة')),
              DropdownMenuItem(value: 'عمران', child: Text('عمران')),
              DropdownMenuItem(value: 'البيضاء', child: Text('البيضاء')),
              DropdownMenuItem(value: 'الجوف', child: Text('الجوف')),
              DropdownMenuItem(value: 'مأرب', child: Text('مأرب')),
              DropdownMenuItem(value: 'المحويت', child: Text('المحويت')),
              DropdownMenuItem(value: 'حضرموت', child: Text('حضرموت')),
              DropdownMenuItem(value: 'أبين', child: Text('أبين')),
              DropdownMenuItem(value: 'شبوة', child: Text('شبوة')),
              DropdownMenuItem(value: 'المهرة', child: Text('المهرة')),
              DropdownMenuItem(value: 'لحج', child: Text('لحج')),
              DropdownMenuItem(value: 'الضالع', child: Text('الضالع')),
              DropdownMenuItem(value: 'ريمة', child: Text('ريمة')),
              DropdownMenuItem(value: 'سقطرى', child: Text('سقطرى')),
              DropdownMenuItem(
                value: 'أمانة العاصمة',
                child: Text('أمانة العاصمة'),
              ),
            ],
            onChanged: (value) {
              setState(() {
                _selectedGovernorate = value;
              });
            },
          ),

          const SizedBox(height: 16),

          // ===== الجنسية =====
          DropdownButtonFormField<String>(
            value: _selectedNationality,
            decoration: const InputDecoration(
              labelText: 'الجنسية',
              prefixIcon: Icon(Icons.flag),
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: 'يمني', child: Text('يمني')),
              DropdownMenuItem(value: 'سعودي', child: Text('سعودي')),
              DropdownMenuItem(value: 'عماني', child: Text('عماني')),
              DropdownMenuItem(value: 'إماراتي', child: Text('إماراتي')),
              DropdownMenuItem(value: 'كويتي', child: Text('كويتي')),
              DropdownMenuItem(value: 'قطري', child: Text('قطري')),
              DropdownMenuItem(value: 'بحريني', child: Text('بحريني')),
              DropdownMenuItem(value: 'مصري', child: Text('مصري')),
              DropdownMenuItem(value: 'سوداني', child: Text('سوداني')),
              DropdownMenuItem(value: 'أردني', child: Text('أردني')),
              DropdownMenuItem(value: 'لبناني', child: Text('لبناني')),
              DropdownMenuItem(value: 'سوري', child: Text('سوري')),
              DropdownMenuItem(value: 'عراقي', child: Text('عراقي')),
              DropdownMenuItem(value: 'فلسطيني', child: Text('فلسطيني')),
              DropdownMenuItem(value: 'أخرى', child: Text('أخرى')),
            ],
            onChanged: (value) {
              setState(() {
                _selectedNationality = value;
              });
            },
          ),

          const SizedBox(height: 16),

          // ===== العنوان =====
          TextFormField(
            controller: _addressController,
            decoration: const InputDecoration(
              labelText: 'العنوان',
              prefixIcon: Icon(Icons.location_on),
              border: OutlineInputBorder(),
              helperText: 'العنوان التفصيلي',
            ),
            maxLines: 2,
          ),

          const SizedBox(height: 16),

          // ===== الرقم الوطني =====
          TextFormField(
            controller: _nationalIdController,
            decoration: const InputDecoration(
              labelText: 'الرقم الوطني',
              prefixIcon: Icon(Icons.credit_card),
              border: OutlineInputBorder(),
              helperText: 'الرقم الوطني اليمني (10 أرقام)',
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value != null && value.isNotEmpty) {
                if (!RegExp(r'^\d{10}$').hasMatch(value)) {
                  return 'الرقم الوطني يجب أن يكون 10 أرقام';
                }
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  /// ===== تبويب الصورة الشخصية =====
  Widget _buildImageTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // ===== عرض الصورة الحالية =====
          Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300, width: 2),
              borderRadius: BorderRadius.circular(100),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(98),
              child: _buildImageWidget(),
            ),
          ),

          const SizedBox(height: 24),

          // ===== زر اختيار الصورة =====
          ElevatedButton.icon(
            onPressed: _pickImage,
            icon: const Icon(Icons.photo_camera),
            label: const Text('اختيار صورة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),

          const SizedBox(height: 16),

          // ===== معلومات الصورة =====
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              border: Border.all(color: Colors.blue.shade200),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue.shade600),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'معلومات الصورة',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '• الحد الأقصى لحجم الصورة: 5 ميجابايت\n'
                  '• الصيغ المدعومة: JPG, PNG, GIF\n'
                  '• يُفضل أن تكون الصورة مربعة الشكل\n'
                  '• سيتم تغيير حجم الصورة تلقائياً',
                  style: TextStyle(fontSize: 12, color: Colors.blue.shade600),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// ===== بناء عنصر الصورة =====
  Widget _buildImageWidget() {
    if (_imageFile != null) {
      // صورة جديدة من الملف
      return Image.file(
        _imageFile!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultAvatar();
        },
      );
    } else if (_imageBytes != null) {
      // صورة جديدة من البايتات (للويب)
      return Image.memory(
        _imageBytes!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultAvatar();
        },
      );
    } else if (_existingImageUrl != null && _existingImageUrl!.isNotEmpty) {
      // صورة موجودة من الرابط
      return Image.network(
        _existingImageUrl!,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Center(
            child: CircularProgressIndicator(
              value:
                  loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultAvatar();
        },
      );
    } else {
      // لا توجد صورة - عرض الأيقونة الافتراضية
      return _buildDefaultAvatar();
    }
  }

  /// ===== الأيقونة الافتراضية =====
  Widget _buildDefaultAvatar() {
    return Container(
      color: Colors.grey.shade200,
      child: Icon(
        Icons.admin_panel_settings,
        size: 80,
        color: Colors.grey.shade400,
      ),
    );
  }
}
