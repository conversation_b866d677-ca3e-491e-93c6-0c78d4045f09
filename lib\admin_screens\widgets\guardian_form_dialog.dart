import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:school_management_system/models/guardian_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/utils/helpers.dart';
import 'package:school_management_system/constants/yemen_data_constants.dart';

/// ===== نموذج إضافة وتعديل أولياء الأمور المحدث =====
///
/// هذا النموذج يوفر واجهة شاملة لإضافة وتعديل بيانات أولياء الأمور
/// يشمل جميع الحقول الجديدة المطلوبة للمدارس اليمنية والعربية
///
/// المميزات:
/// - نموذج متعدد التبويبات لتنظيم الحقول
/// - رفع الصور الشخصية
/// - التحقق من صحة البيانات
/// - دعم القوائم المنسدلة للبيانات الثابتة
/// - تعليقات توضيحية شاملة باللغة العربية
///
/// الاستخدام:
/// - للإضافة: GuardianFormDialog()
/// - للتعديل: GuardianFormDialog(guardian: existingGuardian)
class GuardianFormDialog extends ConsumerStatefulWidget {
  /// ولي الأمر المراد تعديله (null في حالة الإضافة)
  final GuardianModel? guardian;

  const GuardianFormDialog({super.key, this.guardian});

  @override
  ConsumerState<GuardianFormDialog> createState() => _GuardianFormDialogState();
}

class _GuardianFormDialogState extends ConsumerState<GuardianFormDialog>
    with TickerProviderStateMixin {
  // ===== مفاتيح النموذج والتحكم في التبويبات =====
  final _formKey = GlobalKey<FormState>();
  late TabController _tabController;

  // ===== متحكمات النصوص للحقول الأساسية =====
  late final TextEditingController _nameController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;
  late final TextEditingController _passwordController;

  // ===== متحكمات النصوص للحقول الإضافية =====
  late final TextEditingController _alternativePhoneController;
  late final TextEditingController _addressController;
  late final TextEditingController _nationalIdController;
  late final TextEditingController _occupationController;
  late final TextEditingController _workplaceController;
  late final TextEditingController _notesController;

  // ===== متغيرات الاختيار من القوائم المنسدلة =====
  String? _selectedGender;
  String? _selectedGovernorate;
  String? _selectedNationality;
  String? _selectedEducationLevel;
  String? _selectedMaritalStatus;
  String? _selectedBloodType;
  String? _selectedHealthCondition;
  DateTime? _selectedDateOfBirth;

  // ===== متغيرات إدارة الصور =====
  Uint8List? _imageBytes; // للويب
  File? _imageFile; // للموبايل
  String? _existingImageUrl; // للصورة الموجودة مسبقاً

  // ===== متغيرات حالة النموذج =====
  bool _isLoading = false;
  int _currentTabIndex = 0;

  /// التحقق من وضع التعديل
  bool get _isEditMode => widget.guardian != null;

  @override
  void initState() {
    super.initState();

    // ===== إعداد التبويبات =====
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentTabIndex = _tabController.index;
      });
    });

    // ===== تهيئة متحكمات النصوص مع البيانات الموجودة =====
    _initializeControllers();

    // ===== تهيئة القيم المختارة من القوائم المنسدلة =====
    _initializeDropdownValues();

    // ===== تهيئة الصورة الموجودة =====
    _existingImageUrl = widget.guardian?.profileImageUrl;
  }

  /// ===== تهيئة متحكمات النصوص =====
  void _initializeControllers() {
    // الحقول الأساسية
    _nameController = TextEditingController(text: widget.guardian?.name ?? '');
    _emailController = TextEditingController(
      text: widget.guardian?.email ?? '',
    );
    _phoneController = TextEditingController(
      text: widget.guardian?.phoneNumber ?? '',
    );
    _passwordController = TextEditingController();

    // الحقول الإضافية
    _alternativePhoneController = TextEditingController(
      text: widget.guardian?.alternativePhoneNumber ?? '',
    );
    _addressController = TextEditingController(
      text: widget.guardian?.address ?? '',
    );
    _nationalIdController = TextEditingController(
      text: widget.guardian?.nationalId ?? '',
    );
    _occupationController = TextEditingController(
      text: widget.guardian?.occupation ?? '',
    );
    _workplaceController = TextEditingController(
      text: widget.guardian?.workplace ?? '',
    );
    _notesController = TextEditingController(
      text: widget.guardian?.notes ?? '',
    );
  }

  /// ===== تهيئة القيم المختارة من القوائم المنسدلة =====
  void _initializeDropdownValues() {
    _selectedGender = widget.guardian?.gender;
    _selectedGovernorate = widget.guardian?.governorate;
    _selectedNationality = widget.guardian?.nationality;
    _selectedEducationLevel = widget.guardian?.educationLevel;
    _selectedMaritalStatus = widget.guardian?.maritalStatus;
    _selectedBloodType = widget.guardian?.bloodType;
    _selectedHealthCondition = widget.guardian?.healthCondition;
    _selectedDateOfBirth = widget.guardian?.dateOfBirth;
  }

  @override
  void dispose() {
    // تنظيف متحكمات النصوص
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _alternativePhoneController.dispose();
    _addressController.dispose();
    _nationalIdController.dispose();
    _occupationController.dispose();
    _workplaceController.dispose();
    _notesController.dispose();

    // تنظيف متحكم التبويبات
    _tabController.dispose();

    super.dispose();
  }

  /// ===== دالة اختيار الصورة الشخصية =====
  Future<void> _pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null) {
        if (kIsWeb) {
          // للويب: قراءة البيانات كـ bytes
          final bytes = await image.readAsBytes();
          setState(() {
            _imageBytes = bytes;
            _imageFile = null;
          });
        } else {
          // للموبايل: استخدام File
          setState(() {
            _imageFile = File(image.path);
            _imageBytes = null;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar(context, 'خطأ في اختيار الصورة: ${e.toString()}');
      }
    }
  }

  /// ===== دالة اختيار تاريخ الميلاد =====
  Future<void> _selectDateOfBirth() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDateOfBirth ?? DateTime(1980),
      firstDate: DateTime(1940),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
      helpText: 'اختر تاريخ الميلاد',
      cancelText: 'إلغاء',
      confirmText: 'تأكيد',
    );

    if (picked != null && picked != _selectedDateOfBirth) {
      setState(() {
        _selectedDateOfBirth = picked;
      });
    }
  }

  /// ===== دالة حفظ البيانات =====
  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من الحقول المطلوبة
    if (_selectedGovernorate == null || _selectedGovernorate!.isEmpty) {
      if (mounted) {
        showErrorSnackBar(context, 'يرجى اختيار المحافظة');
      }
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final firebaseService = ref.read(firebaseServiceProvider);

      // تحضير الصورة للرفع (إذا كانت موجودة)
      dynamic imageToUpload;
      if (_imageBytes != null) {
        imageToUpload = _imageBytes;
      } else if (_imageFile != null) {
        imageToUpload = _imageFile;
      }

      if (_isEditMode) {
        // ===== وضع التعديل =====
        // ===== استخدام الدالة المحدثة مع جميع الحقول =====
        await firebaseService.updateGuardianWithAllFields(
          widget.guardian!.id,
          // المعلومات الأساسية
          name: _nameController.text.trim(),
          phoneNumber: _phoneController.text.trim(),
          governorate: _selectedGovernorate!,

          // المعلومات الإضافية
          alternativePhoneNumber:
              _alternativePhoneController.text.trim().isNotEmpty
                  ? _alternativePhoneController.text.trim()
                  : null,
          gender: _selectedGender,
          dateOfBirth: _selectedDateOfBirth,
          address:
              _addressController.text.trim().isNotEmpty
                  ? _addressController.text.trim()
                  : null,
          nationality: _selectedNationality ?? 'يمني',
          nationalId:
              _nationalIdController.text.trim().isNotEmpty
                  ? _nationalIdController.text.trim()
                  : null,
          occupation:
              _occupationController.text.trim().isNotEmpty
                  ? _occupationController.text.trim()
                  : null,
          workplace:
              _workplaceController.text.trim().isNotEmpty
                  ? _workplaceController.text.trim()
                  : null,
          educationLevel: _selectedEducationLevel,
          maritalStatus: _selectedMaritalStatus,
          bloodType: _selectedBloodType,
          healthCondition: _selectedHealthCondition,
          notes:
              _notesController.text.trim().isNotEmpty
                  ? _notesController.text.trim()
                  : null,
          profileImage: imageToUpload,
        );

        if (mounted) {
          showSuccessSnackBar(context, 'تم تحديث بيانات ولي الأمر بنجاح');
        }
      } else {
        // ===== وضع الإضافة =====
        // ===== استخدام الدالة المحدثة مع جميع الحقول =====
        await firebaseService.addGuardianWithAllFields(
          // المعلومات الأساسية المطلوبة
          name: _nameController.text.trim(),
          email: _emailController.text.trim(),
          phoneNumber: _phoneController.text.trim(),
          password: _passwordController.text,
          governorate: _selectedGovernorate!,

          // المعلومات الإضافية
          alternativePhoneNumber:
              _alternativePhoneController.text.trim().isNotEmpty
                  ? _alternativePhoneController.text.trim()
                  : null,
          gender: _selectedGender,
          dateOfBirth: _selectedDateOfBirth,
          address:
              _addressController.text.trim().isNotEmpty
                  ? _addressController.text.trim()
                  : null,
          nationality: _selectedNationality ?? 'يمني',
          nationalId:
              _nationalIdController.text.trim().isNotEmpty
                  ? _nationalIdController.text.trim()
                  : null,
          occupation:
              _occupationController.text.trim().isNotEmpty
                  ? _occupationController.text.trim()
                  : null,
          workplace:
              _workplaceController.text.trim().isNotEmpty
                  ? _workplaceController.text.trim()
                  : null,
          educationLevel: _selectedEducationLevel,
          maritalStatus: _selectedMaritalStatus,
          bloodType: _selectedBloodType,
          healthCondition: _selectedHealthCondition,
          notes:
              _notesController.text.trim().isNotEmpty
                  ? _notesController.text.trim()
                  : null,
          profileImage: imageToUpload,
        );

        if (mounted) {
          showSuccessSnackBar(context, 'تمت إضافة ولي الأمر بنجاح');
        }
      }

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        showErrorSnackBar(context, 'حدث خطأ: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.9,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // ===== عنوان النموذج =====
            Row(
              children: [
                Icon(
                  _isEditMode ? Icons.edit : Icons.person_add,
                  color: Theme.of(context).primaryColor,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _isEditMode
                        ? 'تعديل بيانات ولي الأمر'
                        : 'إضافة ولي أمر جديد',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // ===== شريط التبويبات =====
            TabBar(
              controller: _tabController,
              isScrollable: true,
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Theme.of(context).primaryColor,
              tabs: const [
                Tab(icon: Icon(Icons.person), text: 'المعلومات الأساسية'),
                Tab(icon: Icon(Icons.contact_phone), text: 'معلومات الاتصال'),
                Tab(icon: Icon(Icons.info), text: 'المعلومات الشخصية'),
                Tab(icon: Icon(Icons.note), text: 'معلومات إضافية'),
              ],
            ),

            const SizedBox(height: 16),

            // ===== محتوى التبويبات =====
            Expanded(
              child: Form(
                key: _formKey,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildBasicInfoTab(),
                    _buildContactInfoTab(),
                    _buildPersonalInfoTab(),
                    _buildAdditionalInfoTab(),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // ===== أزرار الإجراءات =====
            Row(
              children: [
                // مؤشر التقدم
                Expanded(
                  child: LinearProgressIndicator(
                    value: (_currentTabIndex + 1) / 4,
                    backgroundColor: Colors.grey.shade300,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // أزرار التنقل والحفظ
                if (_currentTabIndex > 0)
                  TextButton.icon(
                    onPressed: () {
                      _tabController.animateTo(_currentTabIndex - 1);
                    },
                    icon: const Icon(Icons.arrow_back),
                    label: const Text('السابق'),
                  ),

                const SizedBox(width: 8),

                if (_currentTabIndex < 3)
                  ElevatedButton.icon(
                    onPressed: () {
                      _tabController.animateTo(_currentTabIndex + 1);
                    },
                    icon: const Icon(Icons.arrow_forward),
                    label: const Text('التالي'),
                  )
                else
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _submit,
                    icon:
                        _isLoading
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : const Icon(Icons.save),
                    label: Text(_isLoading ? 'جاري الحفظ...' : 'حفظ'),
                  ),

                const SizedBox(width: 8),

                TextButton(
                  onPressed:
                      _isLoading ? null : () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// ===== بناء تبويب المعلومات الأساسية =====
  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ===== عنوان القسم =====
          Row(
            children: [
              Icon(Icons.person, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(
                'المعلومات الأساسية المطلوبة',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'هذه المعلومات مطلوبة لإنشاء حساب ولي الأمر',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
          ),
          const SizedBox(height: 24),

          // ===== الصورة الشخصية =====
          Center(
            child: Column(
              children: [
                GestureDetector(
                  onTap: _pickImage,
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Theme.of(context).primaryColor,
                        width: 3,
                      ),
                      color: Colors.grey.shade100,
                    ),
                    child: _buildImageWidget(),
                  ),
                ),
                const SizedBox(height: 12),
                TextButton.icon(
                  onPressed: _pickImage,
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('اختيار صورة شخصية'),
                ),
                Text(
                  'اختياري - يساعد في التعرف على ولي الأمر',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),

          // ===== الاسم الكامل =====
          TextFormField(
            controller: _nameController,
            decoration: InputDecoration(
              labelText: 'الاسم الكامل *',
              hintText: 'الاسم الأول والأخير واسم الأب',
              prefixIcon: const Icon(Icons.person_outline),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText: 'يجب أن يحتوي على الاسم الأول والأخير على الأقل',
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'الاسم الكامل مطلوب';
              }
              if (value.trim().split(' ').length < 2) {
                return 'يرجى إدخال الاسم الأول والأخير على الأقل';
              }
              return null;
            },
          ),

          const SizedBox(height: 20),

          // ===== البريد الإلكتروني (للإضافة فقط) =====
          if (!_isEditMode) ...[
            TextFormField(
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: InputDecoration(
                labelText: 'البريد الإلكتروني *',
                hintText: 'البريد الإلكتروني الشخصي',
                prefixIcon: const Icon(Icons.email_outlined),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                helperText: 'سيستخدم لتسجيل الدخول والتواصل',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'البريد الإلكتروني مطلوب';
                }
                if (!RegExp(
                  r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                ).hasMatch(value)) {
                  return 'يرجى إدخال بريد إلكتروني صحيح';
                }
                return null;
              },
            ),
            const SizedBox(height: 20),
          ],

          // ===== رقم الهاتف الأساسي =====
          TextFormField(
            controller: _phoneController,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              labelText: 'رقم الهاتف الأساسي *',
              hintText: 'رقم الهاتف المحمول',
              prefixIcon: const Icon(Icons.phone_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText: 'يستخدم للتواصل السريع والطوارئ',
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'رقم الهاتف مطلوب';
              }
              // التحقق من صحة رقم الهاتف اليمني
              final phoneRegex = RegExp(r'^(\+967|967|0)?[7][0-9]{8}$');
              if (!phoneRegex.hasMatch(value.replaceAll(' ', ''))) {
                return 'يرجى إدخال رقم هاتف يمني صحيح';
              }
              return null;
            },
          ),

          const SizedBox(height: 20),

          // ===== كلمة المرور (للإضافة فقط) =====
          if (!_isEditMode) ...[
            TextFormField(
              controller: _passwordController,
              obscureText: true,
              decoration: InputDecoration(
                labelText: 'كلمة المرور *',
                hintText: 'يجب أن تكون 6 أحرف على الأقل',
                prefixIcon: const Icon(Icons.lock_outline),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                helperText: 'ستستخدم لتسجيل الدخول إلى النظام',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'كلمة المرور مطلوبة';
                }
                if (value.length < 6) {
                  return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                }
                return null;
              },
            ),
            const SizedBox(height: 20),
          ],

          // ===== ملاحظة مهمة =====
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue.shade700),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'ملاحظة مهمة',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'الحقول المميزة بـ (*) مطلوبة. يمكنك إضافة المزيد من المعلومات في التبويبات التالية.',
                        style: TextStyle(color: Colors.blue.shade700),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// ===== بناء widget الصورة =====
  Widget _buildImageWidget() {
    if (_imageBytes != null) {
      // عرض الصورة الجديدة (للويب)
      return ClipOval(
        child: Image.memory(
          _imageBytes!,
          width: 114,
          height: 114,
          fit: BoxFit.cover,
        ),
      );
    } else if (_imageFile != null) {
      // عرض الصورة الجديدة (للموبايل)
      return ClipOval(
        child: Image.file(
          _imageFile!,
          width: 114,
          height: 114,
          fit: BoxFit.cover,
        ),
      );
    } else if (_existingImageUrl != null && _existingImageUrl!.isNotEmpty) {
      // عرض الصورة الموجودة مسبقاً
      return ClipOval(
        child: Image.network(
          _existingImageUrl!,
          width: 114,
          height: 114,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return const Icon(Icons.person, size: 60, color: Colors.grey);
          },
        ),
      );
    } else {
      // عرض أيقونة افتراضية
      return const Icon(Icons.person, size: 60, color: Colors.grey);
    }
  }

  /// ===== بناء تبويب معلومات الاتصال =====
  Widget _buildContactInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ===== عنوان القسم =====
          Row(
            children: [
              Icon(Icons.contact_phone, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(
                'معلومات الاتصال والعنوان',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'معلومات إضافية للتواصل مع ولي الأمر',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
          ),
          const SizedBox(height: 24),

          // ===== رقم الهاتف البديل =====
          TextFormField(
            controller: _alternativePhoneController,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              labelText: 'رقم الهاتف البديل',
              hintText: 'رقم هاتف إضافي للتواصل',
              prefixIcon: const Icon(Icons.phone_callback_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText:
                  'اختياري - رقم إضافي للتواصل في حالة عدم الوصول للرقم الأساسي',
            ),
            validator: (value) {
              if (value != null && value.trim().isNotEmpty) {
                // التحقق من صحة الرقم إذا تم إدخاله
                if (value.trim().length < 6) {
                  return 'رقم الهاتف قصير جداً';
                }
              }
              return null;
            },
          ),

          const SizedBox(height: 20),

          // ===== العنوان الكامل =====
          TextFormField(
            controller: _addressController,
            maxLines: 3,
            decoration: InputDecoration(
              labelText: 'العنوان الكامل',
              hintText: 'الحي، الشارع، رقم المنزل',
              prefixIcon: const Icon(Icons.location_on_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText:
                  'اختياري - العنوان الكامل للمنزل (الحي، الشارع، رقم المنزل)',
            ),
          ),

          const SizedBox(height: 20),

          // ===== المحافظة =====
          DropdownButtonFormField<String>(
            value: _selectedGovernorate,
            decoration: InputDecoration(
              labelText: 'المحافظة *',
              prefixIcon: const Icon(Icons.location_city_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText: 'مطلوب - المحافظة التي يسكن فيها ولي الأمر',
            ),
            items:
                YemenDataConstants.yemenGovernorates.map((governorate) {
                  return DropdownMenuItem<String>(
                    value: governorate,
                    child: Text(governorate),
                  );
                }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedGovernorate = value;
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى اختيار المحافظة';
              }
              return null;
            },
          ),

          const SizedBox(height: 20),

          // ===== الجنسية =====
          DropdownButtonFormField<String>(
            value: _selectedNationality,
            decoration: InputDecoration(
              labelText: 'الجنسية',
              prefixIcon: const Icon(Icons.flag_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText: 'افتراضي: يمني - يمكن تغييرها للمقيمين الأجانب',
            ),
            items:
                YemenDataConstants.nationalities.map((nationality) {
                  return DropdownMenuItem<String>(
                    value: nationality,
                    child: Text(nationality),
                  );
                }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedNationality = value;
              });
            },
          ),

          const SizedBox(height: 32),

          // ===== ملاحظة توضيحية =====
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.green.shade700),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'معلومات مهمة',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'المحافظة مطلوبة للإحصائيات الجغرافية. باقي المعلومات اختيارية ولكنها تساعد في التواصل الفعال.',
                        style: TextStyle(color: Colors.green.shade700),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// ===== بناء تبويب المعلومات الشخصية =====
  Widget _buildPersonalInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ===== عنوان القسم =====
          Row(
            children: [
              Icon(Icons.info, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(
                'المعلومات الشخصية',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'معلومات شخصية إضافية عن ولي الأمر',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
          ),
          const SizedBox(height: 24),

          // ===== الجنس =====
          DropdownButtonFormField<String>(
            value: _selectedGender,
            decoration: InputDecoration(
              labelText: 'الجنس',
              prefixIcon: const Icon(Icons.person_outline),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText: 'اختياري - يساعد في التواصل المناسب',
            ),
            items:
                YemenDataConstants.genders.map((gender) {
                  return DropdownMenuItem<String>(
                    value: gender,
                    child: Text(gender),
                  );
                }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedGender = value;
              });
            },
          ),

          const SizedBox(height: 20),

          // ===== تاريخ الميلاد =====
          InkWell(
            onTap: _selectDateOfBirth,
            child: InputDecorator(
              decoration: InputDecoration(
                labelText: 'تاريخ الميلاد',
                prefixIcon: const Icon(Icons.cake_outlined),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                helperText: 'اختياري - يستخدم لحساب العمر',
              ),
              child: Text(
                _selectedDateOfBirth != null
                    ? '${_selectedDateOfBirth!.day}/${_selectedDateOfBirth!.month}/${_selectedDateOfBirth!.year}'
                    : 'اضغط لاختيار تاريخ الميلاد',
                style: TextStyle(
                  color:
                      _selectedDateOfBirth != null
                          ? Colors.black87
                          : Colors.grey.shade600,
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // ===== الرقم الوطني =====
          TextFormField(
            controller: _nationalIdController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'الرقم الوطني',
              hintText: 'الرقم الوطني اليمني',
              prefixIcon: const Icon(Icons.credit_card_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText: 'اختياري - الرقم الوطني اليمني (10 أرقام)',
            ),
            validator: (value) {
              if (value != null && value.trim().isNotEmpty) {
                if (!YemenDataConstants.isValidYemeniNationalId(value)) {
                  return 'الرقم الوطني يجب أن يكون 10 أرقام';
                }
              }
              return null;
            },
          ),

          const SizedBox(height: 20),

          // ===== الحالة الاجتماعية =====
          DropdownButtonFormField<String>(
            value: _selectedMaritalStatus,
            decoration: InputDecoration(
              labelText: 'الحالة الاجتماعية',
              prefixIcon: const Icon(Icons.family_restroom_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText: 'اختياري - الحالة الاجتماعية الحالية',
            ),
            items: const [
              DropdownMenuItem(value: 'أعزب', child: Text('أعزب')),
              DropdownMenuItem(value: 'متزوج', child: Text('متزوج')),
              DropdownMenuItem(value: 'مطلق', child: Text('مطلق')),
              DropdownMenuItem(value: 'أرمل', child: Text('أرمل')),
            ],
            onChanged: (value) {
              setState(() {
                _selectedMaritalStatus = value;
              });
            },
          ),

          const SizedBox(height: 20),

          // ===== فصيلة الدم =====
          DropdownButtonFormField<String>(
            value: _selectedBloodType,
            decoration: InputDecoration(
              labelText: 'فصيلة الدم',
              prefixIcon: const Icon(Icons.bloodtype_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText: 'اختياري - مهم في حالات الطوارئ الطبية',
            ),
            items:
                YemenDataConstants.bloodTypes.map((bloodType) {
                  return DropdownMenuItem<String>(
                    value: bloodType,
                    child: Text(bloodType),
                  );
                }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedBloodType = value;
              });
            },
          ),

          const SizedBox(height: 20),

          // ===== الحالة الصحية =====
          DropdownButtonFormField<String>(
            value: _selectedHealthCondition,
            decoration: InputDecoration(
              labelText: 'الحالة الصحية',
              prefixIcon: const Icon(Icons.health_and_safety_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText: 'اختياري - أي حالات صحية مهمة',
            ),
            items:
                YemenDataConstants.commonHealthConditions.map((condition) {
                  return DropdownMenuItem<String>(
                    value: condition,
                    child: Text(condition),
                  );
                }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedHealthCondition = value;
              });
            },
          ),

          const SizedBox(height: 32),

          // ===== ملاحظة توضيحية =====
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.purple.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.purple.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.purple.shade700),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'خصوصية البيانات',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.purple.shade700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'جميع المعلومات الشخصية محمية ولن تُستخدم إلا لأغراض التواصل والطوارئ.',
                        style: TextStyle(color: Colors.purple.shade700),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// ===== بناء تبويب المعلومات الإضافية =====
  Widget _buildAdditionalInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // ===== عنوان القسم =====
          Row(
            children: [
              Icon(Icons.note, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(
                'المعلومات الإضافية',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'معلومات مهنية وإضافية عن ولي الأمر',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
          ),
          const SizedBox(height: 24),

          // ===== المهنة =====
          TextFormField(
            controller: _occupationController,
            decoration: InputDecoration(
              labelText: 'المهنة',
              hintText: 'المهنة أو الوظيفة',
              prefixIcon: const Icon(Icons.work_outline),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText: 'اختياري - مهنة أو وظيفة ولي الأمر',
            ),
          ),

          const SizedBox(height: 20),

          // ===== مكان العمل =====
          TextFormField(
            controller: _workplaceController,
            decoration: InputDecoration(
              labelText: 'مكان العمل',
              hintText: 'اسم الشركة أو المؤسسة',
              prefixIcon: const Icon(Icons.business_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText: 'اختياري - اسم الشركة أو المؤسسة',
            ),
          ),

          const SizedBox(height: 20),

          // ===== المستوى التعليمي =====
          DropdownButtonFormField<String>(
            value: _selectedEducationLevel,
            decoration: InputDecoration(
              labelText: 'المستوى التعليمي',
              prefixIcon: const Icon(Icons.school_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText: 'اختياري - أعلى مؤهل تعليمي حصل عليه',
            ),
            items: const [
              DropdownMenuItem(value: 'أمي', child: Text('أمي')),
              DropdownMenuItem(value: 'يقرأ ويكتب', child: Text('يقرأ ويكتب')),
              DropdownMenuItem(value: 'ابتدائي', child: Text('ابتدائي')),
              DropdownMenuItem(value: 'إعدادي', child: Text('إعدادي')),
              DropdownMenuItem(value: 'ثانوي', child: Text('ثانوي')),
              DropdownMenuItem(value: 'دبلوم', child: Text('دبلوم')),
              DropdownMenuItem(value: 'بكالوريوس', child: Text('بكالوريوس')),
              DropdownMenuItem(value: 'ماجستير', child: Text('ماجستير')),
              DropdownMenuItem(value: 'دكتوراه', child: Text('دكتوراه')),
            ],
            onChanged: (value) {
              setState(() {
                _selectedEducationLevel = value;
              });
            },
          ),

          const SizedBox(height: 20),

          // ===== ملاحظات خاصة =====
          TextFormField(
            controller: _notesController,
            maxLines: 4,
            decoration: InputDecoration(
              labelText: 'ملاحظات خاصة',
              hintText: 'معلومات إضافية مهمة',
              prefixIcon: const Icon(Icons.note_add_outlined),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              helperText: 'اختياري - أي معلومات إضافية تراها مهمة',
            ),
          ),

          const SizedBox(height: 32),

          // ===== ملخص المعلومات =====
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.summarize, color: Colors.orange.shade700),
                    const SizedBox(width: 8),
                    Text(
                      'ملخص البيانات',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                _buildSummaryRow('الاسم', _nameController.text),
                _buildSummaryRow('البريد الإلكتروني', _emailController.text),
                _buildSummaryRow('رقم الهاتف', _phoneController.text),
                _buildSummaryRow(
                  'المحافظة',
                  _selectedGovernorate ?? 'غير محدد',
                ),
                if (_selectedGender != null)
                  _buildSummaryRow('الجنس', _selectedGender!),
                if (_occupationController.text.isNotEmpty)
                  _buildSummaryRow('المهنة', _occupationController.text),
                if (_selectedEducationLevel != null)
                  _buildSummaryRow(
                    'المستوى التعليمي',
                    _selectedEducationLevel!,
                  ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // ===== تأكيد البيانات =====
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.check_circle_outline, color: Colors.green.shade700),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'جاهز للحفظ',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'تأكد من صحة جميع البيانات قبل الضغط على زر الحفظ.',
                        style: TextStyle(color: Colors.green.shade700),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// ===== بناء صف ملخص البيانات =====
  Widget _buildSummaryRow(String label, String value) {
    if (value.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
            ),
          ),
          Expanded(child: Text(value, style: const TextStyle(fontSize: 12))),
        ],
      ),
    );
  }
}
