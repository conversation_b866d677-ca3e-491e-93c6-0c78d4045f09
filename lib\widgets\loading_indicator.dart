import 'package:flutter/material.dart';
import 'package:school_management_system/shared/app_theme.dart';

/// مؤشر تحميل مخصص للتطبيق (محدث)
///
/// يطبق التصميم الموحد الجديد مع إمكانية إضافة رسالة
///
/// ملاحظة: يُنصح باستخدام EnhancedLoadingIndicator من enhanced_widgets.dart
/// للمشاريع الجديدة حيث توفر مرونة أكبر في التخصيص
class LoadingIndicator extends StatelessWidget {
  /// الرسالة المعروضة مع مؤشر التحميل (اختياري)
  final String? message;

  /// حجم مؤشر التحميل (افتراضي: 40)
  final double size;

  const LoadingIndicator({super.key, this.message, this.size = 40});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: const CircularProgressIndicator(
              color: AppColors.primary,
              strokeWidth: 3,
            ),
          ),
          if (message != null) ...[
            const SizedBox(height: AppSpacing.md),
            Text(
              message!,
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: AppTextSizes.bodyMedium,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
