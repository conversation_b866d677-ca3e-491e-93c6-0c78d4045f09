import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:school_management_system/mobile_screens/student_assignments_screen.dart';
import 'package:school_management_system/mobile_screens/student_attendance_screen.dart';
import 'package:school_management_system/mobile_screens/student_fees_screen.dart';
import 'package:school_management_system/mobile_screens/student_grades_screen.dart';
import 'package:school_management_system/mobile_screens/student_home_page.dart';
import 'package:school_management_system/mobile_screens/student_notes_screen.dart';
import 'package:school_management_system/mobile_screens/student_profile_screen.dart';
import 'package:school_management_system/mobile_screens/student_timetable_screen.dart';
import 'package:school_management_system/widgets/shared_app_drawer.dart';
import 'package:school_management_system/shared/app_theme.dart';
// استيراد الشاشات الجديدة للطلاب
import 'package:school_management_system/student_screens/exam_preparation_screen.dart';
import 'package:school_management_system/student_screens/student_results_screen.dart';
import 'package:school_management_system/student_screens/student_exam_schedule_screen.dart';

/// الواجهة الرئيسية المحسنة لتطبيق الطالب بعد تسجيل الدخول
///
/// تطبق التصميم الموحد الجديد مع تحسينات في:
/// - نظام الألوان والمسافات الموحد
/// - تصميم شريط التنقل السفلي المحسن
/// - تجربة مستخدم محسنة للطلاب
/// - دعم أفضل للشاشات المختلفة
class StudentMainNavigation extends StatefulWidget {
  const StudentMainNavigation({super.key});

  @override
  State<StudentMainNavigation> createState() => _StudentMainNavigationState();
}

class _StudentMainNavigationState extends State<StudentMainNavigation> {
  int _selectedIndex = 0;
  late final String _studentId;
  late final List<Widget> _pages;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    // جلب معرّف الطالب الحالي عند بناء الواجهة
    _studentId = FirebaseAuth.instance.currentUser!.uid;

    // بناء قائمة الصفحات مع تمرير معرّف الطالب
    _pages = <Widget>[
      StudentHomeScreen(studentId: _studentId),
      StudentAssignmentsScreen(studentId: _studentId),
      StudentAttendanceScreen(studentId: _studentId),
      StudentFeesScreen(studentId: _studentId),
      StudentGradesScreen(studentId: _studentId),
    ];
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  /// الحصول على عنوان الصفحة حسب الفهرس
  String _getPageTitle(int index) {
    const titles = [
      'الرئيسية',
      'الواجبات',
      'الحضور والغياب',
      'الرسوم الدراسية',
      'الدرجات',
    ];

    return index >= 0 && index < titles.length ? titles[index] : 'تطبيق الطالب';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          _getPageTitle(_selectedIndex),
          style: const TextStyle(
            fontSize: AppTextSizes.headlineMedium,
            fontWeight: FontWeight.bold,
            color: AppColors.textOnPrimary,
          ),
        ),
        backgroundColor: AppColors.studentColor,
        foregroundColor: AppColors.textOnPrimary,
        elevation: AppElevation.medium,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.menu),
          tooltip: 'القائمة',
          onPressed: () => _scaffoldKey.currentState?.openDrawer(),
        ),
        actions: [
          // زر الإشعارات
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            tooltip: 'الإشعارات',
            onPressed: () {
              // TODO: فتح صفحة الإشعارات
            },
          ),
        ],
      ),
      drawer: Drawer(
        backgroundColor: AppColors.surface,
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            // رأس القائمة المحسن
            DrawerHeader(
              decoration: const BoxDecoration(
                color: AppColors.studentColor,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(AppBorderRadius.large),
                  bottomRight: Radius.circular(AppBorderRadius.large),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // أيقونة الطالب
                  Container(
                    padding: const EdgeInsets.all(AppSpacing.sm),
                    decoration: BoxDecoration(
                      color: AppColors.textOnPrimary.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(
                        AppBorderRadius.medium,
                      ),
                    ),
                    child: const Icon(
                      Icons.school,
                      color: AppColors.textOnPrimary,
                      size: 32,
                    ),
                  ),
                  const SizedBox(height: AppSpacing.sm),

                  // عنوان القائمة
                  const Text(
                    'تطبيق الطالب',
                    style: TextStyle(
                      color: AppColors.textOnPrimary,
                      fontSize: AppTextSizes.titleLarge,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'الوصول السريع للخدمات',
                    style: TextStyle(
                      color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                      fontSize: AppTextSizes.bodyMedium,
                    ),
                  ),
                ],
              ),
            ),

            // فاصل
            const SizedBox(height: AppSpacing.sm),
            // الملف الشخصي
            _buildDrawerItem(
              icon: Icons.person_outline,
              selectedIcon: Icons.person,
              title: 'الملف الشخصي',
              subtitle: 'عرض وتعديل البيانات الشخصية',
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            StudentProfileScreen(studentId: _studentId),
                  ),
                );
              },
            ),

            // الجدول الدراسي
            _buildDrawerItem(
              icon: Icons.schedule_outlined,
              selectedIcon: Icons.schedule,
              title: 'الجدول الدراسي',
              subtitle: 'مواعيد الحصص والمواد',
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            StudentTimetableScreen(studentId: _studentId),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.note_alt_outlined),
              title: const Text('الملاحظات'),
              onTap: () {
                Navigator.pop(context); // Close the drawer
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => StudentNotesScreen(studentId: _studentId),
                  ),
                );
              },
            ),
            const Divider(),
            // الشاشات الجديدة للطلاب
            ListTile(
              leading: const Icon(Icons.quiz_outlined),
              title: const Text('الاستعداد للامتحان'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            ExamPreparationScreen(studentId: _studentId),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.assessment_outlined),
              title: const Text('نتائجي'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            StudentResultsScreen(studentId: _studentId),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.calendar_today_outlined),
              title: const Text('جدول امتحاناتي'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            StudentExamScheduleScreen(studentId: _studentId),
                  ),
                );
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.logout),
              title: const Text('تسجيل الخروج'),
              onTap: () {
                FirebaseAuth.instance.signOut();
                // You might want to navigate to the login screen after logout
              },
            ),
          ],
        ),
      ),
      body: IndexedStack(index: _selectedIndex, children: _pages),
      bottomNavigationBar: Container(
        decoration: const BoxDecoration(
          border: Border(top: BorderSide(color: AppColors.border, width: 1)),
        ),
        child: BottomNavigationBar(
          items: const <BottomNavigationBarItem>[
            BottomNavigationBarItem(
              icon: Icon(Icons.home_outlined),
              activeIcon: Icon(Icons.home),
              label: 'الرئيسية',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.assignment_outlined),
              activeIcon: Icon(Icons.assignment),
              label: 'الواجبات',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.check_circle_outline),
              activeIcon: Icon(Icons.check_circle),
              label: 'الحضور',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.monetization_on_outlined),
              activeIcon: Icon(Icons.monetization_on),
              label: 'الرسوم',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.grade_outlined),
              activeIcon: Icon(Icons.grade),
              label: 'الدرجات',
            ),
          ],
          currentIndex: _selectedIndex,
          onTap: _onItemTapped,
          backgroundColor: AppColors.surface,
          selectedItemColor: AppColors.studentColor,
          unselectedItemColor: AppColors.textSecondary,
          showUnselectedLabels: true,
          type: BottomNavigationBarType.fixed,
          elevation: AppElevation.high,
          selectedLabelStyle: const TextStyle(
            fontSize: AppTextSizes.labelSmall,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: AppTextSizes.labelSmall,
            fontWeight: FontWeight.normal,
          ),
        ),
      ),
    );
  }

  /// بناء عنصر في القائمة الجانبية مع التصميم المحسن
  Widget _buildDrawerItem({
    required IconData icon,
    required IconData selectedIcon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isSelected = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      child: Material(
        color:
            isSelected
                ? AppColors.studentColor.withValues(alpha: 0.1)
                : Colors.transparent,
        borderRadius: BorderRadius.circular(AppBorderRadius.medium),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(AppSpacing.sm),
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? AppColors.studentColor
                            : AppColors.studentColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppBorderRadius.small),
                  ),
                  child: Icon(
                    isSelected ? selectedIcon : icon,
                    color:
                        isSelected
                            ? AppColors.textOnPrimary
                            : AppColors.studentColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: AppTextSizes.bodyLarge,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.w500,
                          color:
                              isSelected
                                  ? AppColors.studentColor
                                  : AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: AppSpacing.xs),
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontSize: AppTextSizes.bodySmall,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: AppColors.textSecondary,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
