import 'package:flutter/material.dart';
import 'package:school_management_system/shared/app_theme.dart';

/// نظام التنقل الهرمي المحسن للوحة تحكم الإدارة
///
/// يقوم بتنظيم الشاشات في فئات منطقية لتسهيل التنقل
/// وتحسين تجربة المستخدم للإدارة المدرسية

/// فئات الإدارة الرئيسية
enum AdminSection {
  dashboard, // لوحة المعلومات
  academic, // الشؤون الأكاديمية
  students, // شؤون الطلاب
  staff, // شؤون الموظفين
  financial, // الشؤون المالية
  communication, // التواصل والإشعارات
  reports, // التقارير والإحصائيات
  settings, // الإعدادات والتكوين
}

/// عنصر في قائمة التنقل الإدارية
class AdminNavigationItem {
  /// عنوان العنصر
  final String title;

  /// أيقونة العنصر
  final IconData icon;

  /// أيقونة العنصر المحدد
  final IconData selectedIcon;

  /// الفئة التي ينتمي إليها العنصر
  final AdminSection section;

  /// فهرس الشاشة في قائمة الصفحات
  final int pageIndex;

  /// وصف مختصر للعنصر
  final String? description;

  /// لون مخصص للعنصر (اختياري)
  final Color? color;

  const AdminNavigationItem({
    required this.title,
    required this.icon,
    required this.selectedIcon,
    required this.section,
    required this.pageIndex,
    this.description,
    this.color,
  });
}

/// مدير التنقل الإداري
class AdminNavigationManager {
  /// قائمة جميع عناصر التنقل مرتبة حسب الفئات
  static const List<AdminNavigationItem> _allItems = [
    // لوحة المعلومات
    AdminNavigationItem(
      title: 'لوحة المعلومات',
      icon: Icons.dashboard_outlined,
      selectedIcon: Icons.dashboard,
      section: AdminSection.dashboard,
      pageIndex: 0,
      description: 'نظرة عامة على النظام',
      color: AppColors.primary,
    ),

    // الشؤون الأكاديمية
    AdminNavigationItem(
      title: 'إدارة المحتوى',
      icon: Icons.article_outlined,
      selectedIcon: Icons.edit_document,
      section: AdminSection.academic,
      pageIndex: 1,
      description: 'إدارة المحتوى التعليمي',
    ),
    AdminNavigationItem(
      title: 'الواجبات',
      icon: Icons.assignment_outlined,
      selectedIcon: Icons.assignment,
      section: AdminSection.academic,
      pageIndex: 2,
      description: 'إدارة الواجبات المدرسية',
    ),
    AdminNavigationItem(
      title: 'المواد الدراسية',
      icon: Icons.book_outlined,
      selectedIcon: Icons.book,
      section: AdminSection.academic,
      pageIndex: 14,
      description: 'إدارة المواد والمناهج',
    ),
    AdminNavigationItem(
      title: 'الفصول الدراسية',
      icon: Icons.class_outlined,
      selectedIcon: Icons.class_,
      section: AdminSection.academic,
      pageIndex: 15,
      description: 'إدارة الفصول والشعب',
    ),
    AdminNavigationItem(
      title: 'الجداول الدراسية',
      icon: Icons.schedule_outlined,
      selectedIcon: Icons.schedule,
      section: AdminSection.academic,
      pageIndex: 16,
      description: 'إدارة جداول الحصص',
    ),
    AdminNavigationItem(
      title: 'الامتحانات',
      icon: Icons.quiz_outlined,
      selectedIcon: Icons.quiz,
      section: AdminSection.academic,
      pageIndex: 18,
      description: 'إدارة الامتحانات والتقييم',
    ),
    AdminNavigationItem(
      title: 'جداول الامتحانات',
      icon: Icons.calendar_today_outlined,
      selectedIcon: Icons.calendar_today,
      section: AdminSection.academic,
      pageIndex: 19,
      description: 'تنظيم مواعيد الامتحانات',
    ),

    // شؤون الطلاب
    AdminNavigationItem(
      title: 'إدارة الطلاب',
      icon: Icons.school_outlined,
      selectedIcon: Icons.school,
      section: AdminSection.students,
      pageIndex: 7,
      description: 'إدارة بيانات الطلاب',
      color: AppColors.studentColor,
    ),
    AdminNavigationItem(
      title: 'أولياء الأمور',
      icon: Icons.supervisor_account_outlined,
      selectedIcon: Icons.supervisor_account,
      section: AdminSection.students,
      pageIndex: 8,
      description: 'إدارة بيانات أولياء الأمور',
      color: AppColors.parentColor,
    ),
    AdminNavigationItem(
      title: 'الحضور والغياب',
      icon: Icons.check_circle_outline,
      selectedIcon: Icons.check_circle,
      section: AdminSection.students,
      pageIndex: 3,
      description: 'متابعة حضور الطلاب',
    ),
    AdminNavigationItem(
      title: 'الدرجات',
      icon: Icons.grade_outlined,
      selectedIcon: Icons.grade,
      section: AdminSection.students,
      pageIndex: 6,
      description: 'إدارة درجات الطلاب',
    ),
    AdminNavigationItem(
      title: 'تقييم السلوك',
      icon: Icons.psychology_outlined,
      selectedIcon: Icons.psychology,
      section: AdminSection.students,
      pageIndex: 23,
      description: 'تقييم سلوك الطلاب',
    ),

    // شؤون الموظفين
    AdminNavigationItem(
      title: 'المعلمين',
      icon: Icons.people_outline,
      selectedIcon: Icons.people,
      section: AdminSection.staff,
      pageIndex: 12,
      description: 'إدارة بيانات المعلمين',
      color: AppColors.teacherColor,
    ),
    AdminNavigationItem(
      title: 'الموظفين',
      icon: Icons.badge_outlined,
      selectedIcon: Icons.badge,
      section: AdminSection.staff,
      pageIndex: 12,
      description: 'إدارة بيانات الموظفين',
    ),
    AdminNavigationItem(
      title: 'المسؤولين',
      icon: Icons.admin_panel_settings_outlined,
      selectedIcon: Icons.admin_panel_settings,
      section: AdminSection.staff,
      pageIndex: 13,
      description: 'إدارة صلاحيات المسؤولين',
      color: AppColors.adminColor,
    ),

    // الشؤون المالية
    AdminNavigationItem(
      title: 'الرسوم الدراسية',
      icon: Icons.monetization_on_outlined,
      selectedIcon: Icons.monetization_on,
      section: AdminSection.financial,
      pageIndex: 4,
      description: 'إدارة رسوم الطلاب',
    ),
    AdminNavigationItem(
      title: 'أنواع الرسوم',
      icon: Icons.category_outlined,
      selectedIcon: Icons.category,
      section: AdminSection.financial,
      pageIndex: 5,
      description: 'تصنيف أنواع الرسوم',
    ),

    // التواصل والإشعارات
    AdminNavigationItem(
      title: 'الإشعارات',
      icon: Icons.notifications_outlined,
      selectedIcon: Icons.notifications,
      section: AdminSection.communication,
      pageIndex: 11,
      description: 'إرسال الإشعارات',
    ),
    AdminNavigationItem(
      title: 'التواصل المتقدم',
      icon: Icons.forum_outlined,
      selectedIcon: Icons.forum,
      section: AdminSection.communication,
      pageIndex: 21,
      description: 'نظام المراسلة المتقدم',
    ),
    AdminNavigationItem(
      title: 'إدارة المواعيد',
      icon: Icons.event_outlined,
      selectedIcon: Icons.event,
      section: AdminSection.communication,
      pageIndex: 22,
      description: 'تنظيم المواعيد والاجتماعات',
    ),
    AdminNavigationItem(
      title: 'إدارة الطلبات',
      icon: Icons.request_page_outlined,
      selectedIcon: Icons.request_page,
      section: AdminSection.communication,
      pageIndex: 24,
      description: 'معالجة طلبات أولياء الأمور',
    ),

    // التقارير والإحصائيات
    AdminNavigationItem(
      title: 'تقارير الطلاب',
      icon: Icons.bar_chart_outlined,
      selectedIcon: Icons.bar_chart,
      section: AdminSection.reports,
      pageIndex: 9,
      description: 'تقارير شاملة عن الطلاب',
    ),
    AdminNavigationItem(
      title: 'ملاحظات الطلاب',
      icon: Icons.feedback_outlined,
      selectedIcon: Icons.feedback,
      section: AdminSection.reports,
      pageIndex: 10,
      description: 'ملاحظات ومتابعات الطلاب',
    ),

    // الإعدادات والتكوين
    AdminNavigationItem(
      title: 'إعدادات الامتحانات',
      icon: Icons.settings_outlined,
      selectedIcon: Icons.settings,
      section: AdminSection.settings,
      pageIndex: 20,
      description: 'تكوين نظام الامتحانات',
    ),
    AdminNavigationItem(
      title: 'عن المدرسة',
      icon: Icons.info_outline,
      selectedIcon: Icons.info,
      section: AdminSection.settings,
      pageIndex: 17,
      description: 'معلومات المدرسة العامة',
    ),
  ];

  /// الحصول على جميع العناصر
  static List<AdminNavigationItem> get allItems => _allItems;

  /// الحصول على العناصر حسب الفئة
  static List<AdminNavigationItem> getItemsBySection(AdminSection section) {
    return _allItems.where((item) => item.section == section).toList();
  }

  /// الحصول على عنوان الفئة
  static String getSectionTitle(AdminSection section) {
    switch (section) {
      case AdminSection.dashboard:
        return 'لوحة المعلومات';
      case AdminSection.academic:
        return 'الشؤون الأكاديمية';
      case AdminSection.students:
        return 'شؤون الطلاب';
      case AdminSection.staff:
        return 'شؤون الموظفين';
      case AdminSection.financial:
        return 'الشؤون المالية';
      case AdminSection.communication:
        return 'التواصل والإشعارات';
      case AdminSection.reports:
        return 'التقارير والإحصائيات';
      case AdminSection.settings:
        return 'الإعدادات والتكوين';
    }
  }

  /// الحصول على أيقونة الفئة
  static IconData getSectionIcon(AdminSection section) {
    switch (section) {
      case AdminSection.dashboard:
        return Icons.dashboard;
      case AdminSection.academic:
        return Icons.school;
      case AdminSection.students:
        return Icons.groups;
      case AdminSection.staff:
        return Icons.people;
      case AdminSection.financial:
        return Icons.account_balance;
      case AdminSection.communication:
        return Icons.forum;
      case AdminSection.reports:
        return Icons.analytics;
      case AdminSection.settings:
        return Icons.settings;
    }
  }

  /// الحصول على لون الفئة
  static Color getSectionColor(AdminSection section) {
    switch (section) {
      case AdminSection.dashboard:
        return AppColors.primary;
      case AdminSection.academic:
        return AppColors.info;
      case AdminSection.students:
        return AppColors.studentColor;
      case AdminSection.staff:
        return AppColors.teacherColor;
      case AdminSection.financial:
        return AppColors.success;
      case AdminSection.communication:
        return AppColors.accent;
      case AdminSection.reports:
        return AppColors.secondary;
      case AdminSection.settings:
        return AppColors.textSecondary;
    }
  }
}
