import 'package:flutter/material.dart';
import 'package:school_management_system/utils/error_handler.dart';

/// مساعد لمعالجة الأخطاء في العمليات غير المتزامنة
/// 
/// يوفر طرق مختلفة لتنفيذ العمليات مع معالجة شاملة للأخطاء
class AsyncErrorHandler {
  /// تنفيذ عملية غير متزامنة مع معالجة الأخطاء
  /// 
  /// يتعامل مع الأخطاء ويعرض رسائل مناسبة للمستخدم
  static Future<T?> execute<T>({
    required Future<T> Function() operation,
    required BuildContext context,
    String? loadingMessage,
    String? successMessage,
    VoidCallback? onSuccess,
    VoidCallback? onError,
    bool showLoadingDialog = false,
    bool showSuccessMessage = false,
  }) async {
    // عرض dialog التحميل إذا كان مطلوباً
    if (showLoadingDialog && context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Expanded(
                child: Text(loadingMessage ?? 'جاري التحميل...'),
              ),
            ],
          ),
        ),
      );
    }

    try {
      final result = await operation();
      
      // إغلاق dialog التحميل
      if (showLoadingDialog && context.mounted) {
        Navigator.of(context).pop();
      }
      
      // عرض رسالة النجاح
      if (showSuccessMessage && successMessage != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text(successMessage)),
              ],
            ),
            backgroundColor: Colors.green[600],
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
      
      // تنفيذ callback النجاح
      onSuccess?.call();
      
      return result;
    } catch (error) {
      // إغلاق dialog التحميل في حالة الخطأ
      if (showLoadingDialog && context.mounted) {
        Navigator.of(context).pop();
      }
      
      // عرض رسالة الخطأ
      if (context.mounted) {
        ErrorHandler.showErrorSnackBar(context, error);
      }
      
      // تنفيذ callback الخطأ
      onError?.call();
      
      return null;
    }
  }

  /// تنفيذ عملية مع إعادة المحاولة التلقائية
  /// 
  /// يحاول تنفيذ العملية عدة مرات قبل الفشل النهائي
  static Future<T?> executeWithRetry<T>({
    required Future<T> Function() operation,
    required BuildContext context,
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 2),
    String? loadingMessage,
    bool showProgress = false,
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        if (showProgress && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                loadingMessage ?? 'المحاولة ${attempts + 1} من $maxRetries...',
              ),
              duration: const Duration(seconds: 1),
            ),
          );
        }
        
        final result = await operation();
        return result;
      } catch (error) {
        attempts++;
        
        if (attempts >= maxRetries) {
          // فشل نهائي - عرض الخطأ
          if (context.mounted) {
            ErrorHandler.showErrorSnackBar(
              context,
              error,
              onRetry: () => executeWithRetry(
                operation: operation,
                context: context,
                maxRetries: maxRetries,
                retryDelay: retryDelay,
                loadingMessage: loadingMessage,
                showProgress: showProgress,
              ),
            );
          }
          return null;
        }
        
        // انتظار قبل المحاولة التالية
        await Future.delayed(retryDelay);
      }
    }
    
    return null;
  }

  /// تنفيذ عملية مع timeout
  /// 
  /// يلغي العملية إذا استغرقت وقتاً أطول من المحدد
  static Future<T?> executeWithTimeout<T>({
    required Future<T> Function() operation,
    required BuildContext context,
    Duration timeout = const Duration(seconds: 30),
    String? timeoutMessage,
  }) async {
    try {
      final result = await operation().timeout(
        timeout,
        onTimeout: () {
          throw TimeoutException(
            timeoutMessage ?? 'انتهت مهلة الاتصال',
            timeout,
          );
        },
      );
      return result;
    } catch (error) {
      if (context.mounted) {
        ErrorHandler.showErrorSnackBar(context, error);
      }
      return null;
    }
  }

  /// معالج شامل للعمليات المعقدة
  /// 
  /// يجمع بين إعادة المحاولة والـ timeout ومعالجة الأخطاء
  static Future<T?> executeAdvanced<T>({
    required Future<T> Function() operation,
    required BuildContext context,
    int maxRetries = 3,
    Duration timeout = const Duration(seconds: 30),
    Duration retryDelay = const Duration(seconds: 2),
    String? loadingMessage,
    String? successMessage,
    VoidCallback? onSuccess,
    VoidCallback? onError,
    bool showLoadingDialog = false,
    bool showSuccessMessage = false,
    bool showProgress = false,
  }) async {
    return executeWithRetry(
      operation: () => executeWithTimeout(
        operation: operation,
        context: context,
        timeout: timeout,
      ).then((result) {
        if (result == null) {
          throw Exception('فشل في تنفيذ العملية');
        }
        return result;
      }),
      context: context,
      maxRetries: maxRetries,
      retryDelay: retryDelay,
      loadingMessage: loadingMessage,
      showProgress: showProgress,
    ).then((result) {
      if (result != null) {
        // نجحت العملية
        if (showSuccessMessage && successMessage != null && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(child: Text(successMessage)),
                ],
              ),
              backgroundColor: Colors.green[600],
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
        onSuccess?.call();
      } else {
        // فشلت العملية
        onError?.call();
      }
      return result;
    });
  }

  /// معالج خاص للعمليات التي تتطلب اتصال بالإنترنت
  /// 
  /// يتحقق من الاتصال قبل تنفيذ العملية
  static Future<T?> executeNetworkOperation<T>({
    required Future<T> Function() operation,
    required BuildContext context,
    String? noConnectionMessage,
  }) async {
    // TODO: إضافة فحص الاتصال بالإنترنت
    // يمكن استخدام connectivity_plus package
    
    return execute(
      operation: operation,
      context: context,
    );
  }
}

/// استثناء انتهاء المهلة الزمنية
class TimeoutException implements Exception {
  final String message;
  final Duration timeout;

  const TimeoutException(this.message, this.timeout);

  @override
  String toString() => 'TimeoutException: $message (timeout: $timeout)';
}
