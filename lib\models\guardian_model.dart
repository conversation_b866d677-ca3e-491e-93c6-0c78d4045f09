import 'package:cloud_firestore/cloud_firestore.dart';

/// ===== نموذج بيانات ولي الأمر المحدث =====
///
/// هذا النموذج يحتوي على جميع المعلومات المطلوبة لولي الأمر في النظام المدرسي
/// تم تحديثه ليشمل الحقول الجديدة المطلوبة للمدارس اليمنية والعربية
///
/// الحقول مقسمة إلى أقسام منطقية:
/// 1. المعلومات الأساسية (مطلوبة)
/// 2. المعلومات الشخصية (اختيارية ومطلوبة)
/// 3. معلومات الاتصال (مطلوبة واختيارية)
/// 4. المعلومات الإضافية (اختيارية)
/// 5. معلومات النظام (تلقائية)
class GuardianModel {
  // ===== المعلومات الأساسية المطلوبة =====

  /// مُعرّف المستند الفريد من Firestore
  /// يتم إنشاؤه تلقائياً عند إضافة ولي أمر جديد
  final String id;

  /// الاسم الكامل لولي الأمر (مطلوب)
  /// يجب أن يحتوي على الاسم الأول والأخير على الأقل
  /// مثال: "أحمد محمد علي الشامي"
  final String name;

  /// البريد الإلكتروني لولي الأمر (مطلوب)
  /// يستخدم لتسجيل الدخول والتواصل
  /// يجب أن يكون فريداً في النظام
  /// مثال: "<EMAIL>"
  final String email;

  // ===== معلومات الاتصال =====

  /// رقم الهاتف الأساسي لولي الأمر (مطلوب)
  /// يستخدم للتواصل السريع والطوارئ
  /// يفضل أن يكون رقم جوال للوصول السريع
  /// مثال: "+967771234567" أو "771234567"
  final String phoneNumber;

  /// رقم هاتف إضافي أو بديل (اختياري)
  /// يمكن أن يكون رقم المنزل أو رقم العمل
  /// مفيد في حالة عدم الوصول للرقم الأساسي
  /// مثال: "01234567" (رقم أرضي)
  final String? alternativePhoneNumber;

  // ===== المعلومات الشخصية =====

  /// جنس ولي الأمر (اختياري)
  /// القيم المسموحة: "ذكر", "أنثى"
  /// يساعد في التواصل المناسب والإحصائيات
  final String? gender;

  /// تاريخ ميلاد ولي الأمر (اختياري)
  /// يستخدم لحساب العمر والإحصائيات
  /// مفيد في بعض الإجراءات الإدارية
  final DateTime? dateOfBirth;

  /// عنوان السكن الكامل (اختياري)
  /// يشمل الحي، الشارع، رقم المنزل
  /// مفيد للمراسلات والزيارات المنزلية
  /// مثال: "حي الصافية، شارع الجامعة، منزل رقم 15"
  final String? address;

  /// المحافظة التي يسكن فيها ولي الأمر (مطلوب)
  /// مهم للإحصائيات الجغرافية والتواصل
  /// مثال: "صنعاء", "عدن", "تعز", "إب"
  final String governorate;

  /// الجنسية (افتراضي: يمني)
  /// مهم للطلاب الأجانب أو المقيمين
  /// مثال: "يمني", "سوري", "مصري"
  final String nationality;

  // ===== المعلومات الإضافية =====

  /// الرقم الوطني أو رقم الهوية (اختياري)
  /// مهم للتحقق من الهوية والإجراءات الرسمية
  /// مثال: "01234567890"
  final String? nationalId;

  /// المهنة أو الوظيفة (اختياري)
  /// يساعد في فهم الخلفية الاجتماعية والاقتصادية
  /// مثال: "مهندس", "طبيب", "معلم", "تاجر"
  final String? occupation;

  /// مكان العمل (اختياري)
  /// يساعد في التواصل أثناء ساعات العمل
  /// مثال: "مستشفى الثورة", "جامعة صنعاء", "شركة النفط"
  final String? workplace;

  /// المستوى التعليمي (اختياري)
  /// يساعد في فهم الخلفية التعليمية
  /// مثال: "ثانوية عامة", "بكالوريوس", "ماجستير", "دكتوراه"
  final String? educationLevel;

  /// الحالة الاجتماعية (اختياري)
  /// مهم لفهم الوضع العائلي
  /// مثال: "متزوج", "أعزب", "مطلق", "أرمل"
  final String? maritalStatus;

  /// فصيلة الدم (اختياري)
  /// مهم في حالات الطوارئ الطبية
  /// مثال: "A+", "B-", "O+", "AB-"
  final String? bloodType;

  /// الحالة الصحية أو الأمراض المزمنة (اختياري)
  /// مهم لفهم أي قيود صحية قد تؤثر على التواصل
  /// مثال: "سكري", "ضغط دم", "لا يوجد"
  final String? healthCondition;

  /// ملاحظات خاصة أو معلومات إضافية (اختياري)
  /// يمكن أن تحتوي على أي معلومات مهمة أخرى
  /// مثال: "يفضل التواصل مساءً", "متاح في عطلة نهاية الأسبوع"
  final String? notes;

  // ===== معلومات الصور =====

  /// رابط الصورة الشخصية (اختياري)
  /// يتم رفعها إلى Firebase Storage
  /// تساعد في التعرف على ولي الأمر
  final String? profileImageUrl;

  // ===== معلومات النظام =====

  /// تاريخ إنشاء الحساب
  /// يتم تعيينه تلقائياً عند إنشاء الحساب
  final Timestamp createdAt;

  /// تاريخ آخر تحديث للبيانات (اختياري)
  /// يتم تحديثه تلقائياً عند تعديل البيانات
  final Timestamp? updatedAt;

  /// قائمة بمعرّفات الطلاب المرتبطين بولي الأمر
  /// يمكن أن يكون لولي الأمر أكثر من طالب
  /// مثال: ["student_001", "student_002"]
  final List<String> linkedStudents;

  /// حالة الحساب (نشط، معطل، محذوف)
  /// افتراضي: "active"
  /// يستخدم لإدارة صلاحيات الوصول
  final String accountStatus;

  /// آخر تسجيل دخول (اختياري)
  /// يساعد في مراقبة نشاط الحساب
  final Timestamp? lastLoginAt;

  // ===== منشئ الكلاس مع جميع الحقول =====
  GuardianModel({
    // المعلومات الأساسية المطلوبة
    required this.id,
    required this.name,
    required this.email,
    required this.phoneNumber,
    required this.governorate,

    // المعلومات الاختيارية مع القيم الافتراضية
    this.alternativePhoneNumber,
    this.gender,
    this.dateOfBirth,
    this.address,
    this.nationality = 'يمني', // القيمة الافتراضية للجنسية
    this.nationalId,
    this.occupation,
    this.workplace,
    this.educationLevel,
    this.maritalStatus,
    this.bloodType,
    this.healthCondition,
    this.notes,
    this.profileImageUrl,

    // معلومات النظام
    required this.createdAt,
    this.updatedAt,
    required this.linkedStudents,
    this.accountStatus = 'active', // القيمة الافتراضية لحالة الحساب
    this.lastLoginAt,
  });

  /// دالة لتحويل البيانات من Firestore إلى كائن GuardianModel
  factory GuardianModel.fromMap(Map<String, dynamic> data, String documentId) {
    // تحويل قائمة الطلاب المرتبطين بأمان
    final List<dynamic> studentsFromDb = data['linked_students'] ?? [];
    final List<String> studentUids =
        studentsFromDb.map((e) => e.toString()).toList();

    // ===== تحويل تاريخ الميلاد من Timestamp إلى DateTime =====
    DateTime? dateOfBirth;
    if (data['date_of_birth'] != null) {
      if (data['date_of_birth'] is Timestamp) {
        dateOfBirth = (data['date_of_birth'] as Timestamp).toDate();
      } else if (data['date_of_birth'] is String) {
        // في حالة كان التاريخ مخزون كنص، نحاول تحويله
        try {
          dateOfBirth = DateTime.parse(data['date_of_birth']);
        } catch (e) {
          // في حالة فشل التحويل، نتجاهل التاريخ
          dateOfBirth = null;
        }
      }
    }

    // ===== تحويل تاريخ آخر تحديث =====
    Timestamp? updatedAt;
    if (data['updated_at'] != null && data['updated_at'] is Timestamp) {
      updatedAt = data['updated_at'] as Timestamp;
    }

    // ===== تحويل تاريخ آخر تسجيل دخول =====
    Timestamp? lastLoginAt;
    if (data['last_login_at'] != null && data['last_login_at'] is Timestamp) {
      lastLoginAt = data['last_login_at'] as Timestamp;
    }

    // ===== إنشاء كائن GuardianModel مع جميع الحقول =====
    return GuardianModel(
      // ===== المعلومات الأساسية المطلوبة =====
      id: documentId,
      name: data['name'] ?? '', // الاسم الكامل
      email: data['email'] ?? '', // البريد الإلكتروني
      phoneNumber: data['phone_number'] ?? '', // رقم الهاتف الأساسي
      governorate: data['governorate'] ?? 'غير محدد', // المحافظة (مطلوب)
      // ===== معلومات الاتصال الإضافية =====
      alternativePhoneNumber: data['alternative_phone_number'], // رقم هاتف بديل
      // ===== المعلومات الشخصية =====
      gender: data['gender'], // الجنس
      dateOfBirth: dateOfBirth, // تاريخ الميلاد
      address: data['address'], // العنوان
      nationality: data['nationality'] ?? 'يمني', // الجنسية (افتراضي: يمني)
      // ===== المعلومات الإضافية =====
      nationalId: data['national_id'], // الرقم الوطني
      occupation: data['occupation'], // المهنة
      workplace: data['workplace'], // مكان العمل
      educationLevel: data['education_level'], // المستوى التعليمي
      maritalStatus: data['marital_status'], // الحالة الاجتماعية
      bloodType: data['blood_type'], // فصيلة الدم
      healthCondition: data['health_condition'], // الحالة الصحية
      notes: data['notes'], // ملاحظات خاصة
      // ===== معلومات الصور =====
      profileImageUrl: data['profile_image_url'], // رابط الصورة الشخصية
      // ===== معلومات النظام =====
      createdAt: data['created_at'] ?? Timestamp.now(), // تاريخ الإنشاء
      updatedAt: updatedAt, // تاريخ آخر تحديث
      linkedStudents: studentUids, // قائمة الطلاب المرتبطين
      accountStatus: data['account_status'] ?? 'active', // حالة الحساب
      lastLoginAt: lastLoginAt, // آخر تسجيل دخول
    );
  }

  /// ===== دالة لتحويل كائن GuardianModel إلى Map لإرساله إلى Firestore =====
  ///
  /// هذه الدالة تحول كائن GuardianModel إلى Map يمكن حفظه في Firestore
  /// تتعامل مع جميع الحقول وتحول التواريخ إلى Timestamp
  /// تتجاهل الحقول الفارغة لتوفير مساحة التخزين
  ///
  /// الإرجاع: Map<String, dynamic> جاهز للحفظ في Firestore
  Map<String, dynamic> toMap() {
    // ===== إنشاء Map أساسي مع الحقول المطلوبة =====
    final Map<String, dynamic> map = {
      // ===== المعلومات الأساسية المطلوبة =====
      'name': name, // الاسم الكامل
      'email': email, // البريد الإلكتروني
      'phone_number': phoneNumber, // رقم الهاتف الأساسي
      'governorate': governorate, // المحافظة
      'nationality': nationality, // الجنسية
      // ===== معلومات النظام =====
      'created_at': createdAt, // تاريخ الإنشاء
      'linked_students': linkedStudents, // قائمة الطلاب المرتبطين
      'account_status': accountStatus, // حالة الحساب
    };

    // ===== إضافة الحقول الاختيارية فقط إذا كانت غير فارغة =====
    // هذا يوفر مساحة التخزين ويجعل البيانات أكثر نظافة

    // معلومات الاتصال الإضافية
    if (alternativePhoneNumber != null && alternativePhoneNumber!.isNotEmpty) {
      map['alternative_phone_number'] = alternativePhoneNumber;
    }

    // المعلومات الشخصية
    if (gender != null && gender!.isNotEmpty) {
      map['gender'] = gender;
    }

    if (dateOfBirth != null) {
      // تحويل DateTime إلى Timestamp للحفظ في Firestore
      map['date_of_birth'] = Timestamp.fromDate(dateOfBirth!);
    }

    if (address != null && address!.isNotEmpty) {
      map['address'] = address;
    }

    // المعلومات الإضافية
    if (nationalId != null && nationalId!.isNotEmpty) {
      map['national_id'] = nationalId;
    }

    if (occupation != null && occupation!.isNotEmpty) {
      map['occupation'] = occupation;
    }

    if (workplace != null && workplace!.isNotEmpty) {
      map['workplace'] = workplace;
    }

    if (educationLevel != null && educationLevel!.isNotEmpty) {
      map['education_level'] = educationLevel;
    }

    if (maritalStatus != null && maritalStatus!.isNotEmpty) {
      map['marital_status'] = maritalStatus;
    }

    if (bloodType != null && bloodType!.isNotEmpty) {
      map['blood_type'] = bloodType;
    }

    if (healthCondition != null && healthCondition!.isNotEmpty) {
      map['health_condition'] = healthCondition;
    }

    if (notes != null && notes!.isNotEmpty) {
      map['notes'] = notes;
    }

    // معلومات الصور
    if (profileImageUrl != null && profileImageUrl!.isNotEmpty) {
      map['profile_image_url'] = profileImageUrl;
    }

    // معلومات النظام الاختيارية
    if (updatedAt != null) {
      map['updated_at'] = updatedAt;
    }

    if (lastLoginAt != null) {
      map['last_login_at'] = lastLoginAt;
    }

    return map;
  }

  /// ===== دالة لإنشاء نسخة محدثة من GuardianModel =====
  ///
  /// هذه الدالة تسمح بتحديث حقول معينة دون تغيير الباقي
  /// مفيدة عند تحديث البيانات جزئياً
  ///
  /// جميع المعاملات اختيارية، فقط الحقول المرسلة سيتم تحديثها
  GuardianModel copyWith({
    String? name,
    String? email,
    String? phoneNumber,
    String? alternativePhoneNumber,
    String? gender,
    DateTime? dateOfBirth,
    String? address,
    String? governorate,
    String? nationality,
    String? nationalId,
    String? occupation,
    String? workplace,
    String? educationLevel,
    String? maritalStatus,
    String? bloodType,
    String? healthCondition,
    String? notes,
    String? profileImageUrl,
    Timestamp? updatedAt,
    List<String>? linkedStudents,
    String? accountStatus,
    Timestamp? lastLoginAt,
  }) {
    return GuardianModel(
      // المعلومات الأساسية
      id: id, // المعرف لا يتغير أبداً
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      governorate: governorate ?? this.governorate,

      // المعلومات الاختيارية
      alternativePhoneNumber:
          alternativePhoneNumber ?? this.alternativePhoneNumber,
      gender: gender ?? this.gender,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      address: address ?? this.address,
      nationality: nationality ?? this.nationality,
      nationalId: nationalId ?? this.nationalId,
      occupation: occupation ?? this.occupation,
      workplace: workplace ?? this.workplace,
      educationLevel: educationLevel ?? this.educationLevel,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      bloodType: bloodType ?? this.bloodType,
      healthCondition: healthCondition ?? this.healthCondition,
      notes: notes ?? this.notes,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,

      // معلومات النظام
      createdAt: createdAt, // تاريخ الإنشاء لا يتغير
      updatedAt: updatedAt ?? Timestamp.now(), // تحديث تاريخ التعديل تلقائياً
      linkedStudents: linkedStudents ?? this.linkedStudents,
      accountStatus: accountStatus ?? this.accountStatus,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }

  /// ===== دالة للحصول على الاسم المختصر =====
  ///
  /// ترجع الاسم الأول والأخير فقط
  /// مفيدة في الواجهات التي تحتاج مساحة أقل
  String get shortName {
    final nameParts = name.trim().split(' ');
    if (nameParts.length >= 2) {
      return '${nameParts.first} ${nameParts.last}';
    }
    return name;
  }

  /// ===== دالة لحساب العمر =====
  ///
  /// ترجع العمر بالسنوات إذا كان تاريخ الميلاد متوفراً
  /// ترجع null إذا لم يكن تاريخ الميلاد متوفراً
  int? get age {
    if (dateOfBirth == null) return null;

    final now = DateTime.now();
    int age = now.year - dateOfBirth!.year;

    // التحقق من عدم حلول عيد الميلاد بعد
    if (now.month < dateOfBirth!.month ||
        (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
      age--;
    }

    return age;
  }

  /// ===== دالة للتحقق من صحة البيانات =====
  ///
  /// تتحقق من أن البيانات الأساسية موجودة وصحيحة
  /// ترجع true إذا كانت البيانات صحيحة
  bool get isValid {
    return name.isNotEmpty &&
        email.isNotEmpty &&
        email.contains('@') &&
        phoneNumber.isNotEmpty &&
        governorate.isNotEmpty;
  }

  /// ===== دالة للحصول على معلومات الاتصال الكاملة =====
  ///
  /// ترجع قائمة بجميع طرق الاتصال المتوفرة
  List<String> get contactMethods {
    final List<String> methods = [];

    if (email.isNotEmpty) {
      methods.add('البريد الإلكتروني: $email');
    }

    if (phoneNumber.isNotEmpty) {
      methods.add('الهاتف الأساسي: $phoneNumber');
    }

    if (alternativePhoneNumber != null && alternativePhoneNumber!.isNotEmpty) {
      methods.add('الهاتف البديل: $alternativePhoneNumber');
    }

    return methods;
  }
}
