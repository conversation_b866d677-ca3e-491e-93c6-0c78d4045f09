import 'package:flutter/material.dart';
import 'package:school_management_system/shared/app_theme.dart';
import 'package:school_management_system/admin_screens/admin_navigation_system.dart';
import 'package:school_management_system/admin_screens/enhanced_admin_layout.dart';
import 'package:school_management_system/widgets/enhanced_widgets.dart';

/// مثال على استخدام التحسينات من المرحلة الثانية
/// 
/// يوضح هذا الملف كيفية:
/// 1. استخدام نظام التنقل الهرمي للإدارة
/// 2. تطبيق الألوان المميزة لكل نوع مستخدم
/// 3. استخدام لوحة التحكم المحسنة
/// 4. تطبيق التصميم الموحد على الشاشات الجديدة

/// مثال على شاشة إدارية جديدة تطبق التصميم الموحد
class ExampleAdminScreen extends StatelessWidget {
  const ExampleAdminScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'مثال على شاشة إدارية',
          style: TextStyle(
            fontSize: AppTextSizes.headlineMedium,
            fontWeight: FontWeight.bold,
            color: AppColors.textOnPrimary,
          ),
        ),
        backgroundColor: AppColors.adminColor,
        foregroundColor: AppColors.textOnPrimary,
        elevation: AppElevation.medium,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.screenPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            _buildSectionHeader('إحصائيات سريعة'),
            const SizedBox(height: AppSpacing.md),
            
            // بطاقات الإحصائيات
            _buildStatsCards(),
            const SizedBox(height: AppSpacing.sectionSpacing),
            
            // عنوان القسم
            _buildSectionHeader('الإجراءات السريعة'),
            const SizedBox(height: AppSpacing.md),
            
            // أزرار الإجراءات
            _buildActionButtons(),
            const SizedBox(height: AppSpacing.sectionSpacing),
            
            // عنوان القسم
            _buildSectionHeader('التنقل بين الفئات'),
            const SizedBox(height: AppSpacing.md),
            
            // مثال على استخدام نظام التنقل
            _buildNavigationExample(),
          ],
        ),
      ),
    );
  }

  /// بناء عنوان قسم
  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: AppTextSizes.headlineLarge,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }

  /// بناء بطاقات الإحصائيات
  Widget _buildStatsCards() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'الطلاب',
            value: '1,234',
            icon: Icons.school,
            color: AppColors.studentColor,
          ),
        ),
        const SizedBox(width: AppSpacing.md),
        Expanded(
          child: _buildStatCard(
            title: 'المعلمين',
            value: '56',
            icon: Icons.people,
            color: AppColors.teacherColor,
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية واحدة
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return EnhancedCard(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppBorderRadius.medium),
            ),
            child: Icon(
              icon,
              color: color,
              size: 32,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            value,
            style: TextStyle(
              fontSize: AppTextSizes.displayMedium,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: AppTextSizes.bodyMedium,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: EnhancedButton(
                text: 'إضافة طالب',
                icon: Icons.person_add,
                color: AppColors.studentColor,
                fullWidth: true,
                onPressed: () {
                  // TODO: تنفيذ إضافة طالب
                },
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: EnhancedButton(
                text: 'إضافة معلم',
                icon: Icons.person_add,
                color: AppColors.teacherColor,
                fullWidth: true,
                onPressed: () {
                  // TODO: تنفيذ إضافة معلم
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.md),
        EnhancedButton(
          text: 'عرض التقارير',
          icon: Icons.analytics,
          type: ButtonType.outlined,
          color: AppColors.secondary,
          fullWidth: true,
          onPressed: () {
            // TODO: تنفيذ عرض التقارير
          },
        ),
      ],
    );
  }

  /// بناء مثال على استخدام نظام التنقل
  Widget _buildNavigationExample() {
    return Column(
      children: AdminSection.values.map((section) {
        final sectionColor = AdminNavigationManager.getSectionColor(section);
        final sectionIcon = AdminNavigationManager.getSectionIcon(section);
        final sectionTitle = AdminNavigationManager.getSectionTitle(section);
        final items = AdminNavigationManager.getItemsBySection(section);
        
        return Container(
          margin: const EdgeInsets.only(bottom: AppSpacing.sm),
          child: EnhancedCard(
            borderColor: sectionColor,
            borderWidth: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس الفئة
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(AppSpacing.sm),
                      decoration: BoxDecoration(
                        color: sectionColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppBorderRadius.small),
                      ),
                      child: Icon(
                        sectionIcon,
                        color: sectionColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: AppSpacing.md),
                    Expanded(
                      child: Text(
                        sectionTitle,
                        style: TextStyle(
                          fontSize: AppTextSizes.titleLarge,
                          fontWeight: FontWeight.w600,
                          color: sectionColor,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSpacing.sm,
                        vertical: AppSpacing.xs,
                      ),
                      decoration: BoxDecoration(
                        color: sectionColor,
                        borderRadius: BorderRadius.circular(AppBorderRadius.small),
                      ),
                      child: Text(
                        '${items.length}',
                        style: const TextStyle(
                          color: AppColors.textOnPrimary,
                          fontSize: AppTextSizes.labelSmall,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                
                if (items.isNotEmpty) ...[
                  const SizedBox(height: AppSpacing.sm),
                  const Divider(),
                  const SizedBox(height: AppSpacing.sm),
                  
                  // عرض أول 3 عناصر كمثال
                  ...items.take(3).map((item) => Padding(
                    padding: const EdgeInsets.only(bottom: AppSpacing.xs),
                    child: Row(
                      children: [
                        Icon(
                          item.icon,
                          size: 16,
                          color: AppColors.textSecondary,
                        ),
                        const SizedBox(width: AppSpacing.sm),
                        Expanded(
                          child: Text(
                            item.title,
                            style: const TextStyle(
                              fontSize: AppTextSizes.bodyMedium,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),
                  
                  if (items.length > 3)
                    Text(
                      'و ${items.length - 3} عناصر أخرى...',
                      style: const TextStyle(
                        fontSize: AppTextSizes.bodySmall,
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                ],
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
}

/// مثال على كيفية استخدام لوحة التحكم المحسنة
class ExampleEnhancedAdminApp extends StatelessWidget {
  const ExampleEnhancedAdminApp({super.key});

  @override
  Widget build(BuildContext context) {
    // قائمة الصفحات (نفس الترتيب من AdminMainLayout الأصلي)
    final pages = [
      const ExampleAdminScreen(),  // 0: الصفحة الرئيسية
      const Center(child: Text('إدارة المحتوى')),  // 1
      const Center(child: Text('الواجبات')),  // 2
      const Center(child: Text('الحضور والغياب')),  // 3
      const Center(child: Text('الرسوم الدراسية')),  // 4
      // ... باقي الصفحات
    ];

    return MaterialApp(
      title: 'مثال لوحة التحكم المحسنة',
      theme: AppTheme.lightTheme,
      home: EnhancedAdminLayout(pages: pages),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// مثال على شاشة طالب محسنة
class ExampleStudentScreen extends StatelessWidget {
  const ExampleStudentScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'مثال شاشة طالب',
          style: TextStyle(
            fontSize: AppTextSizes.headlineMedium,
            fontWeight: FontWeight.bold,
            color: AppColors.textOnPrimary,
          ),
        ),
        backgroundColor: AppColors.studentColor,
        foregroundColor: AppColors.textOnPrimary,
        elevation: AppElevation.medium,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            tooltip: 'الإشعارات',
            onPressed: () {
              // TODO: فتح صفحة الإشعارات
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.school,
              size: 64,
              color: AppColors.studentColor,
            ),
            SizedBox(height: AppSpacing.md),
            Text(
              'مرحباً بك في تطبيق الطالب المحسن!',
              style: TextStyle(
                fontSize: AppTextSizes.titleLarge,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppSpacing.sm),
            Text(
              'تم تطبيق التصميم الموحد الجديد',
              style: TextStyle(
                fontSize: AppTextSizes.bodyMedium,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
