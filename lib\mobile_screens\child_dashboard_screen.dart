import 'package:flutter/material.dart';
import 'package:school_management_system/mobile_screens/student_attendance_screen.dart';
import 'package:school_management_system/mobile_screens/student_fees_screen.dart';
import 'package:school_management_system/mobile_screens/student_grades_screen.dart';
import 'package:school_management_system/mobile_screens/student_timetable_screen.dart';
import 'package:school_management_system/shared/app_theme.dart';

/// شاشة تعرض ملخص بيانات الطالب (الابن)
class ChildDashboardScreen extends StatelessWidget {
  final String studentId;
  final String studentName;

  const ChildDashboardScreen({
    Key? key,
    required this.studentId,
    required this.studentName,
  }) : super(key: key);

  // ويدجت لإنشاء بطاقة تنقل
  Widget _buildNavigationCard(String title, IconData icon, VoidCallback onTap) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 40, color: Colors.orange.shade800),
              const SizedBox(height: 12),
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('ملخص أداء: $studentName'),
        backgroundColor: Colors.orange,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: [
            // قسم معلومات الطالب الشخصية
            Center(
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: AppColors.studentColor.withValues(
                      alpha: 0.1,
                    ),
                    child: Icon(
                      Icons.person,
                      size: 50,
                      color: AppColors.studentColor,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    studentName,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'ID: $studentId',
                    style: const TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 16),

            // قسم التنقل بين الشاشات
            GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildNavigationCard('الدرجات', Icons.star_border, () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => StudentGradesScreen(studentId: studentId),
                    ),
                  );
                }),
                _buildNavigationCard(
                  'الحضور والغياب',
                  Icons.check_circle_outline,
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (_) =>
                                StudentAttendanceScreen(studentId: studentId),
                      ),
                    );
                  },
                ),
                _buildNavigationCard(
                  'الرسوم الدراسية',
                  Icons.monetization_on_outlined,
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => StudentFeesScreen(studentId: studentId),
                      ),
                    );
                  },
                ),
                _buildNavigationCard(
                  'الجدول الدراسي',
                  Icons.calendar_today_outlined,
                  () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (_) => StudentTimetableScreen(studentId: studentId),
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
