import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/assignment_model.dart';
import 'package:school_management_system/providers/assignment_providers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/widgets/enhanced_error_widget.dart';
import 'package:school_management_system/mobile_screens/assignment_details_screen.dart';

/// شاشة الواجبات المدرسية المحسنة للطلاب
/// تعرض قائمة شاملة بجميع الواجبات مع إحصائيات وتصفية متقدمة
/// وإمكانيات عرض متنوعة (قائمة، شبكة، تقويم)
class StudentAssignmentsScreen extends ConsumerStatefulWidget {
  final String studentId;

  const StudentAssignmentsScreen({super.key, required this.studentId});

  @override
  ConsumerState<StudentAssignmentsScreen> createState() =>
      _StudentAssignmentsScreenState();
}

class _StudentAssignmentsScreenState
    extends ConsumerState<StudentAssignmentsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    // إعداد التبويبات (جميع الواجبات، العاجلة، المتأخرة، الإحصائيات)
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // مراقبة تدفق الواجبات المحسن
    final assignmentsAsyncValue = ref.watch(
      studentAssignmentsStreamProvider(widget.studentId),
    );

    // مراقبة نمط العرض الحالي
    final viewMode = ref.watch(assignmentViewModeProvider);

    // مراقبة حالة التصفية والترتيب
    final filter = ref.watch(assignmentFilterProvider);
    final sortMode = ref.watch(assignmentSortModeProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الواجبات المدرسية'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        // إضافة أزرار التحكم في شريط التطبيق
        actions: [
          // زر البحث
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
            tooltip: 'البحث في الواجبات',
          ),
          // قائمة الترتيب
          PopupMenuButton<AssignmentSortMode>(
            icon: const Icon(Icons.sort),
            tooltip: 'ترتيب الواجبات',
            onSelected: (mode) {
              ref.read(assignmentSortModeProvider.notifier).state = mode;
            },
            itemBuilder:
                (context) =>
                    AssignmentSortMode.values.map((mode) {
                      return PopupMenuItem(
                        value: mode,
                        child: Row(
                          children: [
                            Icon(
                              _getSortModeIcon(mode),
                              color: Theme.of(context).primaryColor,
                            ),
                            const SizedBox(width: 8),
                            Text(mode.arabicName),
                            if (sortMode == mode) ...[
                              const Spacer(),
                              Icon(
                                Icons.check,
                                color: Theme.of(context).primaryColor,
                              ),
                            ],
                          ],
                        ),
                      );
                    }).toList(),
          ),
          // قائمة أنماط العرض
          PopupMenuButton<AssignmentViewMode>(
            icon: const Icon(Icons.view_module),
            tooltip: 'تغيير نمط العرض',
            onSelected: (mode) {
              ref.read(assignmentViewModeProvider.notifier).state = mode;
            },
            itemBuilder:
                (context) =>
                    AssignmentViewMode.values.map((mode) {
                      return PopupMenuItem(
                        value: mode,
                        child: Row(
                          children: [
                            Icon(
                              _getViewModeIcon(mode),
                              color: Theme.of(context).primaryColor,
                            ),
                            const SizedBox(width: 8),
                            Text(mode.arabicName),
                            if (viewMode == mode) ...[
                              const Spacer(),
                              Icon(
                                Icons.check,
                                color: Theme.of(context).primaryColor,
                              ),
                            ],
                          ],
                        ),
                      );
                    }).toList(),
          ),
        ],
        // إضافة التبويبات
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.assignment), text: 'الكل'),
            Tab(icon: Icon(Icons.priority_high), text: 'عاجلة'),
            Tab(icon: Icon(Icons.warning), text: 'متأخرة'),
            Tab(icon: Icon(Icons.analytics), text: 'إحصائيات'),
          ],
        ),
      ),
      body: assignmentsAsyncValue.when(
        loading: () => const LoadingIndicator(),
        error:
            (error, stackTrace) => EnhancedErrorWidget(
              error: error,
              customMessage: 'حدث خطأ في تحميل الواجبات',
              onRetry:
                  () => ref.invalidate(
                    studentAssignmentsStreamProvider(widget.studentId),
                  ),
              showDetails: true,
            ),
        data: (assignments) {
          if (assignments.isEmpty) {
            return _buildEmptyState(context);
          }

          return TabBarView(
            controller: _tabController,
            children: [
              // تبويب جميع الواجبات
              _buildAllAssignmentsView(context, assignments),
              // تبويب الواجبات العاجلة
              _buildUrgentAssignmentsView(context),
              // تبويب الواجبات المتأخرة
              _buildOverdueAssignmentsView(context),
              // تبويب الإحصائيات
              _buildStatsView(context, assignments),
            ],
          );
        },
      ),
      // زر عائم للإجراءات السريعة
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  /// بناء حالة الشاشة الفارغة عند عدم وجود واجبات
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assignment_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد واجبات مدرسية حالياً',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر الواجبات الجديدة هنا عند إضافتها من قبل المعلمين',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // إعادة تحميل البيانات
              ref.invalidate(
                studentAssignmentsStreamProvider(widget.studentId),
              );
            },
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء عرض جميع الواجبات
  Widget _buildAllAssignmentsView(
    BuildContext context,
    List<AssignmentModel> assignments,
  ) {
    // الحصول على الواجبات المفلترة والمرتبة
    final filteredAssignments = ref.watch(
      filteredByStatusAssignmentsProvider(widget.studentId),
    );
    final viewMode = ref.watch(assignmentViewModeProvider);

    return Column(
      children: [
        // شريط الإحصائيات السريعة
        _buildQuickStatsBar(context, assignments),

        // شريط التصفية
        _buildFilterBar(context),

        // عرض الواجبات حسب النمط المختار
        Expanded(
          child: _buildAssignmentsContent(
            context,
            filteredAssignments,
            viewMode,
          ),
        ),
      ],
    );
  }

  /// بناء عرض الواجبات العاجلة
  Widget _buildUrgentAssignmentsView(BuildContext context) {
    final urgentAssignments = ref.watch(
      urgentAssignmentsProvider(widget.studentId),
    );

    return Column(
      children: [
        // بطاقة تحذيرية للواجبات العاجلة
        Container(
          width: double.infinity,
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.red.shade100, Colors.orange.shade50],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red.shade200),
          ),
          child: Row(
            children: [
              Icon(Icons.warning, color: Colors.red.shade700, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'واجبات عاجلة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.red.shade700,
                      ),
                    ),
                    Text(
                      'هذه الواجبات تحتاج إلى انتباه فوري (أقل من 24 ساعة)',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.red.shade700,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${urgentAssignments.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),

        // قائمة الواجبات العاجلة
        Expanded(
          child:
              urgentAssignments.isEmpty
                  ? _buildNoUrgentAssignments(context)
                  : ListView.separated(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: urgentAssignments.length,
                    separatorBuilder:
                        (context, index) => const SizedBox(height: 12),
                    itemBuilder: (context, index) {
                      final assignment = urgentAssignments[index];
                      return EnhancedAssignmentCard(
                        assignment: assignment,
                        studentId: widget.studentId,
                        isUrgent: true,
                        animationDelay: Duration(milliseconds: index * 100),
                      );
                    },
                  ),
        ),
      ],
    );
  }

  /// بناء عرض الواجبات المتأخرة
  Widget _buildOverdueAssignmentsView(BuildContext context) {
    final overdueAssignments = ref.watch(
      overdueAssignmentsProvider(widget.studentId),
    );

    return Column(
      children: [
        // بطاقة تحذيرية للواجبات المتأخرة
        Container(
          width: double.infinity,
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.red.shade200, Colors.red.shade50],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red.shade300),
          ),
          child: Row(
            children: [
              Icon(Icons.schedule, color: Colors.red.shade800, size: 28),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'واجبات متأخرة',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.red.shade800,
                      ),
                    ),
                    Text(
                      'هذه الواجبات تجاوزت موعد التسليم المحدد',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.red.shade800,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${overdueAssignments.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),

        // قائمة الواجبات المتأخرة
        Expanded(
          child:
              overdueAssignments.isEmpty
                  ? _buildNoOverdueAssignments(context)
                  : ListView.separated(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: overdueAssignments.length,
                    separatorBuilder:
                        (context, index) => const SizedBox(height: 12),
                    itemBuilder: (context, index) {
                      final assignment = overdueAssignments[index];
                      return EnhancedAssignmentCard(
                        assignment: assignment,
                        studentId: widget.studentId,
                        isOverdue: true,
                        animationDelay: Duration(milliseconds: index * 100),
                      );
                    },
                  ),
        ),
      ],
    );
  }

  /// بناء عرض الإحصائيات
  Widget _buildStatsView(
    BuildContext context,
    List<AssignmentModel> assignments,
  ) {
    final stats = ref.watch(assignmentStatsProvider(widget.studentId));

    if (stats == null) {
      return const Center(child: LoadingIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة الإحصائيات العامة
          _buildGeneralStatsCard(context, stats),

          const SizedBox(height: 16),

          // بطاقة توزيع الواجبات حسب الحالة
          _buildStatusDistributionCard(context, stats),

          const SizedBox(height: 16),

          // بطاقة توزيع الواجبات حسب المادة
          _buildSubjectDistributionCard(context, stats),

          const SizedBox(height: 16),

          // بطاقة توزيع الواجبات حسب الأولوية
          _buildPriorityDistributionCard(context, stats),

          const SizedBox(height: 16),

          // بطاقة الواجبات القادمة
          _buildUpcomingAssignmentsCard(context),
        ],
      ),
    );
  }

  /// بناء شريط الإحصائيات السريعة
  Widget _buildQuickStatsBar(
    BuildContext context,
    List<AssignmentModel> assignments,
  ) {
    final stats = AssignmentStats(assignments: assignments);

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.1),
            Theme.of(context).primaryColor.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.2),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildQuickStatItem(
            icon: Icons.assignment,
            label: 'المجموع',
            value: stats.totalAssignments.toString(),
            color: Colors.blue,
          ),
          _buildQuickStatItem(
            icon: Icons.pending_actions,
            label: 'مطلوبة',
            value: stats.pendingAssignments.toString(),
            color: Colors.orange,
          ),
          _buildQuickStatItem(
            icon: Icons.warning,
            label: 'متأخرة',
            value: stats.overdueAssignments.toString(),
            color: Colors.red,
          ),
          _buildQuickStatItem(
            icon: Icons.priority_high,
            label: 'عاجلة',
            value: stats.urgentAssignments.toString(),
            color: Colors.deepOrange,
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائية سريعة
  Widget _buildQuickStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
      ],
    );
  }

  /// بناء شريط التصفية
  Widget _buildFilterBar(BuildContext context) {
    final currentFilter = ref.watch(assignmentFilterProvider);

    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: AssignmentFilter.values.length,
        separatorBuilder: (context, index) => const SizedBox(width: 8),
        itemBuilder: (context, index) {
          final filter = AssignmentFilter.values[index];
          final isSelected = currentFilter == filter;

          return FilterChip(
            label: Text(filter.arabicName),
            selected: isSelected,
            onSelected: (selected) {
              ref.read(assignmentFilterProvider.notifier).state = filter;
            },
            selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
            checkmarkColor: Theme.of(context).primaryColor,
            labelStyle: TextStyle(
              color:
                  isSelected
                      ? Theme.of(context).primaryColor
                      : Colors.grey.shade700,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          );
        },
      ),
    );
  }

  /// بناء محتوى الواجبات حسب نمط العرض
  Widget _buildAssignmentsContent(
    BuildContext context,
    List<AssignmentModel> assignments,
    AssignmentViewMode viewMode,
  ) {
    if (assignments.isEmpty) {
      return _buildNoAssignmentsForFilter(context);
    }

    switch (viewMode) {
      case AssignmentViewMode.list:
        return _buildListView(context, assignments);
      case AssignmentViewMode.grid:
        return _buildGridView(context, assignments);
      case AssignmentViewMode.calendar:
        return _buildCalendarView(context, assignments);
      case AssignmentViewMode.timeline:
        return _buildTimelineView(context, assignments);
    }
  }

  /// بناء عرض القائمة
  Widget _buildListView(
    BuildContext context,
    List<AssignmentModel> assignments,
  ) {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: assignments.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final assignment = assignments[index];
        return EnhancedAssignmentCard(
          assignment: assignment,
          studentId: widget.studentId,
          animationDelay: Duration(milliseconds: index * 100),
        );
      },
    );
  }

  /// بناء عرض الشبكة
  Widget _buildGridView(
    BuildContext context,
    List<AssignmentModel> assignments,
  ) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: assignments.length,
      itemBuilder: (context, index) {
        final assignment = assignments[index];
        return CompactAssignmentCard(
          assignment: assignment,
          studentId: widget.studentId,
          animationDelay: Duration(milliseconds: index * 100),
        );
      },
    );
  }

  /// بناء عرض التقويم (مبسط)
  Widget _buildCalendarView(
    BuildContext context,
    List<AssignmentModel> assignments,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.calendar_month, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'عرض التقويم',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم إضافة هذه الميزة قريباً',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  /// بناء عرض الخط الزمني (مبسط)
  Widget _buildTimelineView(
    BuildContext context,
    List<AssignmentModel> assignments,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.timeline, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'عرض الخط الزمني',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيتم إضافة هذه الميزة قريباً',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  /// بناء شريط الإحصائيات
  Widget _buildStatsBar(
    BuildContext context,
    int total,
    int pending,
    int overdue,
  ) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.1),
            Theme.of(context).primaryColor.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.2),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            icon: Icons.assignment,
            label: 'المجموع',
            value: total.toString(),
            color: Colors.blue,
          ),
          _buildStatItem(
            icon: Icons.pending_actions,
            label: 'مطلوبة',
            value: pending.toString(),
            color: Colors.orange,
          ),
          _buildStatItem(
            icon: Icons.warning,
            label: 'متأخرة',
            value: overdue.toString(),
            color: Colors.red,
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائية واحد
  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
      ],
    );
  }

  /// بناء الدوال المساعدة المتبقية

  /// عرض حالة عدم وجود واجبات للفلتر المحدد
  Widget _buildNoAssignmentsForFilter(BuildContext context) {
    final currentFilter = ref.watch(assignmentFilterProvider);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.filter_list_off, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد واجبات في "${currentFilter.arabicName}"',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير الفلتر أو البحث عن شيء آخر',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // إعادة تعيين الفلتر للكل
              ref.read(assignmentFilterProvider.notifier).state =
                  AssignmentFilter.all;
            },
            icon: const Icon(Icons.clear_all),
            label: const Text('عرض جميع الواجبات'),
          ),
        ],
      ),
    );
  }

  /// عرض حالة عدم وجود واجبات عاجلة
  Widget _buildNoUrgentAssignments(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 80,
            color: Colors.green.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'ممتاز! لا توجد واجبات عاجلة',
            style: TextStyle(
              fontSize: 18,
              color: Colors.green.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أنت منظم جداً! جميع واجباتك تحت السيطرة',
            style: TextStyle(fontSize: 14, color: Colors.green.shade500),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.green.shade200),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.lightbulb_outline, color: Colors.green.shade700),
                const SizedBox(width: 8),
                Text(
                  'نصيحة: استمر في هذا التنظيم الرائع!',
                  style: TextStyle(
                    color: Colors.green.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// عرض حالة عدم وجود واجبات متأخرة
  Widget _buildNoOverdueAssignments(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.emoji_events, size: 80, color: Colors.amber.shade600),
          const SizedBox(height: 16),
          Text(
            'رائع! لا توجد واجبات متأخرة',
            style: TextStyle(
              fontSize: 18,
              color: Colors.amber.shade700,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أنت طالب ملتزم! استمر في هذا الأداء المميز',
            style: TextStyle(fontSize: 14, color: Colors.amber.shade600),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.amber.shade50, Colors.orange.shade50],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.amber.shade200),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.star, color: Colors.amber.shade700),
                    const SizedBox(width: 8),
                    Text(
                      'إنجاز ممتاز!',
                      style: TextStyle(
                        color: Colors.amber.shade700,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'التزامك بالمواعيد يدل على مسؤوليتك العالية',
                  style: TextStyle(color: Colors.amber.shade600, fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقات الإحصائيات المتقدمة

  /// بطاقة الإحصائيات العامة
  Widget _buildGeneralStatsCard(BuildContext context, AssignmentStats stats) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              Theme.of(context).primaryColor.withOpacity(0.1),
              Theme.of(context).primaryColor.withOpacity(0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: Theme.of(context).primaryColor,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'الإحصائيات العامة',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // الصف الأول من الإحصائيات
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي الواجبات',
                    stats.totalAssignments.toString(),
                    Icons.assignment,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'مطلوبة',
                    stats.pendingAssignments.toString(),
                    Icons.pending_actions,
                    Colors.orange,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // الصف الثاني من الإحصائيات
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'متأخرة',
                    stats.overdueAssignments.toString(),
                    Icons.warning,
                    Colors.red,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'عاجلة',
                    stats.urgentAssignments.toString(),
                    Icons.priority_high,
                    Colors.deepOrange,
                  ),
                ),
              ],
            ),

            if (stats.averageTimeRemaining.inMinutes > 0) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.schedule, color: Colors.blue.shade700),
                    const SizedBox(width: 8),
                    Text(
                      'متوسط الوقت المتبقي: ${_formatDuration(stats.averageTimeRemaining)}',
                      style: TextStyle(
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بطاقة إحصائية صغيرة
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بطاقة توزيع الحالات
  Widget _buildStatusDistributionCard(
    BuildContext context,
    AssignmentStats stats,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.pie_chart, color: Colors.purple.shade600, size: 28),
                const SizedBox(width: 12),
                Text(
                  'توزيع الحالات',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple.shade600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            if (stats.totalAssignments > 0) ...[
              _buildStatusBar(
                'مطلوبة',
                stats.pendingAssignments,
                stats.totalAssignments,
                Colors.orange,
              ),
              const SizedBox(height: 12),
              _buildStatusBar(
                'متأخرة',
                stats.overdueAssignments,
                stats.totalAssignments,
                Colors.red,
              ),
              const SizedBox(height: 12),
              _buildStatusBar(
                'عاجلة',
                stats.urgentAssignments,
                stats.totalAssignments,
                Colors.deepOrange,
              ),
            ] else ...[
              Center(
                child: Text(
                  'لا توجد بيانات للعرض',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 16),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// شريط حالة واحد
  Widget _buildStatusBar(String label, int count, int total, Color color) {
    final percentage = total > 0 ? (count / total * 100) : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: TextStyle(fontWeight: FontWeight.w500, color: color),
            ),
            Text(
              '$count (${percentage.toStringAsFixed(1)}%)',
              style: TextStyle(fontWeight: FontWeight.bold, color: color),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: percentage / 100,
          backgroundColor: color.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 8,
        ),
      ],
    );
  }

  /// بطاقة توزيع المواد
  Widget _buildSubjectDistributionCard(
    BuildContext context,
    AssignmentStats stats,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.subject, color: Colors.green.shade600, size: 28),
                const SizedBox(width: 12),
                Text(
                  'توزيع المواد',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            if (stats.assignmentsBySubject.isNotEmpty) ...[
              ...stats.assignmentsBySubject.entries.take(5).map((entry) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: _buildSubjectBar(
                    entry.key,
                    entry.value,
                    stats.totalAssignments,
                    Colors.green.shade600,
                  ),
                );
              }).toList(),

              if (stats.subjectWithMostAssignments != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.star, color: Colors.green.shade700),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'أكثر المواد واجبات: ${stats.subjectWithMostAssignments}',
                          style: TextStyle(
                            color: Colors.green.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ] else ...[
              Center(
                child: Text(
                  'لا توجد بيانات للعرض',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 16),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// شريط مادة واحدة
  Widget _buildSubjectBar(String subject, int count, int total, Color color) {
    final percentage = total > 0 ? (count / total * 100) : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                subject,
                style: TextStyle(fontWeight: FontWeight.w500, color: color),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Text(
              '$count (${percentage.toStringAsFixed(1)}%)',
              style: TextStyle(fontWeight: FontWeight.bold, color: color),
            ),
          ],
        ),
        const SizedBox(height: 6),
        LinearProgressIndicator(
          value: percentage / 100,
          backgroundColor: color.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 6,
        ),
      ],
    );
  }

  /// بطاقة توزيع الأولوية
  Widget _buildPriorityDistributionCard(
    BuildContext context,
    AssignmentStats stats,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.priority_high, color: Colors.red.shade600, size: 28),
                const SizedBox(width: 12),
                Text(
                  'توزيع الأولوية',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            if (stats.assignmentsByPriority.isNotEmpty) ...[
              ...AssignmentPriority.values.map((priority) {
                final count =
                    stats.assignmentsByPriority[priority.arabicName] ?? 0;
                if (count > 0) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: _buildPriorityBar(
                      priority.arabicName,
                      count,
                      stats.totalAssignments,
                      priority.color,
                      priority.icon,
                    ),
                  );
                }
                return const SizedBox.shrink();
              }).toList(),
            ] else ...[
              Center(
                child: Text(
                  'لا توجد بيانات للعرض',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 16),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// شريط أولوية واحدة
  Widget _buildPriorityBar(
    String priority,
    int count,
    int total,
    Color color,
    IconData icon,
  ) {
    final percentage = total > 0 ? (count / total * 100) : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 16),
                const SizedBox(width: 8),
                Text(
                  priority,
                  style: TextStyle(fontWeight: FontWeight.w500, color: color),
                ),
              ],
            ),
            Text(
              '$count (${percentage.toStringAsFixed(1)}%)',
              style: TextStyle(fontWeight: FontWeight.bold, color: color),
            ),
          ],
        ),
        const SizedBox(height: 6),
        LinearProgressIndicator(
          value: percentage / 100,
          backgroundColor: color.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 6,
        ),
      ],
    );
  }

  /// بطاقة الواجبات القادمة
  Widget _buildUpcomingAssignmentsCard(BuildContext context) {
    final upcomingAssignments = ref.watch(
      upcomingAssignmentsProvider(widget.studentId),
    );

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.upcoming, color: Colors.blue.shade600, size: 28),
                const SizedBox(width: 12),
                Text(
                  'الواجبات القادمة',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            if (upcomingAssignments.isNotEmpty) ...[
              ...upcomingAssignments.take(3).map((assignment) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: _buildUpcomingAssignmentItem(assignment),
                );
              }).toList(),

              if (upcomingAssignments.length > 3) ...[
                const SizedBox(height: 8),
                Center(
                  child: Text(
                    'و ${upcomingAssignments.length - 3} واجبات أخرى...',
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                  ),
                ),
              ],
            ] else ...[
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      size: 48,
                      color: Colors.green.shade400,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لا توجد واجبات قادمة قريباً',
                      style: TextStyle(
                        color: Colors.green.shade600,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// عنصر واجب قادم
  Widget _buildUpcomingAssignmentItem(AssignmentModel assignment) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: assignment.priorityColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: assignment.priorityColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            assignment.priorityIcon,
            color: assignment.priorityColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  assignment.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  assignment.subjectName,
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                assignment.timeRemainingText,
                style: TextStyle(
                  color: assignment.urgencyColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                DateFormat.MMMd('ar').format(assignment.dueDate),
                style: TextStyle(color: Colors.grey.shade600, fontSize: 10),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// الدوال المساعدة للأيقونات والتنسيق

  /// الحصول على أيقونة نمط الترتيب
  IconData _getSortModeIcon(AssignmentSortMode mode) {
    switch (mode) {
      case AssignmentSortMode.dueDate:
        return Icons.schedule;
      case AssignmentSortMode.priority:
        return Icons.priority_high;
      case AssignmentSortMode.subject:
        return Icons.subject;
      case AssignmentSortMode.urgency:
        return Icons.warning;
      case AssignmentSortMode.createdDate:
        return Icons.date_range;
    }
  }

  /// الحصول على أيقونة نمط العرض
  IconData _getViewModeIcon(AssignmentViewMode mode) {
    switch (mode) {
      case AssignmentViewMode.list:
        return Icons.list;
      case AssignmentViewMode.grid:
        return Icons.grid_view;
      case AssignmentViewMode.calendar:
        return Icons.calendar_month;
      case AssignmentViewMode.timeline:
        return Icons.timeline;
    }
  }

  /// تنسيق المدة الزمنية للعرض
  String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays} يوم';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} ساعة';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes} دقيقة';
    } else {
      return 'أقل من دقيقة';
    }
  }

  /// عرض حوار البحث
  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.search, color: Colors.blue),
                SizedBox(width: 8),
                Text('البحث في الواجبات'),
              ],
            ),
            content: TextField(
              autofocus: true,
              decoration: const InputDecoration(
                hintText: 'ابحث في العناوين، الأوصاف، أو أسماء المعلمين...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (query) {
                ref.read(studentAssignmentSearchQueryProvider.notifier).state =
                    query;
              },
            ),
            actions: [
              TextButton(
                onPressed: () {
                  ref
                      .read(studentAssignmentSearchQueryProvider.notifier)
                      .state = '';
                  Navigator.of(context).pop();
                },
                child: const Text('مسح'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  /// بناء الزر العائم
  Widget _buildFloatingActionButton(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () {
        // إعادة تحميل البيانات
        ref.invalidate(studentAssignmentsStreamProvider(widget.studentId));

        // إظهار رسالة تأكيد
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث الواجبات'),
            duration: Duration(seconds: 2),
          ),
        );
      },
      icon: const Icon(Icons.refresh),
      label: const Text('تحديث'),
      backgroundColor: Theme.of(context).primaryColor,
    );
  }
}

/// بطاقة عرض الواجب المدرسي مع إمكانية النقر لعرض التفاصيل
class AssignmentCard extends ConsumerStatefulWidget {
  final AssignmentModel assignment;
  final String studentId;
  final Duration animationDelay;

  const AssignmentCard({
    super.key,
    required this.assignment,
    required this.studentId,
    this.animationDelay = Duration.zero,
  });

  @override
  ConsumerState<AssignmentCard> createState() => _AssignmentCardState();
}

class _AssignmentCardState extends ConsumerState<AssignmentCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة للكارد
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    // بدء الرسوم المتحركة مع التأخير
    Future.delayed(widget.animationDelay, () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // مراقبة حالة الواجب من الـ controller
    final cardState = ref.watch(
      assignmentCardControllerProvider((
        studentId: widget.studentId,
        assignment: widget.assignment,
      )),
    );

    // تنسيق التواريخ
    final formattedDueDate = DateFormat.yMMMMd(
      'ar',
    ).format(widget.assignment.dueDate);
    final now = DateTime.now();
    final isOverdue = now.isAfter(widget.assignment.dueDate);
    final timeRemaining = widget.assignment.dueDate.difference(now);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Container(
              margin: const EdgeInsets.only(bottom: 16),
              child: Material(
                elevation: 4,
                borderRadius: BorderRadius.circular(16),
                child: InkWell(
                  // جعل الكارد قابلة للنقر
                  onTap: () => _navigateToDetails(context),
                  borderRadius: BorderRadius.circular(16),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [Colors.white, Colors.grey.shade50],
                      ),
                      // إضافة حدود ملونة حسب الأولوية
                      border: Border.all(
                        color: widget.assignment.priorityColor.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // الصف الأول: العنوان مع أيقونة الأولوية
                          Row(
                            children: [
                              // أيقونة الأولوية
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: widget.assignment.priorityColor
                                      .withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  widget.assignment.priorityIcon,
                                  color: widget.assignment.priorityColor,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 12),

                              // العنوان
                              Expanded(
                                child: Text(
                                  widget.assignment.title,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                    color: Colors.black87,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),

                              // أيقونة المرفقات (إذا كانت متوفرة)
                              if (widget.assignment.attachments.isNotEmpty)
                                Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(
                                        Icons.attach_file,
                                        color: Colors.blue,
                                        size: 16,
                                      ),
                                      const SizedBox(width: 2),
                                      Text(
                                        '${widget.assignment.attachments.length}',
                                        style: const TextStyle(
                                          color: Colors.blue,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),

                          const SizedBox(height: 12),

                          // معلومات المادة والمعلم
                          Row(
                            children: [
                              Icon(
                                Icons.subject,
                                size: 16,
                                color: Colors.grey.shade600,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                widget.assignment.subjectName,
                                style: TextStyle(
                                  color: Colors.grey.shade700,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              if (widget.assignment.teacherName != null) ...[
                                const SizedBox(width: 16),
                                Icon(
                                  Icons.person,
                                  size: 16,
                                  color: Colors.grey.shade600,
                                ),
                                const SizedBox(width: 6),
                                Expanded(
                                  child: Text(
                                    widget.assignment.teacherName!,
                                    style: TextStyle(
                                      color: Colors.grey.shade700,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ],
                          ),

                          const SizedBox(height: 8),

                          // تاريخ التسليم مع الوقت المتبقي
                          Row(
                            children: [
                              Icon(
                                Icons.schedule,
                                size: 16,
                                color: isOverdue ? Colors.red : Colors.orange,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                'التسليم: $formattedDueDate',
                                style: TextStyle(
                                  color: isOverdue ? Colors.red : Colors.orange,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const Spacer(),

                              // عرض الوقت المتبقي
                              if (!isOverdue && timeRemaining.inDays <= 7)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color:
                                        timeRemaining.inDays <= 1
                                            ? Colors.red.withOpacity(0.1)
                                            : Colors.orange.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    timeRemaining.inDays > 0
                                        ? 'متبقي ${timeRemaining.inDays} يوم'
                                        : timeRemaining.inHours > 0
                                        ? 'متبقي ${timeRemaining.inHours} ساعة'
                                        : 'متبقي ${timeRemaining.inMinutes} دقيقة',
                                    style: TextStyle(
                                      fontSize: 11,
                                      fontWeight: FontWeight.bold,
                                      color:
                                          timeRemaining.inDays <= 1
                                              ? Colors.red
                                              : Colors.orange,
                                    ),
                                  ),
                                ),
                            ],
                          ),

                          // الوصف المختصر (إذا كان متوفراً)
                          if (widget.assignment.description.isNotEmpty) ...[
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade100,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                widget.assignment.description,
                                style: TextStyle(
                                  color: Colors.grey.shade700,
                                  fontSize: 13,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],

                          const SizedBox(height: 16),

                          // الصف السفلي: حالة التسليم والإجراءات
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // حالة التسليم
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: cardState.statusColor,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      cardState.isSubmitted
                                          ? Icons.check_circle
                                          : isOverdue
                                          ? Icons.warning
                                          : Icons.pending,
                                      size: 16,
                                      color:
                                          cardState.isSubmitted
                                              ? Colors.green.shade700
                                              : isOverdue
                                              ? Colors.red.shade700
                                              : Colors.orange.shade700,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      cardState.statusText,
                                      style: TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 12,
                                        color:
                                            cardState.isSubmitted
                                                ? Colors.green.shade700
                                                : isOverdue
                                                ? Colors.red.shade700
                                                : Colors.orange.shade700,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // زر عرض التفاصيل
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: Theme.of(
                                    context,
                                  ).primaryColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Theme.of(
                                      context,
                                    ).primaryColor.withOpacity(0.3),
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      'عرض التفاصيل',
                                      style: TextStyle(
                                        color: Theme.of(context).primaryColor,
                                        fontWeight: FontWeight.w600,
                                        fontSize: 12,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    Icon(
                                      Icons.arrow_forward_ios,
                                      size: 12,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// الانتقال إلى شاشة تفاصيل الواجب
  void _navigateToDetails(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => AssignmentDetailsScreen(
              assignment: widget.assignment,
              studentId: widget.studentId,
            ),
      ),
    );
  }
}

//======================================================================
// بطاقة الواجب المحسنة - Enhanced Assignment Card
//======================================================================

/// بطاقة واجب محسنة مع مميزات متقدمة وتصميم عصري
/// تدعم الرسوم المتحركة والألوان الذكية والمعلومات التفصيلية
class EnhancedAssignmentCard extends ConsumerStatefulWidget {
  final AssignmentModel assignment;
  final String studentId;
  final Duration animationDelay;
  final bool isUrgent;
  final bool isOverdue;

  const EnhancedAssignmentCard({
    super.key,
    required this.assignment,
    required this.studentId,
    this.animationDelay = Duration.zero,
    this.isUrgent = false,
    this.isOverdue = false,
  });

  @override
  ConsumerState<EnhancedAssignmentCard> createState() =>
      _EnhancedAssignmentCardState();
}

class _EnhancedAssignmentCardState extends ConsumerState<EnhancedAssignmentCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة المتقدمة
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
      ),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
      ),
    );

    // بدء الرسوم المتحركة مع التأخير
    Future.delayed(widget.animationDelay, () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // مراقبة حالة الواجب المحسنة
    final cardState = ref.watch(
      assignmentCardControllerProvider((
        studentId: widget.studentId,
        assignment: widget.assignment,
      )),
    );

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Container(
                margin: const EdgeInsets.only(bottom: 16),
                child: Material(
                  elevation: widget.isUrgent ? 8 : 4,
                  borderRadius: BorderRadius.circular(20),
                  shadowColor: widget.assignment.priorityColor.withOpacity(0.3),
                  child: InkWell(
                    onTap: () => _navigateToDetails(context),
                    borderRadius: BorderRadius.circular(20),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white,
                            widget.assignment.priorityColor.withOpacity(0.02),
                          ],
                        ),
                        // شريط جانبي ملون حسب الأولوية
                        border: Border(
                          right: BorderSide(
                            color: widget.assignment.priorityColor,
                            width: 6,
                          ),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // الصف العلوي: الأولوية والحالة
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // شارة الأولوية
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6,
                                  ),
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        widget.assignment.priorityColor,
                                        widget.assignment.priorityColor
                                            .withOpacity(0.8),
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: widget.assignment.priorityColor
                                            .withOpacity(0.3),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        widget.assignment.priorityIcon,
                                        color: Colors.white,
                                        size: 16,
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        widget.assignment.priority.arabicName,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                // شارة الحالة
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: cardState.statusColor,
                                    borderRadius: BorderRadius.circular(15),
                                    border: Border.all(
                                      color: _getStatusBorderColor(
                                        cardState.status,
                                      ),
                                      width: 1.5,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        _getStatusIcon(cardState.status),
                                        size: 14,
                                        color: _getStatusBorderColor(
                                          cardState.status,
                                        ),
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        cardState.statusText,
                                        style: TextStyle(
                                          fontSize: 11,
                                          fontWeight: FontWeight.bold,
                                          color: _getStatusBorderColor(
                                            cardState.status,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),

                            const SizedBox(height: 16),

                            // العنوان الرئيسي
                            Text(
                              widget.assignment.title,
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                                height: 1.3,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),

                            const SizedBox(height: 12),

                            // معلومات المادة والمعلم
                            Row(
                              children: [
                                // أيقونة المادة
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Icon(
                                    Icons.subject,
                                    color: Colors.blue.shade700,
                                    size: 18,
                                  ),
                                ),
                                const SizedBox(width: 12),

                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        widget.assignment.subjectName,
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.blue.shade700,
                                        ),
                                      ),
                                      if (widget.assignment.teacherName !=
                                          null) ...[
                                        const SizedBox(height: 2),
                                        Text(
                                          'المعلم: ${widget.assignment.teacherName}',
                                          style: TextStyle(
                                            fontSize: 13,
                                            color: Colors.grey.shade600,
                                          ),
                                        ),
                                      ],
                                    ],
                                  ),
                                ),

                                // عدد المرفقات
                                if (widget.assignment.attachments.isNotEmpty)
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.green.withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.attach_file,
                                          color: Colors.green.shade700,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          '${widget.assignment.attachments.length}',
                                          style: TextStyle(
                                            color: Colors.green.shade700,
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),

                            const SizedBox(height: 16),

                            // معلومات التاريخ والوقت المتبقي
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    widget.assignment.urgencyColor.withOpacity(
                                      0.1,
                                    ),
                                    widget.assignment.urgencyColor.withOpacity(
                                      0.05,
                                    ),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: widget.assignment.urgencyColor
                                      .withOpacity(0.3),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.schedule,
                                    color: widget.assignment.urgencyColor,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 12),

                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'موعد التسليم',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey.shade600,
                                          ),
                                        ),
                                        const SizedBox(height: 2),
                                        Text(
                                          DateFormat.yMMMMEEEEd(
                                            'ar',
                                          ).format(widget.assignment.dueDate),
                                          style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                            color:
                                                widget.assignment.urgencyColor,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  // مؤشر الوقت المتبقي
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      color: widget.assignment.urgencyColor,
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: widget.assignment.urgencyColor
                                              .withOpacity(0.3),
                                          blurRadius: 4,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Text(
                                      widget.assignment.timeRemainingText,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // الوصف (إذا كان متوفراً)
                            if (widget.assignment.description.isNotEmpty) ...[
                              const SizedBox(height: 12),
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade50,
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    color: Colors.grey.shade200,
                                  ),
                                ),
                                child: Text(
                                  widget.assignment.description,
                                  style: TextStyle(
                                    color: Colors.grey.shade700,
                                    fontSize: 14,
                                    height: 1.4,
                                  ),
                                  maxLines: 3,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],

                            const SizedBox(height: 16),

                            // الصف السفلي: الإجراءات
                            Row(
                              children: [
                                // زر التفاصيل
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed:
                                        () => _navigateToDetails(context),
                                    icon: const Icon(
                                      Icons.visibility,
                                      size: 18,
                                    ),
                                    label: const Text('عرض التفاصيل'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          Theme.of(context).primaryColor,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 12,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      elevation: 2,
                                    ),
                                  ),
                                ),

                                const SizedBox(width: 12),

                                // زر الإجراء السريع (حسب الحالة)
                                if (cardState.canSubmit &&
                                    !cardState.isSubmitted)
                                  ElevatedButton.icon(
                                    onPressed:
                                        cardState.isLoading
                                            ? null
                                            : () => _quickSubmit(context),
                                    icon:
                                        cardState.isLoading
                                            ? const SizedBox(
                                              width: 16,
                                              height: 16,
                                              child: CircularProgressIndicator(
                                                strokeWidth: 2,
                                              ),
                                            )
                                            : const Icon(
                                              Icons.upload,
                                              size: 18,
                                            ),
                                    label: Text(
                                      cardState.isLoading ? 'جاري...' : 'تسليم',
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 12,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                              ],
                            ),

                            // رسالة خطأ (إذا كانت موجودة)
                            if (cardState.submissionError != null) ...[
                              const SizedBox(height: 8),
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.red.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.red.shade200,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.error_outline,
                                      color: Colors.red.shade700,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        cardState.submissionError!,
                                        style: TextStyle(
                                          color: Colors.red.shade700,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                    IconButton(
                                      onPressed: () {
                                        ref
                                            .read(
                                              assignmentCardControllerProvider((
                                                studentId: widget.studentId,
                                                assignment: widget.assignment,
                                              )).notifier,
                                            )
                                            .clearError();
                                      },
                                      icon: Icon(
                                        Icons.close,
                                        color: Colors.red.shade700,
                                        size: 16,
                                      ),
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// الحصول على أيقونة الحالة
  IconData _getStatusIcon(AssignmentStatus status) {
    switch (status) {
      case AssignmentStatus.pending:
        return Icons.pending_actions;
      case AssignmentStatus.submitted:
        return Icons.check_circle;
      case AssignmentStatus.overdue:
        return Icons.warning;
      case AssignmentStatus.graded:
        return Icons.grade;
      case AssignmentStatus.returned:
        return Icons.refresh;
    }
  }

  /// الحصول على لون حدود الحالة
  Color _getStatusBorderColor(AssignmentStatus status) {
    switch (status) {
      case AssignmentStatus.pending:
        return Colors.orange.shade700;
      case AssignmentStatus.submitted:
        return Colors.green.shade700;
      case AssignmentStatus.overdue:
        return Colors.red.shade700;
      case AssignmentStatus.graded:
        return Colors.blue.shade700;
      case AssignmentStatus.returned:
        return Colors.purple.shade700;
    }
  }

  /// الانتقال إلى صفحة التفاصيل
  void _navigateToDetails(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => AssignmentDetailsScreen(
              assignment: widget.assignment,
              studentId: widget.studentId,
            ),
      ),
    );
  }

  /// تسليم سريع (مبسط)
  void _quickSubmit(BuildContext context) {
    // هنا يمكن إضافة منطق التسليم السريع
    // مثل فتح حوار لاختيار ملف أو كتابة ملاحظة
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تسليم سريع'),
            content: const Text(
              'هذه الميزة قيد التطوير. استخدم "عرض التفاصيل" للتسليم الكامل.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _navigateToDetails(context);
                },
                child: const Text('عرض التفاصيل'),
              ),
            ],
          ),
    );
  }
}

//======================================================================
// بطاقة الواجب المدمجة - Compact Assignment Card
//======================================================================

/// بطاقة واجب مدمجة مناسبة لعرض الشبكة
/// تحتوي على المعلومات الأساسية فقط مع تصميم مضغوط
class CompactAssignmentCard extends ConsumerStatefulWidget {
  final AssignmentModel assignment;
  final String studentId;
  final Duration animationDelay;

  const CompactAssignmentCard({
    super.key,
    required this.assignment,
    required this.studentId,
    this.animationDelay = Duration.zero,
  });

  @override
  ConsumerState<CompactAssignmentCard> createState() =>
      _CompactAssignmentCardState();
}

class _CompactAssignmentCardState extends ConsumerState<CompactAssignmentCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    Future.delayed(widget.animationDelay, () {
      if (mounted) {
        _animationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cardState = ref.watch(
      assignmentCardControllerProvider((
        studentId: widget.studentId,
        assignment: widget.assignment,
      )),
    );

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Material(
              elevation: 3,
              borderRadius: BorderRadius.circular(16),
              child: InkWell(
                onTap: () => _navigateToDetails(context),
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        widget.assignment.priorityColor.withOpacity(0.1),
                        widget.assignment.priorityColor.withOpacity(0.05),
                      ],
                    ),
                    border: Border.all(
                      color: widget.assignment.priorityColor.withOpacity(0.3),
                      width: 1.5,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // الصف العلوي: الأولوية والحالة
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: widget.assignment.priorityColor,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                widget.assignment.priorityIcon,
                                color: Colors.white,
                                size: 14,
                              ),
                            ),
                            Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: cardState.statusColor.withOpacity(0.8),
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: _getStatusBorderColor(
                                    cardState.status,
                                  ),
                                  width: 2,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 12),

                        // العنوان
                        Text(
                          widget.assignment.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 8),

                        // المادة
                        Text(
                          widget.assignment.subjectName,
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey.shade600,
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const Spacer(),

                        // التاريخ والوقت المتبقي
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: widget.assignment.urgencyColor.withOpacity(
                              0.1,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.schedule,
                                    size: 14,
                                    color: widget.assignment.urgencyColor,
                                  ),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      DateFormat.MMMd(
                                        'ar',
                                      ).format(widget.assignment.dueDate),
                                      style: TextStyle(
                                        fontSize: 11,
                                        color: widget.assignment.urgencyColor,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                widget.assignment.timeRemainingText,
                                style: TextStyle(
                                  fontSize: 10,
                                  color: widget.assignment.urgencyColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 8),

                        // حالة التسليم
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(vertical: 6),
                          decoration: BoxDecoration(
                            color: cardState.statusColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            cardState.statusText,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.bold,
                              color: _getStatusBorderColor(cardState.status),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// الحصول على لون حدود الحالة
  Color _getStatusBorderColor(AssignmentStatus status) {
    switch (status) {
      case AssignmentStatus.pending:
        return Colors.orange.shade700;
      case AssignmentStatus.submitted:
        return Colors.green.shade700;
      case AssignmentStatus.overdue:
        return Colors.red.shade700;
      case AssignmentStatus.graded:
        return Colors.blue.shade700;
      case AssignmentStatus.returned:
        return Colors.purple.shade700;
    }
  }

  /// الانتقال إلى صفحة التفاصيل
  void _navigateToDetails(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => AssignmentDetailsScreen(
              assignment: widget.assignment,
              studentId: widget.studentId,
            ),
      ),
    );
  }
}
