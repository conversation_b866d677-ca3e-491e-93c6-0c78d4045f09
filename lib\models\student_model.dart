import 'package:cloud_firestore/cloud_firestore.dart';

/// يمثل هذا الكائن طالباً بجميع خصائصه الأساسية والإضافية
/// تم تحديث النموذج ليتوافق مع متطلبات المدارس اليمنية
class StudentModel {
  // ===== الحقول الأساسية المطلوبة =====
  final String id; // المعرّف الفريد من Firebase Auth
  final String name; // الاسم الكامل للطالب
  final String email; // البريد الإلكتروني للطالب
  final String studentNumber; // الرقم الأكاديمي للطالب
  final String studentClass; // اسم الصف (مثل: الصف السادس أ)
  final String? classId; // معرّف الصف في قاعدة البيانات
  final Timestamp createdAt; // تاريخ إنشاء السجل
  final String? gender; // الجنس (ذكر/أنثى)
  final bool isActive; // حالة نشاط الطالب

  // ===== الحقول الشخصية =====
  final DateTime? dateOfBirth; // تاريخ الميلاد
  final String? address; // العنوان التفصيلي
  final String? phoneNumber; // رقم هاتف الطالب (إن وجد)

  // ===== الحقول الجديدة المطلوبة للمدارس اليمنية =====
  final String? nationalId; // الرقم الوطني (اختياري)
  final String guardianName; // اسم ولي الأمر (مطلوب)
  final String? guardianPhone; // رقم هاتف ولي الأمر (اختياري)
  final String governorate; // المحافظة (مطلوب)
  final String nationality; // الجنسية (افتراضي: يمني)
  final String? bloodType; // فصيلة الدم (اختياري)
  final String? healthCondition; // الحالة الصحية أو الأمراض المزمنة (اختياري)
  final String? notes; // ملاحظات خاصة (اختياري)

  // ===== الحقول المتعلقة بالصور =====
  final String? imageUrl; // رابط الصورة الشخصية (قديم)
  final String? profileImageUrl; // رابط الصورة الشخصية (جديد)

  StudentModel({
    // ===== الحقول الأساسية المطلوبة =====
    required this.id,
    required this.name,
    required this.email,
    required this.studentNumber,
    required this.studentClass,
    this.classId,
    required this.createdAt,
    this.gender,
    this.isActive = true,

    // ===== الحقول الشخصية =====
    this.dateOfBirth,
    this.address,
    this.phoneNumber,

    // ===== الحقول الجديدة المطلوبة للمدارس اليمنية =====
    this.nationalId, // الرقم الوطني (اختياري)
    required this.guardianName, // اسم ولي الأمر (مطلوب)
    this.guardianPhone, // رقم هاتف ولي الأمر (اختياري)
    required this.governorate, // المحافظة (مطلوب)
    this.nationality = 'يمني', // الجنسية (افتراضي: يمني)
    this.bloodType, // فصيلة الدم (اختياري)
    this.healthCondition, // الحالة الصحية (اختياري)
    this.notes, // ملاحظات خاصة (اختياري)
    // ===== الحقول المتعلقة بالصور =====
    this.imageUrl,
    this.profileImageUrl,
  });

  /// دالة مصنعية محدثة لإنشاء كائن StudentModel من Map
  /// تدعم جميع الحقول الجديدة المطلوبة للمدارس اليمنية
  factory StudentModel.fromMap(Map<String, dynamic> data, String documentId) {
    return StudentModel(
      // ===== الحقول الأساسية =====
      id: documentId,
      name: data['name'] as String? ?? 'اسم غير متوفر',
      email: data['email'] as String? ?? 'بريد إلكتروني غير متوفر',
      studentNumber: data['student_number'] as String? ?? 'رقم غير متوفر',
      studentClass: data['class'] as String? ?? 'صف غير محدد',
      classId: data['classId'] as String?,
      createdAt: data['created_at'] as Timestamp? ?? Timestamp.now(),
      gender: data['gender'] as String?,
      isActive: data['isActive'] as bool? ?? true,

      // ===== الحقول الشخصية =====
      dateOfBirth:
          data['dateOfBirth'] != null
              ? (data['dateOfBirth'] as Timestamp).toDate()
              : null,
      address: data['address'] as String?,
      phoneNumber: data['phoneNumber'] as String?,

      // ===== الحقول الجديدة للمدارس اليمنية =====
      nationalId: data['nationalId'] as String?, // الرقم الوطني (اختياري)
      guardianName:
          data['guardianName'] as String? ??
          'ولي أمر غير محدد', // اسم ولي الأمر (مطلوب)
      guardianPhone:
          data['guardianPhone'] as String?, // رقم هاتف ولي الأمر (اختياري)
      governorate:
          data['governorate'] as String? ?? 'غير محدد', // المحافظة (مطلوب)
      nationality:
          data['nationality'] as String? ?? 'يمني', // الجنسية (افتراضي: يمني)
      bloodType: data['bloodType'] as String?, // فصيلة الدم (اختياري)
      healthCondition:
          data['healthCondition'] as String?, // الحالة الصحية (اختياري)
      notes: data['notes'] as String?, // ملاحظات خاصة (اختياري)
      // ===== الحقول المتعلقة بالصور =====
      imageUrl: data['imageUrl'] as String?,
      profileImageUrl: data['profileImageUrl'] as String?,
    );
  }

  /// دالة مصنعية محدثة لإنشاء الكائن من DocumentSnapshot مباشرة
  /// تدعم جميع الحقول الجديدة المطلوبة للمدارس اليمنية
  factory StudentModel.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>? ?? {};
    return StudentModel(
      // ===== الحقول الأساسية =====
      id: doc.id,
      name: data['name'] as String? ?? 'اسم غير متوفر',
      email: data['email'] as String? ?? 'بريد إلكتروني غير متوفر',
      studentNumber: data['student_number'] as String? ?? 'رقم غير متوفر',
      studentClass: data['class'] as String? ?? 'صف غير محدد',
      classId: data['classId'] as String?,
      createdAt: data['created_at'] as Timestamp? ?? Timestamp.now(),
      gender: data['gender'] as String?,
      isActive: data['isActive'] as bool? ?? true,

      // ===== الحقول الشخصية =====
      dateOfBirth:
          data['dateOfBirth'] != null
              ? (data['dateOfBirth'] as Timestamp).toDate()
              : null,
      address: data['address'] as String?,
      phoneNumber: data['phoneNumber'] as String?,

      // ===== الحقول الجديدة للمدارس اليمنية =====
      nationalId: data['nationalId'] as String?, // الرقم الوطني (اختياري)
      guardianName:
          data['guardianName'] as String? ??
          'ولي أمر غير محدد', // اسم ولي الأمر (مطلوب)
      guardianPhone:
          data['guardianPhone'] as String?, // رقم هاتف ولي الأمر (اختياري)
      governorate:
          data['governorate'] as String? ?? 'غير محدد', // المحافظة (مطلوب)
      nationality:
          data['nationality'] as String? ?? 'يمني', // الجنسية (افتراضي: يمني)
      bloodType: data['bloodType'] as String?, // فصيلة الدم (اختياري)
      healthCondition:
          data['healthCondition'] as String?, // الحالة الصحية (اختياري)
      notes: data['notes'] as String?, // ملاحظات خاصة (اختياري)
      // ===== الحقول المتعلقة بالصور =====
      imageUrl: data['imageUrl'] as String?,
      profileImageUrl: data['profileImageUrl'] as String?,
    );
  }

  /// دالة لتحويل كائن StudentModel إلى Map لحفظه في Firebase
  /// تتضمن جميع الحقول الجديدة المطلوبة للمدارس اليمنية
  Map<String, dynamic> toMap() {
    return {
      // ===== الحقول الأساسية =====
      'name': name,
      'email': email,
      'student_number': studentNumber,
      'class': studentClass,
      'classId': classId,
      'gender': gender,
      'role': 'student', // دور ثابت للطالب
      'isActive': isActive,
      'createdAt': createdAt,

      // ===== الحقول الشخصية =====
      'dateOfBirth':
          dateOfBirth != null ? Timestamp.fromDate(dateOfBirth!) : null,
      'address': address,
      'phoneNumber': phoneNumber,

      // ===== الحقول الجديدة للمدارس اليمنية =====
      'nationalId': nationalId, // الرقم الوطني (اختياري)
      'guardianName': guardianName, // اسم ولي الأمر (مطلوب)
      'guardianPhone': guardianPhone, // رقم هاتف ولي الأمر (اختياري)
      'governorate': governorate, // المحافظة (مطلوب)
      'nationality': nationality, // الجنسية
      'bloodType': bloodType, // فصيلة الدم (اختياري)
      'healthCondition': healthCondition, // الحالة الصحية (اختياري)
      'notes': notes, // ملاحظات خاصة (اختياري)
      // ===== الحقول المتعلقة بالصور =====
      'imageUrl': imageUrl,
      'profileImageUrl': profileImageUrl,
    };
  }
}
