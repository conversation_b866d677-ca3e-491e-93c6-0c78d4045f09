import 'package:flutter/material.dart';
import 'package:school_management_system/shared/app_theme.dart';

/// مجموعة شاملة من المكونات الموحدة لنظام إدارة المدرسة
/// 
/// تحتوي على جميع المكونات الأساسية المطلوبة لبناء واجهات متناسقة
/// مع تطبيق النظام الموحد للألوان والمسافات والخطوط

/// شريط تطبيق موحد للنظام
/// 
/// يطبق التصميم الموحد مع إمكانية تخصيص اللون حسب نوع المستخدم
class UnifiedAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// عنوان شريط التطبيق
  final String title;
  
  /// نوع المستخدم لتحديد اللون المناسب
  final UserType userType;
  
  /// الإجراءات في شريط التطبيق (اختياري)
  final List<Widget>? actions;
  
  /// إظهار زر الرجوع (افتراضي: تلقائي)
  final bool? automaticallyImplyLeading;
  
  /// إجراء مخصص للزر الأيسر (اختياري)
  final Widget? leading;
  
  /// توسيط العنوان (افتراضي: true)
  final bool centerTitle;

  const UnifiedAppBar({
    super.key,
    required this.title,
    required this.userType,
    this.actions,
    this.automaticallyImplyLeading,
    this.leading,
    this.centerTitle = true,
  });

  @override
  Widget build(BuildContext context) {
    final backgroundColor = _getUserColor(userType);
    
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontSize: AppTextSizes.headlineMedium,
          fontWeight: FontWeight.bold,
          color: AppColors.textOnPrimary,
        ),
      ),
      backgroundColor: backgroundColor,
      foregroundColor: AppColors.textOnPrimary,
      elevation: AppElevation.medium,
      centerTitle: centerTitle,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: leading,
      actions: actions ?? [
        // زر الإشعارات الافتراضي
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          tooltip: 'الإشعارات',
          onPressed: () {
            // TODO: فتح صفحة الإشعارات
          },
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  /// الحصول على لون المستخدم
  Color _getUserColor(UserType type) {
    switch (type) {
      case UserType.student:
        return AppColors.studentColor;
      case UserType.parent:
        return AppColors.parentColor;
      case UserType.teacher:
        return AppColors.teacherColor;
      case UserType.admin:
        return AppColors.adminColor;
      case UserType.general:
        return AppColors.primary;
    }
  }
}

/// أنواع المستخدمين في النظام
enum UserType {
  student,  // طالب
  parent,   // ولي أمر
  teacher,  // معلم
  admin,    // إدارة
  general,  // عام
}

/// بطاقة إحصائية موحدة
/// 
/// تعرض رقم إحصائي مع عنوان وأيقونة بتصميم موحد
class StatCard extends StatelessWidget {
  /// العنوان الرئيسي
  final String title;
  
  /// القيمة الإحصائية
  final String value;
  
  /// الأيقونة
  final IconData icon;
  
  /// اللون المميز (اختياري - افتراضي: أساسي)
  final Color? color;
  
  /// دالة تُستدعى عند الضغط (اختياري)
  final VoidCallback? onTap;
  
  /// نص إضافي أسفل القيمة (اختياري)
  final String? subtitle;

  const StatCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.color,
    this.onTap,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? AppColors.primary;
    
    return Card(
      elevation: AppElevation.medium,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.large),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppBorderRadius.large),
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // الأيقونة
              Container(
                padding: const EdgeInsets.all(AppSpacing.md),
                decoration: BoxDecoration(
                  color: effectiveColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppBorderRadius.medium),
                ),
                child: Icon(
                  icon,
                  color: effectiveColor,
                  size: 32,
                ),
              ),
              const SizedBox(height: AppSpacing.md),
              
              // القيمة
              Text(
                value,
                style: TextStyle(
                  fontSize: AppTextSizes.displayMedium,
                  fontWeight: FontWeight.bold,
                  color: effectiveColor,
                ),
              ),
              const SizedBox(height: AppSpacing.xs),
              
              // العنوان
              Text(
                title,
                style: const TextStyle(
                  fontSize: AppTextSizes.bodyLarge,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              
              // النص الإضافي
              if (subtitle != null) ...[
                const SizedBox(height: AppSpacing.xs),
                Text(
                  subtitle!,
                  style: const TextStyle(
                    fontSize: AppTextSizes.bodySmall,
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// قائمة عناصر موحدة
/// 
/// تعرض قائمة من العناصر بتصميم موحد مع إمكانيات تخصيص
class UnifiedListTile extends StatelessWidget {
  /// العنوان الرئيسي
  final String title;
  
  /// العنوان الفرعي (اختياري)
  final String? subtitle;
  
  /// الأيقونة الرئيسية
  final IconData icon;
  
  /// أيقونة الحالة (اختياري)
  final IconData? statusIcon;
  
  /// لون الحالة (اختياري)
  final Color? statusColor;
  
  /// دالة تُستدعى عند الضغط
  final VoidCallback? onTap;
  
  /// إظهار سهم التنقل (افتراضي: true)
  final bool showTrailing;
  
  /// محدد أم لا (افتراضي: false)
  final bool isSelected;

  const UnifiedListTile({
    super.key,
    required this.title,
    required this.icon,
    this.subtitle,
    this.statusIcon,
    this.statusColor,
    this.onTap,
    this.showTrailing = true,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      child: Material(
        color: isSelected 
            ? AppColors.primary.withValues(alpha: 0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(AppBorderRadius.medium),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.md),
            child: Row(
              children: [
                // الأيقونة الرئيسية
                Container(
                  padding: const EdgeInsets.all(AppSpacing.sm),
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? AppColors.primary
                        : AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppBorderRadius.small),
                  ),
                  child: Icon(
                    icon,
                    color: isSelected 
                        ? AppColors.textOnPrimary
                        : AppColors.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                
                // النصوص
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: AppTextSizes.bodyLarge,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                          color: isSelected 
                              ? AppColors.primary
                              : AppColors.textPrimary,
                        ),
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          subtitle!,
                          style: const TextStyle(
                            fontSize: AppTextSizes.bodySmall,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                
                // أيقونة الحالة
                if (statusIcon != null) ...[
                  Icon(
                    statusIcon,
                    color: statusColor ?? AppColors.success,
                    size: 20,
                  ),
                  const SizedBox(width: AppSpacing.sm),
                ],
                
                // سهم التنقل
                if (showTrailing)
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// رأس قسم موحد
/// 
/// يعرض عنوان قسم مع خط فاصل وإمكانية إضافة إجراءات
class SectionHeader extends StatelessWidget {
  /// عنوان القسم
  final String title;
  
  /// وصف القسم (اختياري)
  final String? description;
  
  /// إجراءات القسم (اختياري)
  final List<Widget>? actions;
  
  /// إظهار خط فاصل (افتراضي: true)
  final bool showDivider;

  const SectionHeader({
    super.key,
    required this.title,
    this.description,
    this.actions,
    this.showDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: AppTextSizes.headlineLarge,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  if (description != null) ...[
                    const SizedBox(height: AppSpacing.xs),
                    Text(
                      description!,
                      style: const TextStyle(
                        fontSize: AppTextSizes.bodyMedium,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (actions != null) ...actions!,
          ],
        ),
        if (showDivider) ...[
          const SizedBox(height: AppSpacing.md),
          const Divider(),
        ],
      ],
    );
  }
}

/// شاشة فارغة موحدة
/// 
/// تعرض رسالة عندما لا توجد بيانات مع إمكانية إضافة إجراء
class EmptyState extends StatelessWidget {
  /// الأيقونة
  final IconData icon;
  
  /// العنوان
  final String title;
  
  /// الوصف
  final String description;
  
  /// نص الزر (اختياري)
  final String? actionText;
  
  /// دالة الزر (اختياري)
  final VoidCallback? onAction;

  const EmptyState({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    this.actionText,
    this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.xl),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpacing.xl),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 64,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(height: AppSpacing.lg),
            
            Text(
              title,
              style: const TextStyle(
                fontSize: AppTextSizes.headlineMedium,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.sm),
            
            Text(
              description,
              style: const TextStyle(
                fontSize: AppTextSizes.bodyLarge,
                color: AppColors.textSecondary,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: AppSpacing.lg),
              ElevatedButton.icon(
                onPressed: onAction,
                icon: const Icon(Icons.add),
                label: Text(actionText!),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpacing.lg,
                    vertical: AppSpacing.md,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
