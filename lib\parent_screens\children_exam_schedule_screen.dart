import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/exam_model.dart';
import 'package:school_management_system/providers/exam_providers.dart';
import 'package:school_management_system/shared/app_theme.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة جدول امتحانات الأبناء لأولياء الأمور
///
/// هذه الشاشة تعرض لولي الأمر جداول امتحانات جميع أبنائه
/// مع إمكانية التنقل بين الأطفال ومتابعة امتحاناتهم
///
/// الوظائف الرئيسية:
/// - عرض امتحانات جميع الأبناء في مكان واحد
/// - التنقل بين الأطفال بسهولة
/// - عرض تفاصيل كل امتحان
/// - العد التنازلي للامتحانات القريبة
/// - تذكيرات للأهل بمواعيد الامتحانات
/// - إمكانية إضافة ملاحظات لكل امتحان
/// - مشاركة الجدول مع الطفل أو أفراد العائلة
///
/// أنواع العرض:
/// - عرض حسب الطفل: امتحانات طفل محدد
/// - عرض زمني: امتحانات جميع الأطفال مرتبة زمنياً
/// - عرض اليوم: امتحانات اليوم لجميع الأطفال
/// - عرض الأسبوع: امتحانات الأسبوع لجميع الأطفال
class ChildrenExamScheduleScreen extends ConsumerStatefulWidget {
  /// معرف ولي الأمر
  final String parentId;

  /// اسم ولي الأمر
  final String parentName;

  const ChildrenExamScheduleScreen({
    super.key,
    required this.parentId,
    required this.parentName,
  });

  @override
  ConsumerState<ChildrenExamScheduleScreen> createState() =>
      _ChildrenExamScheduleScreenState();
}

class _ChildrenExamScheduleScreenState
    extends ConsumerState<ChildrenExamScheduleScreen>
    with TickerProviderStateMixin {
  // ===================================================================
  // متحكمات التبويبات والحالة
  // ===================================================================

  /// متحكم التبويبات الرئيسية
  late TabController _tabController;

  /// متحكم التبويبات الفرعية للأطفال
  late TabController _childrenTabController;

  /// فهرس الطفل المحدد حالياً
  int _selectedChildIndex = 0;

  /// نوع العرض المحدد
  ViewType _viewType = ViewType.byChild;

  // ===================================================================
  // بيانات وهمية للاختبار
  // ===================================================================

  /// قائمة الأطفال (بيانات وهمية)
  final List<Map<String, dynamic>> _children = [
    {
      'id': 'child1',
      'name': 'أحمد محمد',
      'grade': 'الصف السادس',
      'section': 'أ',
    },
    {
      'id': 'child2',
      'name': 'فاطمة محمد',
      'grade': 'الصف الثالث',
      'section': 'ب',
    },
    {'id': 'child3', 'name': 'عمر محمد', 'grade': 'الصف الأول', 'section': 'أ'},
  ];

  /// امتحانات وهمية لكل طفل
  late Map<String, List<ExamModel>> _childrenExams;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _childrenTabController = TabController(
      length: _children.length,
      vsync: this,
    );
    _generateMockExams();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _childrenTabController.dispose();
    super.dispose();
  }

  /// إنشاء امتحانات وهمية للاختبار
  void _generateMockExams() {
    final now = DateTime.now();
    _childrenExams = {};

    for (int i = 0; i < _children.length; i++) {
      final childId = _children[i]['id'] as String;
      _childrenExams[childId] = [
        ExamModel(
          id: '${childId}_exam1',
          name: 'امتحان الفصل الأول - الرياضيات',
          academicYear: '2024-2025',
          semester: 'الفصل الأول',
          type: ExamType.midterm,
          startDate: now.add(Duration(days: 2 + i)),
          endDate: now.add(Duration(days: 2 + i, hours: 2)),
          classIds: ['class${i + 1}'],
          status: ExamStatus.scheduled,
          description: 'امتحان نصف الفصل في مادة الرياضيات',
          createdAt: now.subtract(const Duration(days: 10)),
          createdBy: 'teacher1',
          updatedAt: now.subtract(const Duration(days: 1)),
          updatedBy: 'teacher1',
        ),
        ExamModel(
          id: '${childId}_exam2',
          name: 'امتحان الفصل الأول - العلوم',
          academicYear: '2024-2025',
          semester: 'الفصل الأول',
          type: ExamType.midterm,
          startDate: now.add(Duration(days: 5 + i)),
          endDate: now.add(Duration(days: 5 + i, hours: 1, minutes: 30)),
          classIds: ['class${i + 1}'],
          status: ExamStatus.scheduled,
          description: 'امتحان نصف الفصل في مادة العلوم',
          createdAt: now.subtract(const Duration(days: 8)),
          createdBy: 'teacher2',
          updatedAt: now.subtract(const Duration(days: 1)),
          updatedBy: 'teacher2',
        ),
      ];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق
      appBar: AppBar(
        title: const Text(
          'جدول امتحانات الأبناء',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.parentColor,
        elevation: 2,
        iconTheme: const IconThemeData(color: Colors.white),

        // التبويبات الرئيسية
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 13,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.person, size: 20), text: 'حسب الطفل'),
            Tab(icon: Icon(Icons.schedule, size: 20), text: 'زمني'),
            Tab(icon: Icon(Icons.today, size: 20), text: 'اليوم'),
            Tab(icon: Icon(Icons.view_week, size: 20), text: 'الأسبوع'),
          ],
        ),

        // أزرار الإجراءات
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () => _refreshData(),
            tooltip: 'تحديث البيانات',
          ),
          IconButton(
            icon: const Icon(Icons.share, color: Colors.white),
            onPressed: () => _shareSchedule(),
            tooltip: 'مشاركة الجدول',
          ),
        ],
      ),

      // محتوى التبويبات
      body: TabBarView(
        controller: _tabController,
        children: [
          // تبويب حسب الطفل
          _buildByChildTab(),
          // تبويب زمني
          _buildTimelineTab(),
          // تبويب اليوم
          _buildTodayTab(),
          // تبويب الأسبوع
          _buildWeekTab(),
        ],
      ),

      // زر عائم للإجراءات السريعة
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showQuickActions(),
        backgroundColor: AppColors.parentColor,
        icon: const Icon(Icons.add_alert, color: Colors.white),
        label: const Text(
          'إضافة تذكير',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  /// بناء تبويب العرض حسب الطفل
  Widget _buildByChildTab() {
    return Column(
      children: [
        // شريط اختيار الطفل
        Container(
          height: 60,
          color: Colors.grey.shade50,
          child: TabBar(
            controller: _childrenTabController,
            isScrollable: true,
            indicatorColor: AppColors.parentColor,
            labelColor: AppColors.parentColor,
            unselectedLabelColor: Colors.grey,
            labelStyle: const TextStyle(fontWeight: FontWeight.w600),
            tabs:
                _children.map((child) {
                  return Tab(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          child['name'],
                          style: const TextStyle(fontSize: 14),
                        ),
                        Text(
                          child['grade'],
                          style: const TextStyle(fontSize: 11),
                        ),
                      ],
                    ),
                  );
                }).toList(),
          ),
        ),

        // محتوى امتحانات الطفل المحدد
        Expanded(
          child: TabBarView(
            controller: _childrenTabController,
            children:
                _children.map((child) {
                  final childExams = _childrenExams[child['id']] ?? [];
                  return _buildChildExamsList(child, childExams);
                }).toList(),
          ),
        ),
      ],
    );
  }

  /// بناء قائمة امتحانات طفل محدد
  Widget _buildChildExamsList(
    Map<String, dynamic> child,
    List<ExamModel> exams,
  ) {
    if (exams.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_available, size: 64, color: Colors.green.shade300),
            const SizedBox(height: 16),
            Text(
              'لا توجد امتحانات لـ ${child['name']}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'سيتم إضافة الامتحانات عند توفرها',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: exams.length,
      itemBuilder: (context, index) {
        final exam = exams[index];
        return _buildParentExamCard(exam, child);
      },
    );
  }

  /// بناء تبويب العرض الزمني
  Widget _buildTimelineTab() {
    // جمع جميع الامتحانات وترتيبها زمنياً
    final allExams = <Map<String, dynamic>>[];

    for (final child in _children) {
      final childId = child['id'] as String;
      final childExams = _childrenExams[childId] ?? [];

      for (final exam in childExams) {
        allExams.add({'exam': exam, 'child': child});
      }
    }

    // ترتيب حسب التاريخ
    allExams.sort(
      (a, b) => (a['exam'] as ExamModel).startDate.compareTo(
        (b['exam'] as ExamModel).startDate,
      ),
    );

    if (allExams.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_note, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد امتحانات مجدولة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: allExams.length,
      itemBuilder: (context, index) {
        final examData = allExams[index];
        final exam = examData['exam'] as ExamModel;
        final child = examData['child'] as Map<String, dynamic>;
        return _buildParentExamCard(exam, child);
      },
    );
  }

  /// بناء تبويب امتحانات اليوم
  Widget _buildTodayTab() {
    final today = DateTime.now();
    final todayExams = <Map<String, dynamic>>[];

    for (final child in _children) {
      final childId = child['id'] as String;
      final childExams = _childrenExams[childId] ?? [];

      for (final exam in childExams) {
        if (exam.startDate.year == today.year &&
            exam.startDate.month == today.month &&
            exam.startDate.day == today.day) {
          todayExams.add({'exam': exam, 'child': child});
        }
      }
    }

    if (todayExams.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_available, size: 64, color: Colors.green),
            SizedBox(height: 16),
            Text(
              'لا توجد امتحانات اليوم',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'يوم هادئ لجميع الأطفال',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: todayExams.length,
      itemBuilder: (context, index) {
        final examData = todayExams[index];
        final exam = examData['exam'] as ExamModel;
        final child = examData['child'] as Map<String, dynamic>;
        return _buildParentExamCard(exam, child, isToday: true);
      },
    );
  }

  /// بناء تبويب امتحانات الأسبوع
  Widget _buildWeekTab() {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday % 7));
    final weekEnd = weekStart.add(const Duration(days: 6));
    final weekExams = <Map<String, dynamic>>[];

    for (final child in _children) {
      final childId = child['id'] as String;
      final childExams = _childrenExams[childId] ?? [];

      for (final exam in childExams) {
        if (exam.startDate.isAfter(weekStart) &&
            exam.startDate.isBefore(weekEnd.add(const Duration(days: 1)))) {
          weekExams.add({'exam': exam, 'child': child});
        }
      }
    }

    if (weekExams.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.calendar_view_week, size: 64, color: Colors.blue),
            SizedBox(height: 16),
            Text(
              'لا توجد امتحانات هذا الأسبوع',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'أسبوع مريح للمراجعة والاستعداد',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: weekExams.length,
      itemBuilder: (context, index) {
        final examData = weekExams[index];
        final exam = examData['exam'] as ExamModel;
        final child = examData['child'] as Map<String, dynamic>;
        return _buildParentExamCard(exam, child);
      },
    );
  }

  /// بناء بطاقة امتحان لولي الأمر
  Widget _buildParentExamCard(
    ExamModel exam,
    Map<String, dynamic> child, {
    bool isToday = false,
  }) {
    final timeUntilExam = exam.startDate.difference(DateTime.now());
    final isUpcoming = timeUntilExam.inDays >= 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: isToday ? 8 : 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side:
            isToday
                ? BorderSide(color: AppColors.parentColor, width: 2)
                : BorderSide.none,
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient:
              isToday
                  ? LinearGradient(
                    colors: [
                      AppColors.parentColor.withValues(alpha: 0.1),
                      Colors.white,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                  : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة مع معلومات الطفل
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isToday ? AppColors.parentColor : Colors.blue,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.person, color: Colors.white, size: 20),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          child['name'],
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color:
                                isToday
                                    ? AppColors.parentColor
                                    : Colors.black87,
                          ),
                        ),
                        Text(
                          '${child['grade']} - ${child['section']}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isToday)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.parentColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'اليوم',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 12),

              // اسم الامتحان
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.quiz, color: Colors.indigo, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        exam.name,
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: Colors.indigo,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // تفاصيل الامتحان
              Row(
                children: [
                  Expanded(
                    child: _buildParentExamDetail(
                      Icons.calendar_today,
                      'التاريخ',
                      _formatDate(exam.startDate),
                    ),
                  ),
                  Expanded(
                    child: _buildParentExamDetail(
                      Icons.access_time,
                      'الوقت',
                      _formatTime(exam.startDate),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              Row(
                children: [
                  Expanded(
                    child: _buildParentExamDetail(
                      Icons.timer,
                      'المدة',
                      _formatDuration(exam.startDate, exam.endDate),
                    ),
                  ),
                  Expanded(
                    child: _buildParentExamDetail(
                      Icons.event_available,
                      'الحالة',
                      _getExamStatusText(exam.status),
                    ),
                  ),
                ],
              ),

              if (exam.description?.isNotEmpty == true) ...[
                const SizedBox(height: 12),
                Text(
                  exam.description!,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                    height: 1.4,
                  ),
                ),
              ],

              // العد التنازلي والإجراءات
              const SizedBox(height: 16),

              // العد التنازلي
              if (isUpcoming && timeUntilExam.inDays <= 7) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                        timeUntilExam.inDays <= 1
                            ? Colors.red.shade50
                            : Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color:
                          timeUntilExam.inDays <= 1
                              ? Colors.red.shade200
                              : Colors.blue.shade200,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        color:
                            timeUntilExam.inDays <= 1
                                ? Colors.red
                                : Colors.blue,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _getCountdownText(timeUntilExam),
                        style: TextStyle(
                          color:
                              timeUntilExam.inDays <= 1
                                  ? Colors.red.shade700
                                  : Colors.blue.shade700,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
              ],

              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _addReminder(exam, child),
                      icon: const Icon(Icons.alarm_add, size: 18),
                      label: const Text('تذكير'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.parentColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _shareExam(exam, child),
                      icon: const Icon(Icons.share, size: 18),
                      label: const Text('مشاركة'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.parentColor,
                        side: BorderSide(color: AppColors.parentColor),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء تفصيل امتحان لولي الأمر
  Widget _buildParentExamDetail(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey.shade600),
        const SizedBox(width: 6),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            ),
            Text(
              value,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ],
    );
  }

  /// دوال الإجراءات
  void _refreshData() {
    setState(() {
      _generateMockExams();
    });
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تم تحديث البيانات')));
  }

  void _shareSchedule() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة جدول امتحانات الأبناء')),
    );
  }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'الإجراءات السريعة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.parentColor,
                  ),
                ),
                const SizedBox(height: 20),

                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.parentColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.alarm_add, color: AppColors.parentColor),
                  ),
                  title: const Text('إضافة تذكير عام'),
                  subtitle: const Text('تذكير لجميع امتحانات الأطفال'),
                  onTap: () {
                    Navigator.pop(context);
                    _addGeneralReminder();
                  },
                ),

                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.calendar_today, color: Colors.blue),
                  ),
                  title: const Text('تصدير الجدول'),
                  subtitle: const Text('حفظ جدول الامتحانات كملف'),
                  onTap: () {
                    Navigator.pop(context);
                    _exportSchedule();
                  },
                ),

                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.print, color: Colors.green),
                  ),
                  title: const Text('طباعة الجدول'),
                  subtitle: const Text('طباعة جدول امتحانات الأطفال'),
                  onTap: () {
                    Navigator.pop(context);
                    _printSchedule();
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _addReminder(ExamModel exam, Map<String, dynamic> child) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('إضافة تذكير لامتحان ${child['name']}: ${exam.name}'),
      ),
    );
  }

  void _shareExam(ExamModel exam, Map<String, dynamic> child) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('مشاركة امتحان ${child['name']}: ${exam.name}')),
    );
  }

  void _addGeneralReminder() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إضافة تذكير عام لجميع الامتحانات')),
    );
  }

  void _exportSchedule() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تصدير جدول الامتحانات')));
  }

  void _printSchedule() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('طباعة جدول الامتحانات')));
  }

  /// دوال التنسيق المساعدة
  String _formatDate(DateTime date) {
    final weekdays = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];
    final months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    return '${weekdays[date.weekday % 7]} ${date.day} ${months[date.month - 1]}';
  }

  String _formatTime(DateTime date) {
    final hour = date.hour;
    final minute = date.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'م' : 'ص';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

    return '$displayHour:$minute $period';
  }

  String _formatDuration(DateTime start, DateTime end) {
    final duration = end.difference(start);
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return minutes > 0 ? '${hours}س ${minutes}د' : '${hours}س';
    } else {
      return '${minutes}د';
    }
  }

  String _getExamStatusText(ExamStatus status) {
    switch (status) {
      case ExamStatus.scheduled:
        return 'مجدول';
      case ExamStatus.ongoing:
        return 'جاري';
      case ExamStatus.completed:
        return 'مكتمل';
      case ExamStatus.cancelled:
        return 'ملغي';
    }
  }

  String _getCountdownText(Duration timeUntil) {
    if (timeUntil.inDays > 0) {
      return 'خلال ${timeUntil.inDays} ${timeUntil.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (timeUntil.inHours > 0) {
      return 'خلال ${timeUntil.inHours} ${timeUntil.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (timeUntil.inMinutes > 0) {
      return 'خلال ${timeUntil.inMinutes} ${timeUntil.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }
}

/// تعداد أنواع العرض
enum ViewType {
  byChild, // حسب الطفل
  timeline, // زمني
  today, // اليوم
  week, // الأسبوع
}
