import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/mobile_screens/child_dashboard_screen.dart';
import 'package:school_management_system/mobile_screens/guardian_communication_screen.dart';
import 'package:school_management_system/providers/guardian_providers.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/widgets/shared_app_drawer.dart';
import 'package:school_management_system/shared/app_theme.dart';
import 'package:school_management_system/widgets/enhanced_widgets.dart';

/// الواجهة الرئيسية المحسنة لولي الأمر (لوحة التحكم الموحدة)
///
/// تطبق التصميم الموحد الجديد مع تحسينات في:
/// - نظام الألوان والمسافات الموحد
/// - تصميم البطاقات المحسن
/// - تجربة مستخدم محسنة لأولياء الأمور
/// - عرض أفضل لبيانات الأبناء
class GuardianHomePage extends ConsumerWidget {
  const GuardianHomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final guardianId = FirebaseAuth.instance.currentUser?.uid;

    // التحقق من تسجيل دخول ولي الأمر
    if (guardianId == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('بوابة ولي الأمر')),
        body: const ErrorMessage(message: 'خطأ: المستخدم غير مسجل دخوله.'),
      );
    }

    // مراقبة FutureProvider لجلب بيانات الأبناء الحقيقية والمفصلة
    final childrenDataAsyncValue = ref.watch(
      guardianChildrenRealDataProvider(guardianId),
    );

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'لوحة تحكم ولي الأمر',
          style: TextStyle(
            fontSize: AppTextSizes.headlineMedium,
            fontWeight: FontWeight.bold,
            color: AppColors.textOnPrimary,
          ),
        ),
        backgroundColor: AppColors.parentColor,
        foregroundColor: AppColors.textOnPrimary,
        elevation: AppElevation.medium,
        centerTitle: true,
        actions: [
          // زر الإشعارات
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            tooltip: 'الإشعارات',
            onPressed: () {
              // TODO: فتح صفحة الإشعارات
            },
          ),
        ],
      ),
      drawer: const SharedAppDrawer(),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) =>
                      GuardianCommunicationScreen(guardianId: guardianId),
            ),
          );
        },
        backgroundColor: AppColors.parentColor,
        foregroundColor: AppColors.textOnPrimary,
        label: const Text('التواصل مع الإدارة'),
        icon: const Icon(Icons.message_outlined),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // تمكين السحب للتحديث عن طريق إعادة تحميل الـ provider الجديد
          ref.refresh(guardianChildrenRealDataProvider(guardianId));
        },
        child: childrenDataAsyncValue.when(
          loading: () => const LoadingIndicator(),
          error:
              (err, stack) =>
                  ErrorMessage(message: 'حدث خطأ أثناء جلب البيانات: $err'),
          data: (childrenData) {
            if (childrenData.isEmpty) {
              return const Center(
                child: Text(
                  'لم يتم ربط أي طالب بحسابك حتى الآن.',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: childrenData.length,
              itemBuilder: (context, index) {
                final child = childrenData[index];
                return _buildChildSummaryCard(context, child);
              },
            );
          },
        ),
      ),
    );
  }

  /// ويدجت لبناء بطاقة ملخص بيانات الابن
  Widget _buildChildSummaryCard(
    BuildContext context,
    Map<String, dynamic> childData,
  ) {
    final studentInfo = childData['studentInfo'] as Map<String, dynamic>;
    final financialSummary =
        childData['financialSummary'] as Map<String, dynamic>;
    final attendanceSummary =
        childData['attendanceSummary'] as Map<String, dynamic>;
    final studentId = studentInfo['id']; // نفترض وجود 'id' في بيانات الطالب
    final studentName = studentInfo['name'] ?? 'اسم غير متوفر';

    final currencyFormat = NumberFormat.currency(
      locale: 'ar_SA',
      symbol: 'ر.س',
    );
    final remainingAmount = financialSummary['totalRemaining'] ?? 0.0;
    final absentDays = attendanceSummary['absentDays'] ?? 0;

    return Card(
      elevation: 4.0,
      margin: const EdgeInsets.only(bottom: 16.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      child: InkWell(
        onTap: () {
          // الانتقال إلى شاشة التفاصيل الكاملة للابن
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => ChildDashboardScreen(
                    studentId: studentId,
                    studentName: studentName,
                  ),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الجزء العلوي: الاسم والصورة
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundImage:
                        studentInfo['profileImageUrl'] != null
                            ? NetworkImage(studentInfo['profileImageUrl'])
                            : null,
                    child:
                        studentInfo['profileImageUrl'] == null
                            ? const Icon(Icons.person, size: 30)
                            : null,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          studentName,
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          'الصف: ${studentInfo['studentClass'] ?? 'غير محدد'}',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                  const Icon(Icons.arrow_forward_ios, color: Colors.grey),
                ],
              ),
              const Divider(height: 24.0),
              // الجزء السفلي: ملخص البيانات
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildSummaryItem(
                    context,
                    icon: Icons.account_balance_wallet_outlined,
                    label: 'المتبقي',
                    value: currencyFormat.format(remainingAmount),
                    color: remainingAmount > 0 ? Colors.red : Colors.green,
                  ),
                  _buildSummaryItem(
                    context,
                    icon: Icons.event_busy_outlined,
                    label: 'أيام الغياب',
                    value: absentDays.toString(),
                    color:
                        absentDays > 0
                            ? Colors.orange.shade800
                            : Colors.black87,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// ويدجت مساعد لعرض عنصر في ملخص البيانات
  Widget _buildSummaryItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 28),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: Colors.grey[700]),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
