import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/services/firebase_service.dart';
import 'package:school_management_system/shared/app_theme.dart';

/// شاشة التواصل مع المدرسة للأولياء
///
/// هذه الشاشة تعتبر حلقة الوصل الرئيسية بين أولياء الأمور والمدرسة حيث تمكنهم من:
/// - إرسال رسائل مباشرة للمعلمين والإدارة
/// - استقبال الإشعارات والتنبيهات من المدرسة
/// - حجز مواعيد لاجتماعات أولياء الأمور
/// - تقديم الشكاوى والاقتراحات
/// - متابعة حالة الطلبات والاستفسارات
/// - عرض الإعلانات والأخبار المدرسية
/// - التواصل مع أولياء أمور آخرين (إذا كان مسموحاً)
/// - طلب تقارير خاصة أو معلومات إضافية
/// - الإبلاغ عن مشاكل أو حوادث
///
/// تدفق العمل في الشاشة:
/// 1. عرض صندوق الوارد للرسائل المستقبلة
/// 2. إمكانية إنشاء رسالة جديدة أو طلب
/// 3. اختيار المستقبل (معلم، إدارة، قسم معين)
/// 4. كتابة الرسالة وإرفاق الملفات إن أمكن
/// 5. إرسال الرسالة وتتبع حالتها
/// 6. استقبال الردود والتفاعل معها
/// 7. أرشفة المحادثات المكتملة
///
/// الألوان والتصميم:
/// - اللون الأساسي: برتقالي داكن (يرمز للتواصل والدفء)
/// - ألوان فرعية: برتقالي فاتح، أصفر، أخضر للنجاح
/// - تصميم يشبه تطبيقات المراسلة المألوفة
/// - فقاعات محادثة واضحة ومريحة للقراءة
/// - أيقونات تعبيرية لحالة الرسائل
class SchoolCommunicationScreen extends ConsumerStatefulWidget {
  /// معرف ولي الأمر الذي يستخدم النظام
  /// يستخدم لتحميل المحادثات والرسائل الخاصة به
  final String parentId;

  const SchoolCommunicationScreen({super.key, required this.parentId});

  @override
  ConsumerState<SchoolCommunicationScreen> createState() =>
      _SchoolCommunicationScreenState();
}

class _SchoolCommunicationScreenState
    extends ConsumerState<SchoolCommunicationScreen>
    with TickerProviderStateMixin {
  // ===================================================================
  // متحكمات الواجهة والرسوم المتحركة
  // ===================================================================

  /// متحكم التبويبات الرئيسية
  /// يدير التنقل بين: الرسائل، الإشعارات، المواعيد، الطلبات
  late TabController _tabController;

  /// متحكم الرسوم المتحركة لظهور الرسائل
  /// يستخدم لإظهار الرسائل الجديدة بشكل تدريجي وجذاب
  late AnimationController _messageAnimationController;

  /// الرسم المتحرك لانزلاق الرسائل
  late Animation<Offset> _messageSlideAnimation;

  /// متحكم كتابة الرسالة الجديدة
  final TextEditingController _messageController = TextEditingController();

  /// متحكم البحث في المحادثات
  final TextEditingController _searchController = TextEditingController();

  /// متحكم عنوان الرسالة
  final TextEditingController _subjectController = TextEditingController();

  /// مفتاح النموذج للتحقق من صحة البيانات
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // ===================================================================
  // متغيرات البيانات الرئيسية
  // ===================================================================

  /// قائمة المحادثات مع المدرسة
  /// كل محادثة تحتوي على سلسلة من الرسائل
  List<Map<String, dynamic>> _conversations = [];

  /// قائمة الإشعارات من المدرسة
  /// مرتبة حسب التاريخ من الأحدث إلى الأقدم
  List<Map<String, dynamic>> _notifications = [];

  /// قائمة المواعيد المحجوزة
  /// تشمل: اجتماعات أولياء أمور، مقابلات شخصية، فعاليات
  List<Map<String, dynamic>> _appointments = [];

  /// قائمة الطلبات المقدمة
  /// تشمل: طلبات شهادات، استفسارات، شكاوى
  List<Map<String, dynamic>> _requests = [];

  /// قائمة المعلمين والموظفين المتاحين للتواصل
  List<Map<String, dynamic>> _availableContacts = [];

  /// قائمة الأبناء لربط الرسائل بطالب محدد
  List<StudentModel> _children = [];

  // ===================================================================
  // متغيرات حالة التحميل والتفاعل
  // ===================================================================

  /// حالة تحميل البيانات الأولية
  bool _isLoading = false;

  /// حالة إرسال رسالة جديدة
  bool _isSendingMessage = false;

  /// حالة حجز موعد جديد
  bool _isBookingAppointment = false;

  /// نص البحث الحالي
  String _searchQuery = '';

  /// نوع الرسالة المحددة (استفسار، شكوى، اقتراح، إلخ)
  MessageType _selectedMessageType = MessageType.inquiry;

  /// المستقبل المحدد للرسالة
  String? _selectedRecipient;

  /// الطالب المرتبط بالرسالة (إن وجد)
  String? _selectedStudent;

  /// أولوية الرسالة (عادية، مهمة، عاجلة)
  MessagePriority _messagePriority = MessagePriority.normal;

  /// فلتر المحادثات (الكل، غير مقروءة، مهمة)
  ConversationFilter _conversationFilter = ConversationFilter.all;

  // ===================================================================
  // إحصائيات سريعة للعرض
  // ===================================================================

  /// عدد الرسائل غير المقروءة
  int _unreadMessages = 0;

  /// عدد الإشعارات الجديدة
  int _newNotifications = 0;

  /// عدد المواعيد القادمة
  int _upcomingAppointments = 0;

  /// عدد الطلبات المعلقة
  int _pendingRequests = 0;

  /// آخر رسالة مستقبلة
  Map<String, dynamic>? _lastMessage;

  /// حالة الاتصال مع الخادم
  bool _isConnected = true;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم التبويبات مع 4 تبويبات
    _tabController = TabController(length: 4, vsync: this);

    // إنشاء متحكم الرسوم المتحركة للرسائل
    _messageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // إنشاء الرسم المتحرك لانزلاق الرسائل
    _messageSlideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0), // يبدأ من اليمين
      end: Offset.zero, // ينتهي في الموضع النهائي
    ).animate(
      CurvedAnimation(
        parent: _messageAnimationController,
        curve: Curves.easeOutCubic,
      ),
    );

    // تحميل البيانات الأولية
    _loadCommunicationData();

    // إضافة مستمع لتغييرات التبويبات
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    // تنظيف الموارد عند إغلاق الشاشة
    _tabController.dispose();
    _messageAnimationController.dispose();
    _messageController.dispose();
    _searchController.dispose();
    _subjectController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع التبويبات والإجراءات
      appBar: AppBar(
        title: const Text(
          'التواصل مع المدرسة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.orange[800], // لون برتقالي داكن للتواصل
        elevation: 2,

        // التبويبات السفلية في شريط التطبيق
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          tabs: [
            Tab(
              icon: Stack(
                children: [
                  const Icon(Icons.message, size: 18),
                  if (_unreadMessages > 0)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 12,
                          minHeight: 12,
                        ),
                        child: Text(
                          '$_unreadMessages',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
              text: 'الرسائل',
            ),
            Tab(
              icon: Stack(
                children: [
                  const Icon(Icons.notifications, size: 18),
                  if (_newNotifications > 0)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 12,
                          minHeight: 12,
                        ),
                        child: Text(
                          '$_newNotifications',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
              text: 'الإشعارات',
            ),
            const Tab(icon: Icon(Icons.event, size: 18), text: 'المواعيد'),
            const Tab(icon: Icon(Icons.assignment, size: 18), text: 'الطلبات'),
          ],
        ),

        // أزرار الإجراءات في شريط التطبيق
        actions: [
          // زر البحث في المحادثات
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () => _showSearchDialog(),
            tooltip: 'البحث في المحادثات',
          ),

          // زر الفلاتر
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: () => _showFilterDialog(),
            tooltip: 'فلترة المحادثات',
          ),

          // مؤشر حالة الاتصال
          Icon(
            _isConnected ? Icons.wifi : Icons.wifi_off,
            color: _isConnected ? Colors.white : Colors.red[300],
            size: 20,
          ),
          const SizedBox(width: 8),
        ],
      ),

      // محتوى التبويبات الرئيسية
      body: TabBarView(
        controller: _tabController,
        children: [
          // تبويب الرسائل والمحادثات
          _buildMessagesTab(),

          // تبويب الإشعارات
          _buildNotificationsTab(),

          // تبويب المواعيد
          _buildAppointmentsTab(),

          // تبويب الطلبات
          _buildRequestsTab(),
        ],
      ),

      // شريط المعلومات السفلي
      bottomNavigationBar: _buildBottomInfoBar(),

      // زر عائم لإنشاء رسالة جديدة
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showNewMessageDialog(),
        backgroundColor: Colors.orange[600],
        icon: const Icon(Icons.add_comment, color: Colors.white),
        label: const Text(
          'رسالة جديدة',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  // ===================================================================
  // دوال تحميل البيانات والتحليل
  // ===================================================================

  /// تحميل بيانات التواصل من قاعدة البيانات
  ///
  /// هذه الدالة تقوم بتحميل جميع البيانات المطلوبة:
  /// - المحادثات والرسائل السابقة
  /// - الإشعارات من المدرسة
  /// - المواعيد المحجوزة
  /// - الطلبات المقدمة
  /// - قائمة المعلمين والموظفين
  /// - معلومات الأبناء
  Future<void> _loadCommunicationData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final firebaseService = FirebaseService();

      // 1. جلب معلومات الأبناء الحقيقية من Firebase
      await _loadChildrenData(firebaseService);

      // 2. تهيئة القوائم الأخرى بقوائم فارغة حتى يتم تنفيذ الأنظمة المطلوبة
      setState(() {
        _conversations = [];
        _notifications = [];
        _appointments = [];
        _requests = [];
        _availableContacts = [];
      });

      // جلب البيانات من Firebase
      await _loadConversations();
      await _loadNotifications();
      await _loadAppointments();
      await _loadRequests();

      // جلب قائمة المعلمين والموظفين المحدثة
      await _loadAvailableContacts();

      print('تم تحميل بيانات التواصل مع قائمة المعلمين المحدثة');

      // حساب الإحصائيات
      _calculateStatistics();

      // بدء الرسوم المتحركة
      _messageAnimationController.forward();
    } catch (e) {
      // معالجة الأخطاء وعرض رسالة مناسبة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _loadCommunicationData(),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تحميل بيانات الأبناء الحقيقية من Firebase
  Future<void> _loadChildrenData(FirebaseService firebaseService) async {
    try {
      // جلب جميع الطلاب من Firebase
      // في التطبيق الحقيقي، يجب ربط الطلاب بولي الأمر المحدد
      final studentsStream = firebaseService.getAllStudentsStream();

      // الاستماع لأول دفعة من البيانات
      await for (final students in studentsStream.take(1)) {
        setState(() {
          _children = students;
        });
        break; // الخروج بعد أول دفعة
      }
    } catch (e) {
      print('خطأ في تحميل بيانات الأبناء: $e');
      // في حالة الفشل، استخدم قائمة فارغة
      setState(() {
        _children = [];
      });
    }
  }

  /// تحميل قائمة المعلمين والموظفين المتاحين للتواصل
  Future<void> _loadAvailableContacts() async {
    try {
      // جلب قائمة المعلمين من Firebase
      final teachersSnapshot =
          await FirebaseFirestore.instance
              .collection('users')
              .where('role', isEqualTo: 'teacher')
              .get();

      // جلب قائمة الموظفين الإداريين من Firebase
      final adminSnapshot =
          await FirebaseFirestore.instance
              .collection('users')
              .where('role', isEqualTo: 'admin')
              .get();

      List<Map<String, dynamic>> contacts = [];

      // إضافة المعلمين إلى القائمة
      for (var doc in teachersSnapshot.docs) {
        final data = doc.data();
        contacts.add({
          'id': doc.id,
          'name': data['name'] ?? 'معلم غير محدد',
          'jobTitle': data['jobTitle'] ?? 'معلم',
          'role': 'teacher',
          'email': data['email'] ?? '',
          'phoneNumber': data['phoneNumber'],
          'profileImageUrl': data['profileImageUrl'],
          'gender': data['gender'],
          'governorate': data['governorate'],
          'nationality': data['nationality'] ?? 'غير محدد',
          'bio': data['bio'],
        });
      }

      // إضافة الموظفين الإداريين إلى القائمة
      for (var doc in adminSnapshot.docs) {
        final data = doc.data();
        contacts.add({
          'id': doc.id,
          'name': data['name'] ?? 'موظف غير محدد',
          'jobTitle': data['jobTitle'] ?? 'موظف إداري',
          'role': 'admin',
          'email': data['email'] ?? '',
          'phoneNumber': data['phoneNumber'],
          'profileImageUrl': data['profileImageUrl'],
          'gender': data['gender'],
          'governorate': data['governorate'],
          'nationality': data['nationality'] ?? 'غير محدد',
          'bio': data['bio'],
        });
      }

      setState(() {
        _availableContacts = contacts;
      });
    } catch (e) {
      print('خطأ في تحميل قائمة المعلمين: $e');
      setState(() {
        _availableContacts = [];
      });
    }
  }

  /// حساب الإحصائيات والملخصات
  ///
  /// يحسب جميع الإحصائيات المطلوبة لعرض الملخص
  void _calculateStatistics() {
    // حساب عدد الرسائل غير المقروءة
    _unreadMessages =
        _conversations.where((conv) => !(conv['isRead'] as bool)).length;

    // حساب عدد الإشعارات الجديدة
    _newNotifications =
        _notifications.where((notif) => !(notif['isRead'] as bool)).length;

    // حساب عدد المواعيد القادمة
    final now = DateTime.now();
    _upcomingAppointments =
        _appointments
            .where((app) => (app['date'] as DateTime).isAfter(now))
            .length;

    // حساب عدد الطلبات المعلقة
    _pendingRequests =
        _requests
            .where(
              (req) =>
                  req['status'] == 'processing' || req['status'] == 'pending',
            )
            .length;

    // تحديد آخر رسالة
    if (_conversations.isNotEmpty) {
      _lastMessage = _conversations.first;
    }
  }

  // ===================================================================
  // معالجات الأحداث والحوارات
  // ===================================================================

  /// معالج تغيير التبويبات
  void _onTabChanged() {
    if (!mounted) return;

    // تحديث البيانات حسب التبويب المحدد
    switch (_tabController.index) {
      case 0: // تبويب الرسائل
        break;
      case 1: // تبويب الإشعارات
        break;
      case 2: // تبويب المواعيد
        break;
      case 3: // تبويب الطلبات
        break;
    }
  }

  /// عرض حوار البحث
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('البحث في المحادثات'),
            content: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'ابحث عن رسالة أو محادثة...',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('بحث'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار الفلاتر
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('فلترة المحادثات'),
            content: const Text('سيتم تطبيق خيارات الفلترة قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار رسالة جديدة
  void _showNewMessageDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('رسالة جديدة'),
            content: const Text('سيتم تطبيق إنشاء رسالة جديدة قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إرسال'),
              ),
            ],
          ),
    );
  }

  // ===================================================================
  // دوال بناء التبويبات
  // ===================================================================

  /// بناء تبويب الرسائل والمحادثات
  Widget _buildMessagesTab() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    return Column(
      children: [
        // شريط البحث والفلترة
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey.shade50,
          child: Column(
            children: [
              // شريط البحث
              TextField(
                decoration: InputDecoration(
                  hintText: 'البحث في المحادثات...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.filter_list),
                    onPressed: () => _showMessageFilters(),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
              ),
              const SizedBox(height: 12),

              // أزرار الفلترة السريعة
              Row(
                children: [
                  _buildFilterChip('الكل', true),
                  const SizedBox(width: 8),
                  _buildFilterChip('غير مقروءة', false),
                  const SizedBox(width: 8),
                  _buildFilterChip('المعلمين', false),
                  const SizedBox(width: 8),
                  _buildFilterChip('الإدارة', false),
                ],
              ),
            ],
          ),
        ),

        // قائمة المحادثات
        Expanded(
          child:
              _conversations.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.chat_bubble_outline,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'لا توجد محادثات',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'ابدأ محادثة جديدة مع المعلمين',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                  : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _conversations.length,
                    itemBuilder: (context, index) {
                      final conversation = _conversations[index];
                      return _buildConversationCard(conversation);
                    },
                  ),
        ),
      ],
    );
  }

  /// بناء تبويب الإشعارات
  Widget _buildNotificationsTab() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    return Column(
      children: [
        // شريط الفلترة والإجراءات
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey.shade50,
          child: Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    _buildNotificationFilterChip('الكل', true),
                    const SizedBox(width: 8),
                    _buildNotificationFilterChip('غير مقروءة', false),
                    const SizedBox(width: 8),
                    _buildNotificationFilterChip('مهمة', false),
                  ],
                ),
              ),
              IconButton(
                icon: const Icon(Icons.mark_email_read),
                onPressed: () => _markAllAsRead(),
                tooltip: 'تحديد الكل كمقروء',
              ),
            ],
          ),
        ),

        // قائمة الإشعارات
        Expanded(
          child:
              _notifications.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.notifications_none,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'لا توجد إشعارات',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'ستظهر الإشعارات الجديدة هنا',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                  : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _notifications.length,
                    itemBuilder: (context, index) {
                      final notification = _notifications[index];
                      return _buildNotificationCard(notification);
                    },
                  ),
        ),
      ],
    );
  }

  /// بناء تبويب المواعيد
  Widget _buildAppointmentsTab() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    return Column(
      children: [
        // شريط الإجراءات والفلترة
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey.shade50,
          child: Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    _buildAppointmentFilterChip('القادمة', true),
                    const SizedBox(width: 8),
                    _buildAppointmentFilterChip('المكتملة', false),
                    const SizedBox(width: 8),
                    _buildAppointmentFilterChip('الملغية', false),
                  ],
                ),
              ),
              ElevatedButton.icon(
                onPressed: () => _showBookAppointmentDialog(),
                icon: const Icon(Icons.add, size: 18),
                label: const Text('حجز موعد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.parentColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ),

        // قائمة المواعيد
        Expanded(
          child:
              _appointments.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.event_available,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'لا توجد مواعيد محجوزة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'احجز موعداً جديداً مع المعلمين',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                  : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _appointments.length,
                    itemBuilder: (context, index) {
                      final appointment = _appointments[index];
                      return _buildAppointmentCard(appointment);
                    },
                  ),
        ),
      ],
    );
  }

  /// بناء تبويب الطلبات
  Widget _buildRequestsTab() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    return Column(
      children: [
        // شريط الإجراءات والفلترة
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey.shade50,
          child: Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    _buildRequestFilterChip('الكل', true),
                    const SizedBox(width: 8),
                    _buildRequestFilterChip('قيد المراجعة', false),
                    const SizedBox(width: 8),
                    _buildRequestFilterChip('مكتملة', false),
                  ],
                ),
              ),
              ElevatedButton.icon(
                onPressed: () => _showNewRequestDialog(),
                icon: const Icon(Icons.add, size: 18),
                label: const Text('طلب جديد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.parentColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ),

        // قائمة الطلبات
        Expanded(
          child:
              _requests.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.description,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'لا توجد طلبات مقدمة',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'قدم طلباً جديداً للمدرسة',
                          style: TextStyle(fontSize: 14, color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                  : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _requests.length,
                    itemBuilder: (context, index) {
                      final request = _requests[index];
                      return _buildRequestCard(request);
                    },
                  ),
        ),
      ],
    );
  }

  /// بناء شريط المعلومات السفلي
  Widget _buildBottomInfoBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        border: Border(top: BorderSide(color: Colors.orange[200]!)),
      ),
      child: Row(
        children: [
          Icon(Icons.message, size: 16, color: Colors.orange[600]),
          const SizedBox(width: 4),
          Text(
            'غير مقروءة: $_unreadMessages',
            style: TextStyle(fontSize: 12, color: Colors.orange[600]),
          ),
          const Spacer(),
          if (_lastMessage != null) ...[
            Icon(Icons.access_time, size: 16, color: Colors.orange[600]),
            const SizedBox(width: 4),
            Text(
              'آخر رسالة: ${_formatTime(_lastMessage!['lastMessageTime'] as DateTime)}',
              style: TextStyle(fontSize: 12, color: Colors.orange[600]),
            ),
          ],
        ],
      ),
    );
  }

  /// تنسيق الوقت للعرض
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  // ===================================================================
  // دوال الرسائل والمحادثات
  // ===================================================================

  /// عرض فلاتر الرسائل
  void _showMessageFilters() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'فلترة المحادثات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.parentColor,
                  ),
                ),
                const SizedBox(height: 20),

                ListTile(
                  leading: const Icon(Icons.all_inbox),
                  title: const Text('جميع المحادثات'),
                  onTap: () => Navigator.pop(context),
                ),
                ListTile(
                  leading: const Icon(Icons.mark_email_unread),
                  title: const Text('غير مقروءة فقط'),
                  onTap: () => Navigator.pop(context),
                ),
                ListTile(
                  leading: const Icon(Icons.school),
                  title: const Text('المعلمين فقط'),
                  onTap: () => Navigator.pop(context),
                ),
                ListTile(
                  leading: const Icon(Icons.admin_panel_settings),
                  title: const Text('الإدارة فقط'),
                  onTap: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
    );
  }

  /// بناء رقاقة الفلترة
  Widget _buildFilterChip(String label, bool isSelected) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        // TODO: تنفيذ منطق الفلترة
      },
      selectedColor: AppColors.parentColor.withValues(alpha: 0.2),
      checkmarkColor: AppColors.parentColor,
      labelStyle: TextStyle(
        color: isSelected ? AppColors.parentColor : Colors.grey.shade700,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
    );
  }

  /// بناء بطاقة محادثة
  Widget _buildConversationCard(Map<String, dynamic> conversation) {
    final bool hasUnread = conversation['unreadCount'] > 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: hasUnread ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side:
            hasUnread
                ? BorderSide(color: AppColors.parentColor, width: 1)
                : BorderSide.none,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Stack(
          children: [
            CircleAvatar(
              radius: 25,
              backgroundColor: AppColors.parentColor,
              child: Text(
                conversation['avatar'],
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ),
            if (conversation['isOnline'])
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                ),
              ),
          ],
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                conversation['name'],
                style: TextStyle(
                  fontWeight: hasUnread ? FontWeight.bold : FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
            if (hasUnread)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.parentColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${conversation['unreadCount']}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              conversation['role'],
              style: TextStyle(
                color: AppColors.parentColor,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              conversation['lastMessage'],
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 14,
                fontWeight: hasUnread ? FontWeight.w500 : FontWeight.normal,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              _formatTime(conversation['timestamp']),
              style: TextStyle(color: Colors.grey.shade500, fontSize: 12),
            ),
          ],
        ),
        onTap: () => _openConversation(conversation),
      ),
    );
  }

  /// فتح محادثة
  void _openConversation(Map<String, dynamic> conversation) {
    // TODO: تنفيذ فتح شاشة المحادثة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('فتح محادثة مع ${conversation['name']}')),
    );
  }

  // ===================================================================
  // دوال الإشعارات
  // ===================================================================

  /// بناء رقاقة فلترة الإشعارات
  Widget _buildNotificationFilterChip(String label, bool isSelected) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        // TODO: تنفيذ منطق فلترة الإشعارات
      },
      selectedColor: AppColors.parentColor.withValues(alpha: 0.2),
      checkmarkColor: AppColors.parentColor,
      labelStyle: TextStyle(
        color: isSelected ? AppColors.parentColor : Colors.grey.shade700,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
    );
  }

  /// تحديد جميع الإشعارات كمقروءة
  void _markAllAsRead() {
    setState(() {
      for (var notification in _notifications) {
        notification['isRead'] = true;
      }
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم تحديد جميع الإشعارات كمقروءة')),
    );
  }

  /// بناء بطاقة إشعار
  Widget _buildNotificationCard(Map<String, dynamic> notification) {
    final bool isUnread = !notification['isRead'];

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isUnread ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side:
            isUnread
                ? BorderSide(color: notification['color'], width: 1)
                : BorderSide.none,
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient:
              isUnread
                  ? LinearGradient(
                    colors: [
                      notification['color'].withValues(alpha: 0.05),
                      Colors.white,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                  : null,
        ),
        child: ListTile(
          contentPadding: const EdgeInsets.all(16),
          leading: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: notification['color'].withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              notification['icon'],
              color: notification['color'],
              size: 24,
            ),
          ),
          title: Row(
            children: [
              Expanded(
                child: Text(
                  notification['title'],
                  style: TextStyle(
                    fontWeight: isUnread ? FontWeight.bold : FontWeight.w600,
                    fontSize: 16,
                    color: isUnread ? Colors.black87 : Colors.grey.shade700,
                  ),
                ),
              ),
              if (isUnread)
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: notification['color'],
                    shape: BoxShape.circle,
                  ),
                ),
            ],
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              Text(
                notification['content'],
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 14,
                    color: Colors.grey.shade500,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatTime(notification['timestamp']),
                    style: TextStyle(color: Colors.grey.shade500, fontSize: 12),
                  ),
                  const Spacer(),
                  Text(
                    _getNotificationTypeText(notification['type']),
                    style: TextStyle(
                      color: notification['color'],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
          onTap: () => _openNotification(notification),
        ),
      ),
    );
  }

  /// الحصول على نص نوع الإشعار
  String _getNotificationTypeText(String type) {
    switch (type) {
      case 'grades':
        return 'درجات';
      case 'attendance':
        return 'حضور';
      case 'fees':
        return 'رسوم';
      case 'activity':
        return 'نشاط';
      default:
        return 'عام';
    }
  }

  /// فتح إشعار
  void _openNotification(Map<String, dynamic> notification) {
    setState(() {
      notification['isRead'] = true;
    });

    // TODO: تنفيذ فتح تفاصيل الإشعار
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('فتح إشعار: ${notification['title']}')),
    );
  }

  // ===================================================================
  // دوال المواعيد
  // ===================================================================

  /// بناء رقاقة فلترة المواعيد
  Widget _buildAppointmentFilterChip(String label, bool isSelected) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        // TODO: تنفيذ منطق فلترة المواعيد
      },
      selectedColor: AppColors.parentColor.withValues(alpha: 0.2),
      checkmarkColor: AppColors.parentColor,
      labelStyle: TextStyle(
        color: isSelected ? AppColors.parentColor : Colors.grey.shade700,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
    );
  }

  /// عرض حوار حجز موعد جديد
  void _showBookAppointmentDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'حجز موعد جديد',
              style: TextStyle(
                color: AppColors.parentColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('اختر المعلم والموضوع لحجز موعد'),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'المعلم',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'teacher1',
                      child: Text('أ. محمد أحمد - الرياضيات'),
                    ),
                    DropdownMenuItem(
                      value: 'teacher2',
                      child: Text('أ. فاطمة علي - العلوم'),
                    ),
                    DropdownMenuItem(
                      value: 'admin',
                      child: Text('إدارة المدرسة'),
                    ),
                  ],
                  onChanged: (value) {},
                ),
                const SizedBox(height: 16),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'موضوع الموعد',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم إرسال طلب حجز الموعد')),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.parentColor,
                ),
                child: const Text('حجز الموعد'),
              ),
            ],
          ),
    );
  }

  /// بناء بطاقة موعد
  Widget _buildAppointmentCard(Map<String, dynamic> appointment) {
    final status = appointment['status'];
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (status) {
      case 'confirmed':
        statusColor = Colors.green;
        statusText = 'مؤكد';
        statusIcon = Icons.check_circle;
        break;
      case 'pending':
        statusColor = Colors.orange;
        statusText = 'قيد المراجعة';
        statusIcon = Icons.schedule;
        break;
      case 'cancelled':
        statusColor = Colors.red;
        statusText = 'ملغي';
        statusIcon = Icons.cancel;
        break;
      case 'completed':
        statusColor = Colors.blue;
        statusText = 'مكتمل';
        statusIcon = Icons.done_all;
        break;
      default:
        statusColor = Colors.grey;
        statusText = 'غير محدد';
        statusIcon = Icons.help;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: statusColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(statusIcon, color: statusColor, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        appointment['teacherName'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        appointment['subject'],
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    statusText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // تفاصيل الموعد
            Text(
              appointment['purpose'],
              style: const TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 6),
                Text(
                  _formatAppointmentDate(appointment['date']),
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(width: 16),
                Icon(Icons.access_time, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 6),
                Text(
                  '${appointment['duration']} دقيقة',
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),

            const SizedBox(height: 8),

            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    appointment['location'],
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),

            if (appointment['notes'].isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.note, size: 16, color: Colors.grey.shade600),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        appointment['notes'],
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // أزرار الإجراءات
            if (status == 'confirmed' || status == 'pending') ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _editAppointment(appointment),
                      icon: const Icon(Icons.edit, size: 16),
                      label: const Text('تعديل'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.parentColor,
                        side: BorderSide(color: AppColors.parentColor),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _cancelAppointment(appointment),
                      icon: const Icon(Icons.cancel, size: 16),
                      label: const Text('إلغاء'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: const BorderSide(color: Colors.red),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// تنسيق تاريخ الموعد
  String _formatAppointmentDate(DateTime date) {
    final weekdays = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];
    final months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    final hour = date.hour;
    final minute = date.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'م' : 'ص';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

    return '${weekdays[date.weekday % 7]} ${date.day} ${months[date.month - 1]} - $displayHour:$minute $period';
  }

  /// تعديل موعد
  void _editAppointment(Map<String, dynamic> appointment) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('تعديل موعد مع ${appointment['teacherName']}')),
    );
  }

  /// إلغاء موعد
  void _cancelAppointment(Map<String, dynamic> appointment) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إلغاء الموعد'),
            content: Text(
              'هل أنت متأكد من إلغاء الموعد مع ${appointment['teacherName']}؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('لا'),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    appointment['status'] = 'cancelled';
                  });
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم إلغاء الموعد')),
                  );
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('نعم، إلغاء'),
              ),
            ],
          ),
    );
  }

  // ===================================================================
  // دوال الطلبات
  // ===================================================================

  /// بناء رقاقة فلترة الطلبات
  Widget _buildRequestFilterChip(String label, bool isSelected) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        // TODO: تنفيذ منطق فلترة الطلبات
      },
      selectedColor: AppColors.parentColor.withValues(alpha: 0.2),
      checkmarkColor: AppColors.parentColor,
      labelStyle: TextStyle(
        color: isSelected ? AppColors.parentColor : Colors.grey.shade700,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
    );
  }

  /// عرض حوار طلب جديد
  void _showNewRequestDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'طلب جديد',
              style: TextStyle(
                color: AppColors.parentColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('اختر نوع الطلب المطلوب'),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'نوع الطلب',
                    border: OutlineInputBorder(),
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'certificate',
                      child: Text('شهادة سلوك'),
                    ),
                    DropdownMenuItem(value: 'grades', child: Text('كشف درجات')),
                    DropdownMenuItem(
                      value: 'absence',
                      child: Text('تبرير غياب'),
                    ),
                    DropdownMenuItem(value: 'transfer', child: Text('طلب نقل')),
                    DropdownMenuItem(
                      value: 'complaint',
                      child: Text('شكوى أو اقتراح'),
                    ),
                  ],
                  onChanged: (value) {},
                ),
                const SizedBox(height: 16),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'تفاصيل الطلب',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم إرسال الطلب بنجاح')),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.parentColor,
                ),
                child: const Text('إرسال الطلب'),
              ),
            ],
          ),
    );
  }

  /// بناء بطاقة طلب
  Widget _buildRequestCard(Map<String, dynamic> request) {
    final status = request['status'];
    final priority = request['priority'];

    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (status) {
      case 'pending':
        statusColor = Colors.orange;
        statusText = 'قيد المراجعة';
        statusIcon = Icons.schedule;
        break;
      case 'approved':
        statusColor = Colors.blue;
        statusText = 'موافق عليه';
        statusIcon = Icons.check_circle;
        break;
      case 'rejected':
        statusColor = Colors.red;
        statusText = 'مرفوض';
        statusIcon = Icons.cancel;
        break;
      case 'completed':
        statusColor = Colors.green;
        statusText = 'مكتمل';
        statusIcon = Icons.done_all;
        break;
      default:
        statusColor = Colors.grey;
        statusText = 'غير محدد';
        statusIcon = Icons.help;
    }

    Color priorityColor;
    switch (priority) {
      case 'urgent':
        priorityColor = Colors.red;
        break;
      case 'high':
        priorityColor = Colors.orange;
        break;
      case 'normal':
        priorityColor = Colors.blue;
        break;
      case 'low':
        priorityColor = Colors.grey;
        break;
      default:
        priorityColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: statusColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(statusIcon, color: statusColor, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        request['title'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _getRequestTypeText(request['type']),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: statusColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        statusText,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: priorityColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _getPriorityText(priority),
                        style: TextStyle(
                          color: priorityColor,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),

            // وصف الطلب
            Text(
              request['description'],
              style: const TextStyle(fontSize: 14, height: 1.4),
            ),

            const SizedBox(height: 12),

            // تواريخ الطلب
            Row(
              children: [
                Icon(Icons.send, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 6),
                Text(
                  'تاريخ التقديم: ${_formatRequestDate(request['submittedDate'])}',
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),

            const SizedBox(height: 4),

            Row(
              children: [
                Icon(Icons.event, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 6),
                Text(
                  'التاريخ المتوقع: ${_formatRequestDate(request['expectedDate'])}',
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),

            // المستندات المطلوبة
            if (request['documents'].isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.attach_file,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 6),
                        const Text(
                          'المستندات:',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    ...request['documents']
                        .map<Widget>(
                          (doc) => Padding(
                            padding: const EdgeInsets.only(left: 22, top: 2),
                            child: Text(
                              '• $doc',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ],
                ),
              ),
            ],

            // أزرار الإجراءات
            if (status == 'pending' || status == 'approved') ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _viewRequestDetails(request),
                      icon: const Icon(Icons.visibility, size: 16),
                      label: const Text('التفاصيل'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.parentColor,
                        side: BorderSide(color: AppColors.parentColor),
                      ),
                    ),
                  ),
                  if (status == 'pending') ...[
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _cancelRequest(request),
                        icon: const Icon(Icons.cancel, size: 16),
                        label: const Text('إلغاء'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                          side: const BorderSide(color: Colors.red),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// الحصول على نص نوع الطلب
  String _getRequestTypeText(String type) {
    switch (type) {
      case 'certificate':
        return 'شهادة';
      case 'grades':
        return 'درجات';
      case 'absence':
        return 'غياب';
      case 'transfer':
        return 'نقل';
      case 'complaint':
        return 'شكوى';
      default:
        return 'عام';
    }
  }

  /// الحصول على نص الأولوية
  String _getPriorityText(String priority) {
    switch (priority) {
      case 'urgent':
        return 'عاجل';
      case 'high':
        return 'عالي';
      case 'normal':
        return 'عادي';
      case 'low':
        return 'منخفض';
      default:
        return 'عادي';
    }
  }

  /// تنسيق تاريخ الطلب
  String _formatRequestDate(DateTime date) {
    final months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  /// عرض تفاصيل الطلب
  void _viewRequestDetails(Map<String, dynamic> request) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('عرض تفاصيل: ${request['title']}')));
  }

  /// إلغاء طلب
  void _cancelRequest(Map<String, dynamic> request) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إلغاء الطلب'),
            content: Text('هل أنت متأكد من إلغاء طلب "${request['title']}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('لا'),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    request['status'] = 'cancelled';
                  });
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم إلغاء الطلب')),
                  );
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('نعم، إلغاء'),
              ),
            ],
          ),
    );
  }

  // ===================================================================
  // دوال جلب البيانات من Firebase
  // ===================================================================

  /// جلب المحادثات من Firebase
  Future<void> _loadConversations() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final conversationsSnapshot =
          await FirebaseFirestore.instance
              .collection('conversations')
              .where('participants', arrayContains: user.uid)
              .orderBy('lastMessageTime', descending: true)
              .get();

      setState(() {
        _conversations =
            conversationsSnapshot.docs.map((doc) {
              final data = doc.data();
              return {
                'id': doc.id,
                'name': data['teacherName'] ?? 'غير محدد',
                'role': data['teacherRole'] ?? 'معلم',
                'avatar': data['teacherName']?.substring(0, 1) ?? '؟',
                'lastMessage': data['lastMessage'] ?? '',
                'timestamp':
                    (data['lastMessageTime'] as Timestamp?)?.toDate() ??
                    DateTime.now(),
                'unreadCount': data['unreadCount'] ?? 0,
                'isOnline': data['isOnline'] ?? false,
              };
            }).toList();
      });
    } catch (e) {
      print('خطأ في جلب المحادثات: $e');
    }
  }

  /// جلب الإشعارات من Firebase
  Future<void> _loadNotifications() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final notificationsSnapshot =
          await FirebaseFirestore.instance
              .collection('notifications')
              .where('parentId', isEqualTo: user.uid)
              .orderBy('timestamp', descending: true)
              .limit(50)
              .get();

      setState(() {
        _notifications =
            notificationsSnapshot.docs.map((doc) {
              final data = doc.data();
              final type = data['type'] ?? 'general';

              return {
                'id': doc.id,
                'title': data['title'] ?? 'إشعار',
                'content': data['content'] ?? '',
                'type': type,
                'timestamp':
                    (data['timestamp'] as Timestamp?)?.toDate() ??
                    DateTime.now(),
                'isRead': data['isRead'] ?? false,
                'icon': _getNotificationIcon(type),
                'color': _getNotificationColor(type),
              };
            }).toList();
      });
    } catch (e) {
      print('خطأ في جلب الإشعارات: $e');
    }
  }

  /// جلب المواعيد من Firebase
  Future<void> _loadAppointments() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final appointmentsSnapshot =
          await FirebaseFirestore.instance
              .collection('appointments')
              .where('parentId', isEqualTo: user.uid)
              .orderBy('date', descending: false)
              .get();

      setState(() {
        _appointments =
            appointmentsSnapshot.docs.map((doc) {
              final data = doc.data();
              return {
                'id': doc.id,
                'teacherName': data['teacherName'] ?? 'غير محدد',
                'subject': data['subject'] ?? '',
                'purpose': data['purpose'] ?? '',
                'date':
                    (data['date'] as Timestamp?)?.toDate() ?? DateTime.now(),
                'duration': data['duration'] ?? 30,
                'status': data['status'] ?? 'pending',
                'location': data['location'] ?? '',
                'notes': data['notes'] ?? '',
              };
            }).toList();
      });
    } catch (e) {
      print('خطأ في جلب المواعيد: $e');
    }
  }

  /// جلب الطلبات من Firebase
  Future<void> _loadRequests() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final requestsSnapshot =
          await FirebaseFirestore.instance
              .collection('requests')
              .where('parentId', isEqualTo: user.uid)
              .orderBy('submittedDate', descending: true)
              .get();

      setState(() {
        _requests =
            requestsSnapshot.docs.map((doc) {
              final data = doc.data();
              return {
                'id': doc.id,
                'type': data['type'] ?? 'general',
                'title': data['title'] ?? 'طلب',
                'description': data['description'] ?? '',
                'status': data['status'] ?? 'pending',
                'submittedDate':
                    (data['submittedDate'] as Timestamp?)?.toDate() ??
                    DateTime.now(),
                'expectedDate':
                    (data['expectedDate'] as Timestamp?)?.toDate() ??
                    DateTime.now(),
                'priority': data['priority'] ?? 'normal',
                'documents': List<String>.from(data['documents'] ?? []),
              };
            }).toList();
      });
    } catch (e) {
      print('خطأ في جلب الطلبات: $e');
    }
  }

  /// الحصول على أيقونة الإشعار حسب النوع
  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'grades':
        return Icons.grade;
      case 'attendance':
        return Icons.person_off;
      case 'fees':
        return Icons.payment;
      case 'activity':
        return Icons.event;
      default:
        return Icons.notifications;
    }
  }

  /// الحصول على لون الإشعار حسب النوع
  Color _getNotificationColor(String type) {
    switch (type) {
      case 'grades':
        return Colors.blue;
      case 'attendance':
        return Colors.orange;
      case 'fees':
        return Colors.red;
      case 'activity':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}

/// تعداد أنواع الرسائل
enum MessageType {
  inquiry, // استفسار
  complaint, // شكوى
  suggestion, // اقتراح
  request, // طلب
  emergency, // طارئ
  general, // عام
}

/// تعداد أولوية الرسائل
enum MessagePriority {
  low, // منخفضة
  normal, // عادية
  high, // مهمة
  urgent, // عاجلة
}

/// تعداد فلاتر المحادثات
enum ConversationFilter {
  all, // جميع المحادثات
  unread, // غير مقروءة
  important, // مهمة
  recent, // حديثة
}
