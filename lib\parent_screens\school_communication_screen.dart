import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/services/firebase_service.dart';

/// شاشة التواصل مع المدرسة للأولياء
///
/// هذه الشاشة تعتبر حلقة الوصل الرئيسية بين أولياء الأمور والمدرسة حيث تمكنهم من:
/// - إرسال رسائل مباشرة للمعلمين والإدارة
/// - استقبال الإشعارات والتنبيهات من المدرسة
/// - حجز مواعيد لاجتماعات أولياء الأمور
/// - تقديم الشكاوى والاقتراحات
/// - متابعة حالة الطلبات والاستفسارات
/// - عرض الإعلانات والأخبار المدرسية
/// - التواصل مع أولياء أمور آخرين (إذا كان مسموحاً)
/// - طلب تقارير خاصة أو معلومات إضافية
/// - الإبلاغ عن مشاكل أو حوادث
///
/// تدفق العمل في الشاشة:
/// 1. عرض صندوق الوارد للرسائل المستقبلة
/// 2. إمكانية إنشاء رسالة جديدة أو طلب
/// 3. اختيار المستقبل (معلم، إدارة، قسم معين)
/// 4. كتابة الرسالة وإرفاق الملفات إن أمكن
/// 5. إرسال الرسالة وتتبع حالتها
/// 6. استقبال الردود والتفاعل معها
/// 7. أرشفة المحادثات المكتملة
///
/// الألوان والتصميم:
/// - اللون الأساسي: برتقالي داكن (يرمز للتواصل والدفء)
/// - ألوان فرعية: برتقالي فاتح، أصفر، أخضر للنجاح
/// - تصميم يشبه تطبيقات المراسلة المألوفة
/// - فقاعات محادثة واضحة ومريحة للقراءة
/// - أيقونات تعبيرية لحالة الرسائل
class SchoolCommunicationScreen extends ConsumerStatefulWidget {
  /// معرف ولي الأمر الذي يستخدم النظام
  /// يستخدم لتحميل المحادثات والرسائل الخاصة به
  final String parentId;

  const SchoolCommunicationScreen({super.key, required this.parentId});

  @override
  ConsumerState<SchoolCommunicationScreen> createState() =>
      _SchoolCommunicationScreenState();
}

class _SchoolCommunicationScreenState
    extends ConsumerState<SchoolCommunicationScreen>
    with TickerProviderStateMixin {
  // ===================================================================
  // متحكمات الواجهة والرسوم المتحركة
  // ===================================================================

  /// متحكم التبويبات الرئيسية
  /// يدير التنقل بين: الرسائل، الإشعارات، المواعيد، الطلبات
  late TabController _tabController;

  /// متحكم الرسوم المتحركة لظهور الرسائل
  /// يستخدم لإظهار الرسائل الجديدة بشكل تدريجي وجذاب
  late AnimationController _messageAnimationController;

  /// الرسم المتحرك لانزلاق الرسائل
  late Animation<Offset> _messageSlideAnimation;

  /// متحكم كتابة الرسالة الجديدة
  final TextEditingController _messageController = TextEditingController();

  /// متحكم البحث في المحادثات
  final TextEditingController _searchController = TextEditingController();

  /// متحكم عنوان الرسالة
  final TextEditingController _subjectController = TextEditingController();

  /// مفتاح النموذج للتحقق من صحة البيانات
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // ===================================================================
  // متغيرات البيانات الرئيسية
  // ===================================================================

  /// قائمة المحادثات مع المدرسة
  /// كل محادثة تحتوي على سلسلة من الرسائل
  List<Map<String, dynamic>> _conversations = [];

  /// قائمة الإشعارات من المدرسة
  /// مرتبة حسب التاريخ من الأحدث إلى الأقدم
  List<Map<String, dynamic>> _notifications = [];

  /// قائمة المواعيد المحجوزة
  /// تشمل: اجتماعات أولياء أمور، مقابلات شخصية، فعاليات
  List<Map<String, dynamic>> _appointments = [];

  /// قائمة الطلبات المقدمة
  /// تشمل: طلبات شهادات، استفسارات، شكاوى
  List<Map<String, dynamic>> _requests = [];

  /// قائمة المعلمين والموظفين المتاحين للتواصل
  List<Map<String, dynamic>> _availableContacts = [];

  /// قائمة الأبناء لربط الرسائل بطالب محدد
  List<StudentModel> _children = [];

  // ===================================================================
  // متغيرات حالة التحميل والتفاعل
  // ===================================================================

  /// حالة تحميل البيانات الأولية
  bool _isLoading = false;

  /// حالة إرسال رسالة جديدة
  bool _isSendingMessage = false;

  /// حالة حجز موعد جديد
  bool _isBookingAppointment = false;

  /// نص البحث الحالي
  String _searchQuery = '';

  /// نوع الرسالة المحددة (استفسار، شكوى، اقتراح، إلخ)
  MessageType _selectedMessageType = MessageType.inquiry;

  /// المستقبل المحدد للرسالة
  String? _selectedRecipient;

  /// الطالب المرتبط بالرسالة (إن وجد)
  String? _selectedStudent;

  /// أولوية الرسالة (عادية، مهمة، عاجلة)
  MessagePriority _messagePriority = MessagePriority.normal;

  /// فلتر المحادثات (الكل، غير مقروءة، مهمة)
  ConversationFilter _conversationFilter = ConversationFilter.all;

  // ===================================================================
  // إحصائيات سريعة للعرض
  // ===================================================================

  /// عدد الرسائل غير المقروءة
  int _unreadMessages = 0;

  /// عدد الإشعارات الجديدة
  int _newNotifications = 0;

  /// عدد المواعيد القادمة
  int _upcomingAppointments = 0;

  /// عدد الطلبات المعلقة
  int _pendingRequests = 0;

  /// آخر رسالة مستقبلة
  Map<String, dynamic>? _lastMessage;

  /// حالة الاتصال مع الخادم
  bool _isConnected = true;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم التبويبات مع 4 تبويبات
    _tabController = TabController(length: 4, vsync: this);

    // إنشاء متحكم الرسوم المتحركة للرسائل
    _messageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // إنشاء الرسم المتحرك لانزلاق الرسائل
    _messageSlideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0), // يبدأ من اليمين
      end: Offset.zero, // ينتهي في الموضع النهائي
    ).animate(
      CurvedAnimation(
        parent: _messageAnimationController,
        curve: Curves.easeOutCubic,
      ),
    );

    // تحميل البيانات الأولية
    _loadCommunicationData();

    // إضافة مستمع لتغييرات التبويبات
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    // تنظيف الموارد عند إغلاق الشاشة
    _tabController.dispose();
    _messageAnimationController.dispose();
    _messageController.dispose();
    _searchController.dispose();
    _subjectController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع التبويبات والإجراءات
      appBar: AppBar(
        title: const Text(
          'التواصل مع المدرسة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.orange[800], // لون برتقالي داكن للتواصل
        elevation: 2,

        // التبويبات السفلية في شريط التطبيق
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          tabs: [
            Tab(
              icon: Stack(
                children: [
                  const Icon(Icons.message, size: 18),
                  if (_unreadMessages > 0)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 12,
                          minHeight: 12,
                        ),
                        child: Text(
                          '$_unreadMessages',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
              text: 'الرسائل',
            ),
            Tab(
              icon: Stack(
                children: [
                  const Icon(Icons.notifications, size: 18),
                  if (_newNotifications > 0)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 12,
                          minHeight: 12,
                        ),
                        child: Text(
                          '$_newNotifications',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              ),
              text: 'الإشعارات',
            ),
            const Tab(icon: Icon(Icons.event, size: 18), text: 'المواعيد'),
            const Tab(icon: Icon(Icons.assignment, size: 18), text: 'الطلبات'),
          ],
        ),

        // أزرار الإجراءات في شريط التطبيق
        actions: [
          // زر البحث في المحادثات
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () => _showSearchDialog(),
            tooltip: 'البحث في المحادثات',
          ),

          // زر الفلاتر
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: () => _showFilterDialog(),
            tooltip: 'فلترة المحادثات',
          ),

          // مؤشر حالة الاتصال
          Icon(
            _isConnected ? Icons.wifi : Icons.wifi_off,
            color: _isConnected ? Colors.white : Colors.red[300],
            size: 20,
          ),
          const SizedBox(width: 8),
        ],
      ),

      // محتوى التبويبات الرئيسية
      body: TabBarView(
        controller: _tabController,
        children: [
          // تبويب الرسائل والمحادثات
          _buildMessagesTab(),

          // تبويب الإشعارات
          _buildNotificationsTab(),

          // تبويب المواعيد
          _buildAppointmentsTab(),

          // تبويب الطلبات
          _buildRequestsTab(),
        ],
      ),

      // شريط المعلومات السفلي
      bottomNavigationBar: _buildBottomInfoBar(),

      // زر عائم لإنشاء رسالة جديدة
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showNewMessageDialog(),
        backgroundColor: Colors.orange[600],
        icon: const Icon(Icons.add_comment, color: Colors.white),
        label: const Text(
          'رسالة جديدة',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  // ===================================================================
  // دوال تحميل البيانات والتحليل
  // ===================================================================

  /// تحميل بيانات التواصل من قاعدة البيانات
  ///
  /// هذه الدالة تقوم بتحميل جميع البيانات المطلوبة:
  /// - المحادثات والرسائل السابقة
  /// - الإشعارات من المدرسة
  /// - المواعيد المحجوزة
  /// - الطلبات المقدمة
  /// - قائمة المعلمين والموظفين
  /// - معلومات الأبناء
  Future<void> _loadCommunicationData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final firebaseService = FirebaseService();

      // 1. جلب معلومات الأبناء الحقيقية من Firebase
      await _loadChildrenData(firebaseService);

      // 2. تهيئة القوائم الأخرى بقوائم فارغة حتى يتم تنفيذ الأنظمة المطلوبة
      setState(() {
        _conversations = [];
        _notifications = [];
        _appointments = [];
        _requests = [];
        _availableContacts = [];
      });

      // TODO: جلب المحادثات من Firebase عند إضافة نظام الرسائل
      // TODO: جلب الإشعارات من Firebase عند إضافة نظام الإشعارات
      // TODO: جلب المواعيد من Firebase عند إضافة نظام المواعيد
      // TODO: جلب الطلبات من Firebase عند إضافة نظام الطلبات

      // جلب قائمة المعلمين والموظفين المحدثة
      await _loadAvailableContacts();

      print('تم تحميل بيانات التواصل مع قائمة المعلمين المحدثة');

      // حساب الإحصائيات
      _calculateStatistics();

      // بدء الرسوم المتحركة
      _messageAnimationController.forward();
    } catch (e) {
      // معالجة الأخطاء وعرض رسالة مناسبة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _loadCommunicationData(),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تحميل بيانات الأبناء الحقيقية من Firebase
  Future<void> _loadChildrenData(FirebaseService firebaseService) async {
    try {
      // جلب جميع الطلاب من Firebase
      // في التطبيق الحقيقي، يجب ربط الطلاب بولي الأمر المحدد
      final studentsStream = firebaseService.getAllStudentsStream();

      // الاستماع لأول دفعة من البيانات
      await for (final students in studentsStream.take(1)) {
        setState(() {
          _children = students;
        });
        break; // الخروج بعد أول دفعة
      }
    } catch (e) {
      print('خطأ في تحميل بيانات الأبناء: $e');
      // في حالة الفشل، استخدم قائمة فارغة
      setState(() {
        _children = [];
      });
    }
  }

  /// تحميل قائمة المعلمين والموظفين المتاحين للتواصل
  Future<void> _loadAvailableContacts() async {
    try {
      // جلب قائمة المعلمين من Firebase
      final teachersSnapshot =
          await FirebaseFirestore.instance
              .collection('users')
              .where('role', isEqualTo: 'teacher')
              .get();

      // جلب قائمة الموظفين الإداريين من Firebase
      final adminSnapshot =
          await FirebaseFirestore.instance
              .collection('users')
              .where('role', isEqualTo: 'admin')
              .get();

      List<Map<String, dynamic>> contacts = [];

      // إضافة المعلمين إلى القائمة
      for (var doc in teachersSnapshot.docs) {
        final data = doc.data();
        contacts.add({
          'id': doc.id,
          'name': data['name'] ?? 'معلم غير محدد',
          'jobTitle': data['jobTitle'] ?? 'معلم',
          'role': 'teacher',
          'email': data['email'] ?? '',
          'phoneNumber': data['phoneNumber'],
          'profileImageUrl': data['profileImageUrl'],
          'gender': data['gender'],
          'governorate': data['governorate'],
          'nationality': data['nationality'] ?? 'غير محدد',
          'bio': data['bio'],
        });
      }

      // إضافة الموظفين الإداريين إلى القائمة
      for (var doc in adminSnapshot.docs) {
        final data = doc.data();
        contacts.add({
          'id': doc.id,
          'name': data['name'] ?? 'موظف غير محدد',
          'jobTitle': data['jobTitle'] ?? 'موظف إداري',
          'role': 'admin',
          'email': data['email'] ?? '',
          'phoneNumber': data['phoneNumber'],
          'profileImageUrl': data['profileImageUrl'],
          'gender': data['gender'],
          'governorate': data['governorate'],
          'nationality': data['nationality'] ?? 'غير محدد',
          'bio': data['bio'],
        });
      }

      setState(() {
        _availableContacts = contacts;
      });
    } catch (e) {
      print('خطأ في تحميل قائمة المعلمين: $e');
      setState(() {
        _availableContacts = [];
      });
    }
  }

  /// حساب الإحصائيات والملخصات
  ///
  /// يحسب جميع الإحصائيات المطلوبة لعرض الملخص
  void _calculateStatistics() {
    // حساب عدد الرسائل غير المقروءة
    _unreadMessages =
        _conversations.where((conv) => !(conv['isRead'] as bool)).length;

    // حساب عدد الإشعارات الجديدة
    _newNotifications =
        _notifications.where((notif) => !(notif['isRead'] as bool)).length;

    // حساب عدد المواعيد القادمة
    final now = DateTime.now();
    _upcomingAppointments =
        _appointments
            .where((app) => (app['date'] as DateTime).isAfter(now))
            .length;

    // حساب عدد الطلبات المعلقة
    _pendingRequests =
        _requests
            .where(
              (req) =>
                  req['status'] == 'processing' || req['status'] == 'pending',
            )
            .length;

    // تحديد آخر رسالة
    if (_conversations.isNotEmpty) {
      _lastMessage = _conversations.first;
    }
  }

  // ===================================================================
  // معالجات الأحداث والحوارات
  // ===================================================================

  /// معالج تغيير التبويبات
  void _onTabChanged() {
    if (!mounted) return;

    // تحديث البيانات حسب التبويب المحدد
    switch (_tabController.index) {
      case 0: // تبويب الرسائل
        break;
      case 1: // تبويب الإشعارات
        break;
      case 2: // تبويب المواعيد
        break;
      case 3: // تبويب الطلبات
        break;
    }
  }

  /// عرض حوار البحث
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('البحث في المحادثات'),
            content: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'ابحث عن رسالة أو محادثة...',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('بحث'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار الفلاتر
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('فلترة المحادثات'),
            content: const Text('سيتم تطبيق خيارات الفلترة قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار رسالة جديدة
  void _showNewMessageDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('رسالة جديدة'),
            content: const Text('سيتم تطبيق إنشاء رسالة جديدة قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إرسال'),
              ),
            ],
          ),
    );
  }

  // ===================================================================
  // دوال بناء التبويبات
  // ===================================================================

  /// بناء تبويب الرسائل والمحادثات
  Widget _buildMessagesTab() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    return const Center(
      child: Text(
        'الرسائل والمحادثات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء تبويب الإشعارات
  Widget _buildNotificationsTab() {
    return const Center(
      child: Text(
        'الإشعارات من المدرسة\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء تبويب المواعيد
  Widget _buildAppointmentsTab() {
    return const Center(
      child: Text(
        'المواعيد والاجتماعات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء تبويب الطلبات
  Widget _buildRequestsTab() {
    return const Center(
      child: Text(
        'الطلبات والاستفسارات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء شريط المعلومات السفلي
  Widget _buildBottomInfoBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        border: Border(top: BorderSide(color: Colors.orange[200]!)),
      ),
      child: Row(
        children: [
          Icon(Icons.message, size: 16, color: Colors.orange[600]),
          const SizedBox(width: 4),
          Text(
            'غير مقروءة: $_unreadMessages',
            style: TextStyle(fontSize: 12, color: Colors.orange[600]),
          ),
          const Spacer(),
          if (_lastMessage != null) ...[
            Icon(Icons.access_time, size: 16, color: Colors.orange[600]),
            const SizedBox(width: 4),
            Text(
              'آخر رسالة: ${_formatTime(_lastMessage!['lastMessageTime'] as DateTime)}',
              style: TextStyle(fontSize: 12, color: Colors.orange[600]),
            ),
          ],
        ],
      ),
    );
  }

  /// تنسيق الوقت للعرض
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
}

/// تعداد أنواع الرسائل
enum MessageType {
  inquiry, // استفسار
  complaint, // شكوى
  suggestion, // اقتراح
  request, // طلب
  emergency, // طارئ
  general, // عام
}

/// تعداد أولوية الرسائل
enum MessagePriority {
  low, // منخفضة
  normal, // عادية
  high, // مهمة
  urgent, // عاجلة
}

/// تعداد فلاتر المحادثات
enum ConversationFilter {
  all, // جميع المحادثات
  unread, // غير مقروءة
  important, // مهمة
  recent, // حديثة
}
