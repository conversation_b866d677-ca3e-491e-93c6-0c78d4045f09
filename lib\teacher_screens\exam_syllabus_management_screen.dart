import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/exam_model.dart';
import 'package:school_management_system/models/syllabus_management_models.dart';
import 'package:school_management_system/providers/exam_providers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/utils/async_error_handler.dart';
import 'package:school_management_system/widgets/enhanced_error_widget.dart';

/// شاشة إدارة منهج الامتحان للمعلمين
///
/// هذه الشاشة تسمح للمعلمين بإنشاء وإدارة مناهج الامتحانات
/// وتشمل إضافة المواضيع والفصول وتحديد الأوزان لكل موضوع
///
/// الوظائف الرئيسية:
/// - إنشاء منهج جديد للامتحان
/// - إضافة وتعديل المواضيع والفصول
/// - تحديد الأوزان النسبية لكل موضوع
/// - ربط المنهج بامتحان محدد
/// - معاينة وطباعة المنهج النهائي
/// - حفظ المسودات والتعديل عليها
///
/// تدفق العمل:
/// 1. اختيار الامتحان المراد إنشاء منهج له
/// 2. إضافة المواضيع الرئيسية
/// 3. إضافة الفصول الفرعية لكل موضوع
/// 4. تحديد الأوزان والدرجات
/// 5. مراجعة ومعاينة المنهج
/// 6. حفظ ونشر المنهج للطلاب
class ExamSyllabusManagementScreen extends ConsumerStatefulWidget {
  const ExamSyllabusManagementScreen({super.key});

  @override
  ConsumerState<ExamSyllabusManagementScreen> createState() =>
      _ExamSyllabusManagementScreenState();
}

class _ExamSyllabusManagementScreenState
    extends ConsumerState<ExamSyllabusManagementScreen>
    with TickerProviderStateMixin {
  // ===================================================================
  // متحكمات الواجهة والحالة
  // ===================================================================

  /// متحكم التبويبات الرئيسية
  /// يدير التنقل بين تبويبات: إنشاء منهج، المناهج الحالية، المناهج المكتملة
  late TabController _tabController;

  /// متحكمات النماذج لإدخال البيانات
  final _formKey = GlobalKey<FormState>();
  final _syllabusNameController = TextEditingController();
  final _syllabusDescriptionController = TextEditingController();
  final _topicNameController = TextEditingController();
  final _topicDescriptionController = TextEditingController();
  final _chapterNameController = TextEditingController();
  final _chapterDescriptionController = TextEditingController();
  final _searchController = TextEditingController();

  // ===================================================================
  // متغيرات الحالة الرئيسية
  // ===================================================================

  /// الامتحان المحدد حالياً لإنشاء المنهج له
  ExamModel? _selectedExam;

  /// المنهج الحالي قيد التحرير
  ExamSyllabusModel? _currentSyllabus;

  /// قائمة المواضيع المضافة للمنهج
  /// كل موضوع يحتوي على فصول فرعية وأوزان
  List<SyllabusTopicModel> _topics = [];

  /// الموضوع المحدد حالياً للتعديل
  SyllabusTopicModel? _selectedTopic;

  /// فهرس الموضوع المحدد في القائمة
  int _selectedTopicIndex = -1;

  /// حالة حفظ البيانات
  bool _isSaving = false;

  /// حالة تحميل البيانات
  bool _isLoading = false;

  /// إجمالي الوزن المخصص للمواضيع
  /// يجب أن يساوي 100% في النهاية
  double _totalWeight = 0.0;

  /// إجمالي الدرجات المخصصة
  int _totalMarks = 0;

  // ===================================================================
  // إعدادات المنهج
  // ===================================================================

  /// نوع المنهج (شامل، مختصر، مراجعة)
  SyllabusType _syllabusType = SyllabusType.comprehensive;

  /// مستوى الصعوبة (سهل، متوسط، صعب)
  DifficultyLevel _difficultyLevel = DifficultyLevel.medium;

  /// الوقت المقدر لدراسة المنهج (بالساعات)
  int _estimatedStudyHours = 0;

  /// هل المنهج منشور للطلاب؟
  bool _isPublished = false;

  /// تاريخ آخر تحديث للمنهج
  DateTime? _lastUpdated;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم التبويبات مع 3 تبويبات رئيسية
    _tabController = TabController(length: 3, vsync: this);

    // تحميل البيانات الأولية
    _loadInitialData();

    // إضافة مستمع لتغييرات التبويبات
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    // تنظيف الموارد عند إغلاق الشاشة
    _tabController.dispose();
    _syllabusNameController.dispose();
    _syllabusDescriptionController.dispose();
    _topicNameController.dispose();
    _topicDescriptionController.dispose();
    _chapterNameController.dispose();
    _chapterDescriptionController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع التبويبات
      appBar: AppBar(
        title: const Text(
          'إدارة مناهج الامتحانات',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.teal[800],
        elevation: 2,

        // التبويبات السفلية
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.add_circle_outline), text: 'إنشاء منهج'),
            Tab(icon: Icon(Icons.edit_note), text: 'المناهج الحالية'),
            Tab(icon: Icon(Icons.library_books), text: 'المناهج المكتملة'),
          ],
        ),

        // أزرار الإجراءات
        actions: [
          // زر البحث
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () => _showSearchDialog(),
            tooltip: 'البحث في المناهج',
          ),

          // زر المعاينة (يظهر فقط عند وجود منهج قيد التحرير)
          if (_currentSyllabus != null)
            IconButton(
              icon: const Icon(Icons.preview, color: Colors.white),
              onPressed: () => _previewSyllabus(),
              tooltip: 'معاينة المنهج',
            ),

          // زر الحفظ (يظهر فقط عند وجود تغييرات)
          if (_topics.isNotEmpty)
            IconButton(
              icon:
                  _isSaving
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : const Icon(Icons.save, color: Colors.white),
              onPressed: _isSaving ? null : () => _saveSyllabus(),
              tooltip: 'حفظ المنهج',
            ),
        ],
      ),

      // محتوى التبويبات
      body: TabBarView(
        controller: _tabController,
        children: [
          // تبويب إنشاء منهج جديد
          _buildCreateSyllabusTab(),

          // تبويب المناهج الحالية (قيد التحرير)
          _buildCurrentSyllabusesTab(),

          // تبويب المناهج المكتملة
          _buildCompletedSyllabusesTab(),
        ],
      ),

      // زر عائم لإضافة موضوع جديد (يظهر في تبويب الإنشاء فقط)
      floatingActionButton:
          _tabController.index == 0 && _selectedExam != null
              ? FloatingActionButton.extended(
                onPressed: () => _showAddTopicDialog(),
                backgroundColor: Colors.teal[600],
                icon: const Icon(Icons.add, color: Colors.white),
                label: const Text(
                  'إضافة موضوع',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              )
              : null,
    );
  }

  /// بناء تبويب إنشاء منهج جديد
  ///
  /// هذا التبويب يحتوي على:
  /// - اختيار الامتحان
  /// - إعدادات المنهج الأساسية
  /// - قائمة المواضيع والفصول
  /// - ملخص الأوزان والدرجات
  Widget _buildCreateSyllabusTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          const Text(
            'إنشاء منهج امتحان جديد',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'قم بإنشاء منهج شامل للامتحان مع تحديد المواضيع والأوزان',
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),

          // قسم اختيار الامتحان
          _buildExamSelectionSection(),

          const SizedBox(height: 24),

          // قسم إعدادات المنهج (يظهر فقط بعد اختيار الامتحان)
          if (_selectedExam != null) ...[
            _buildSyllabusSettingsSection(),
            const SizedBox(height: 24),
          ],

          // قسم المواضيع والفصول (يظهر فقط بعد اختيار الامتحان)
          if (_selectedExam != null) ...[
            _buildTopicsSection(),
            const SizedBox(height: 24),
          ],

          // ملخص الأوزان والدرجات (يظهر فقط عند وجود مواضيع)
          if (_topics.isNotEmpty) ...[
            _buildWeightsSummarySection(),
            const SizedBox(height: 24),
          ],

          // أزرار الإجراءات النهائية
          if (_selectedExam != null && _topics.isNotEmpty)
            _buildFinalActionsSection(),
        ],
      ),
    );
  }

  /// بناء قسم اختيار الامتحان
  ///
  /// يعرض قائمة بالامتحانات المتاحة للمعلم لإنشاء منهج لها
  Widget _buildExamSelectionSection() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(Icons.quiz, color: Colors.teal[600], size: 24),
                const SizedBox(width: 12),
                const Text(
                  'اختيار الامتحان',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'اختر الامتحان الذي تريد إنشاء منهج له',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            const SizedBox(height: 20),

            // قائمة الامتحانات المتاحة
            Consumer(
              builder: (context, ref, child) {
                final upcomingExamsAsync = ref.watch(
                  upcomingExamsStreamProvider,
                );

                return upcomingExamsAsync.when(
                  loading:
                      () => const Center(
                        child: Padding(
                          padding: EdgeInsets.all(20.0),
                          child: LoadingIndicator(),
                        ),
                      ),
                  error:
                      (error, stack) => EnhancedErrorWidget(
                        error: error,
                        customMessage: 'خطأ في تحميل الامتحانات',
                        onRetry:
                            () => ref.invalidate(upcomingExamsStreamProvider),
                        showDetails: true,
                        padding: const EdgeInsets.all(20.0),
                      ),
                  data: (exams) {
                    if (exams.isEmpty) {
                      return Center(
                        child: Column(
                          children: [
                            Icon(
                              Icons.quiz_outlined,
                              size: 64,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'لا توجد امتحانات متاحة',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'لا توجد امتحانات قادمة تحتاج إلى منهج',
                              style: TextStyle(
                                color: Colors.grey[500],
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    return Column(
                      children:
                          exams.map((exam) => _buildExamCard(exam)).toList(),
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة امتحان واحد للاختيار
  ///
  /// تعرض معلومات الامتحان مع إمكانية الاختيار
  Widget _buildExamCard(ExamModel exam) {
    final isSelected = _selectedExam?.id == exam.id;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _selectExam(exam),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? Colors.teal[600]! : Colors.grey[300]!,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(12),
            color: isSelected ? Colors.teal[50] : Colors.white,
          ),
          child: Row(
            children: [
              // أيقونة الامتحان
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.teal[600] : Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.quiz,
                  color: isSelected ? Colors.white : Colors.grey[600],
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),

              // معلومات الامتحان
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم الامتحان
                    Text(
                      exam.name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: isSelected ? Colors.teal[800] : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),

                    // تفاصيل الامتحان
                    Row(
                      children: [
                        Icon(Icons.category, size: 14, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          exam.type.arabicName,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(
                          Icons.calendar_today,
                          size: 14,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${exam.startDate.day}/${exam.startDate.month}/${exam.startDate.year}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),

                    // عدد الصفوف المشاركة
                    Row(
                      children: [
                        Icon(Icons.class_, size: 14, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Text(
                          '${exam.classIds.length} صف مشارك',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // مؤشر الاختيار
              if (isSelected)
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.teal[600],
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.check, color: Colors.white, size: 16),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // ===================================================================
  // دوال الإجراءات والتفاعل
  // ===================================================================

  /// تحميل البيانات الأولية عند فتح الشاشة
  ///
  /// يتم تحميل:
  /// - قائمة الامتحانات المتاحة
  /// - المناهج المحفوظة مسبقاً
  /// - إعدادات المعلم
  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
    });

    // استخدام نظام معالجة الأخطاء المحسن
    await AsyncErrorHandler.execute<void>(
      operation: () async {
        // TODO: تحميل البيانات من قاعدة البيانات
        // يمكن إضافة تحميل المناهج المحفوظة مسبقاً هنا

        await Future.delayed(
          const Duration(milliseconds: 500),
        ); // محاكاة التحميل
      },
      context: context,
      onError: () {
        // يمكن إضافة منطق إضافي عند الفشل
      },
    );

    // إعادة تعيين حالة التحميل
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// معالج تغيير التبويبات
  ///
  /// يتم استدعاؤه عند التنقل بين التبويبات المختلفة
  void _onTabChanged() {
    if (!mounted) return;

    // إعادة تعيين بعض المتغيرات عند تغيير التبويب
    setState(() {
      _selectedTopic = null;
      _selectedTopicIndex = -1;
    });

    // تحديث البيانات حسب التبويب المحدد
    switch (_tabController.index) {
      case 0: // تبويب الإنشاء
        // لا حاجة لإجراءات خاصة
        break;
      case 1: // تبويب المناهج الحالية
        _loadCurrentSyllabuses();
        break;
      case 2: // تبويب المناهج المكتملة
        _loadCompletedSyllabuses();
        break;
    }
  }

  /// اختيار امتحان لإنشاء منهج له
  ///
  /// يتم حفظ الامتحان المحدد وإعداد المنهج الجديد
  void _selectExam(ExamModel exam) {
    setState(() {
      _selectedExam = exam;

      // إنشاء منهج جديد للامتحان المحدد
      _currentSyllabus = ExamSyllabusModel(
        id: '', // سيتم تعيينه عند الحفظ
        examId: exam.id,
        examName: exam.name,
        subjectId: '', // TODO: إضافة subjectId للامتحان
        teacherId: 'current_teacher_id', // TODO: جلب من المصادقة
        syllabusName:
            _syllabusNameController.text.isEmpty
                ? 'منهج ${exam.name}'
                : _syllabusNameController.text,
        description: _syllabusDescriptionController.text,
        topics: [],
        totalMarks: 0,
        estimatedStudyHours: _estimatedStudyHours,
        difficultyLevel: _difficultyLevel,
        syllabusType: _syllabusType,
        isPublished: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // تحديث اسم المنهج التلقائي
      if (_syllabusNameController.text.isEmpty) {
        _syllabusNameController.text = 'منهج ${exam.name}';
      }
    });

    // إظهار رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم اختيار امتحان: ${exam.name}'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// عرض حوار البحث في المناهج
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('البحث في المناهج'),
            content: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'ابحث عن منهج...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  // TODO: تطبيق البحث
                  Navigator.pop(context);
                  _performSearch(_searchController.text);
                },
                child: const Text('بحث'),
              ),
            ],
          ),
    );
  }

  /// تنفيذ البحث في المناهج
  void _performSearch(String query) {
    // TODO: تطبيق منطق البحث
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('البحث عن: $query'), backgroundColor: Colors.blue),
    );
  }

  /// معاينة المنهج الحالي
  void _previewSyllabus() {
    if (_currentSyllabus == null) return;

    // TODO: فتح شاشة معاينة المنهج
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تطبيق معاينة المنهج قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// حفظ المنهج الحالي
  Future<void> _saveSyllabus() async {
    if (_currentSyllabus == null || _topics.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يوجد منهج للحفظ'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // التحقق من صحة البيانات
    if (!_validateSyllabus()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    // استخدام نظام معالجة الأخطاء المحسن لحفظ المنهج
    await AsyncErrorHandler.executeAdvanced<void>(
      operation: () async {
        // تحديث المنهج بالمواضيع الحالية
        final updatedSyllabus = _currentSyllabus!.copyWith(
          topics: _topics,
          totalMarks: _totalMarks,
          updatedAt: DateTime.now(),
          syllabusName: _syllabusNameController.text,
          description: _syllabusDescriptionController.text,
          estimatedStudyHours: _estimatedStudyHours,
          difficultyLevel: _difficultyLevel,
          syllabusType: _syllabusType,
        );

        // TODO: حفظ في قاعدة البيانات
        await Future.delayed(const Duration(seconds: 2)); // محاكاة الحفظ

        setState(() {
          _currentSyllabus = updatedSyllabus;
        });
      },
      context: context,
      maxRetries: 2,
      timeout: const Duration(seconds: 15),
      loadingMessage: 'جاري حفظ المنهج...',
      successMessage: 'تم حفظ المنهج بنجاح',
      showSuccessMessage: true,
      onError: () {
        // يمكن إضافة منطق إضافي عند الفشل
      },
    );

    // إعادة تعيين حالة الحفظ
    if (mounted) {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// التحقق من صحة بيانات المنهج
  bool _validateSyllabus() {
    // التحقق من وجود اسم للمنهج
    if (_syllabusNameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال اسم المنهج'),
          backgroundColor: Colors.orange,
        ),
      );
      return false;
    }

    // التحقق من وجود مواضيع
    if (_topics.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة موضوع واحد على الأقل'),
          backgroundColor: Colors.orange,
        ),
      );
      return false;
    }

    // التحقق من مجموع الأوزان
    if ((_totalWeight - 100.0).abs() > 0.01) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'مجموع الأوزان يجب أن يساوي 100% (حالياً: ${_totalWeight.toStringAsFixed(1)}%)',
          ),
          backgroundColor: Colors.orange,
        ),
      );
      return false;
    }

    return true;
  }

  /// عرض حوار إضافة موضوع جديد
  void _showAddTopicDialog() {
    // إعادة تعيين الحقول
    _topicNameController.clear();
    _topicDescriptionController.clear();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إضافة موضوع جديد'),
            content: SizedBox(
              width: 400,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // اسم الموضوع
                  TextField(
                    controller: _topicNameController,
                    decoration: const InputDecoration(
                      labelText: 'اسم الموضوع *',
                      hintText: 'مثال: الجبر',
                      border: OutlineInputBorder(),
                    ),
                    autofocus: true,
                  ),
                  const SizedBox(height: 16),

                  // وصف الموضوع
                  TextField(
                    controller: _topicDescriptionController,
                    decoration: const InputDecoration(
                      labelText: 'وصف الموضوع',
                      hintText: 'وصف مختصر للموضوع...',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  if (_topicNameController.text.trim().isNotEmpty) {
                    _addNewTopic();
                    Navigator.pop(context);
                  }
                },
                child: const Text('إضافة'),
              ),
            ],
          ),
    );
  }

  /// إضافة موضوع جديد للمنهج
  void _addNewTopic() {
    final newTopic = SyllabusTopicModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: _topicNameController.text.trim(),
      description: _topicDescriptionController.text.trim(),
      weight: 0.0, // سيتم تحديده لاحقاً
      marks: 0, // سيتم تحديده لاحقاً
      estimatedHours: 0,
      chapters: [],
      isCompleted: false,
      order: _topics.length + 1,
    );

    setState(() {
      _topics.add(newTopic);
      _updateTotals();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة الموضوع: ${newTopic.name}'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// تحديث المجاميع الإجمالية
  void _updateTotals() {
    _totalWeight = _topics.fold(0.0, (sum, topic) => sum + topic.weight);
    _totalMarks = _topics.fold(0, (sum, topic) => sum + topic.marks);
  }

  /// تحميل المناهج الحالية
  void _loadCurrentSyllabuses() {
    // TODO: تحميل المناهج قيد التحرير
  }

  /// تحميل المناهج المكتملة
  void _loadCompletedSyllabuses() {
    // TODO: تحميل المناهج المكتملة
  }

  /// بناء تبويب المناهج الحالية
  Widget _buildCurrentSyllabusesTab() {
    return const Center(
      child: Text(
        'المناهج الحالية\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء تبويب المناهج المكتملة
  Widget _buildCompletedSyllabusesTab() {
    return const Center(
      child: Text(
        'المناهج المكتملة\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء قسم إعدادات المنهج
  Widget _buildSyllabusSettingsSection() {
    return const Center(
      child: Text(
        'إعدادات المنهج\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  /// بناء قسم المواضيع
  Widget _buildTopicsSection() {
    return const Center(
      child: Text(
        'قسم المواضيع\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  /// بناء ملخص الأوزان
  Widget _buildWeightsSummarySection() {
    return const Center(
      child: Text(
        'ملخص الأوزان\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  /// بناء قسم الإجراءات النهائية
  Widget _buildFinalActionsSection() {
    return const Center(
      child: Text(
        'الإجراءات النهائية\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }
}
