import 'package:flutter/material.dart';
import 'package:school_management_system/teacher_screens/teacher_home_page.dart';
import 'package:school_management_system/teacher_screens/exam_syllabus_management_screen.dart';
import 'package:school_management_system/teacher_screens/grade_entry_screen.dart';
import 'package:school_management_system/teacher_screens/teacher_exam_schedule_screen.dart';
import 'package:school_management_system/services/firebase_service.dart';
import 'package:school_management_system/shared/app_theme.dart';
import 'package:school_management_system/widgets/unified_components.dart';

/// لوحة التحكم المحسنة للمعلمين
///
/// تطبق التصميم الموحد الجديد مع تحسينات في:
/// - نظام الألوان والمسافات الموحد
/// - تصميم متجاوب محسن
/// - تجربة مستخدم محسنة للمعلمين
/// - هوية بصرية مميزة للمعلمين
///
/// الوظائف الرئيسية:
/// - الصفحة الرئيسية للمعلم
/// - إدارة مناهج الامتحانات
/// - إدخال الدرجات
/// - جدول امتحانات المعلم
///
/// التصميم المحسن:
/// - تطبيق النظام الموحد للألوان والخطوط
/// - تصميم متجاوب محسن للويب والجوال
/// - قائمة تنقل محسنة مع أيقونات واضحة
/// - لون مميز للمعلمين (بنفسجي)
class TeacherMainLayout extends StatefulWidget {
  const TeacherMainLayout({super.key});

  @override
  State<TeacherMainLayout> createState() => _TeacherMainLayoutState();
}

class _TeacherMainLayoutState extends State<TeacherMainLayout> {
  /// فهرس الصفحة المحددة حالياً
  int _selectedIndex = 0;

  /// قائمة الصفحات التي يمكن للمعلم التنقل بينها
  final List<Widget> _pages = [
    const TeacherHomePage(), // 0: الصفحة الرئيسية للمعلم
    const ExamSyllabusManagementScreen(), // 1: إدارة مناهج الامتحانات
    const GradeEntryScreen(), // 2: إدخال الدرجات
    const TeacherExamScheduleScreen(), // 3: جدول امتحانات المعلم
    // يمكن إضافة المزيد من الشاشات هنا
  ];

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 1000;

        return Scaffold(
          backgroundColor: AppColors.background,
          appBar: AppBar(
            title: const Text(
              'لوحة تحكم المعلم',
              style: TextStyle(
                fontSize: AppTextSizes.headlineMedium,
                fontWeight: FontWeight.bold,
                color: AppColors.textOnPrimary,
              ),
            ),
            backgroundColor: AppColors.teacherColor,
            foregroundColor: AppColors.textOnPrimary,
            elevation: AppElevation.medium,
            centerTitle: true,
            actions: [
              // زر الإشعارات
              IconButton(
                icon: const Icon(Icons.notifications_outlined),
                tooltip: 'الإشعارات',
                onPressed: () {
                  // TODO: فتح شاشة الإشعارات
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('سيتم تطبيق الإشعارات قريباً'),
                      backgroundColor: AppColors.info,
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          AppBorderRadius.medium,
                        ),
                      ),
                    ),
                  );
                },
              ),

              // زر تسجيل الخروج
              IconButton(
                icon: const Icon(Icons.logout),
                tooltip: 'تسجيل الخروج',
                onPressed: () {
                  _showLogoutDialog();
                },
              ),
            ],
            leading:
                isSmallScreen
                    ? Builder(
                      builder:
                          (context) => IconButton(
                            icon: const Icon(Icons.menu),
                            onPressed: () => Scaffold.of(context).openDrawer(),
                          ),
                    )
                    : null,
          ),

          // قائمة منسدلة للشاشات الصغيرة
          drawer: isSmallScreen ? _buildDrawer() : null,

          // محتوى الشاشة
          body: Row(
            children: [
              // قائمة التنقل الجانبية للشاشات الكبيرة
              if (!isSmallScreen)
                SingleChildScrollView(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height,
                    ),
                    child: IntrinsicHeight(
                      child: NavigationRail(
                        selectedIndex: _selectedIndex,
                        onDestinationSelected: (int index) {
                          setState(() {
                            _selectedIndex = index;
                          });
                        },
                        labelType: NavigationRailLabelType.all,
                        backgroundColor: AppColors.teacherColor.withValues(
                          alpha: 0.05,
                        ),
                        selectedIconTheme: const IconThemeData(
                          color: AppColors.teacherColor,
                        ),
                        unselectedIconTheme: const IconThemeData(
                          color: AppColors.textSecondary,
                        ),
                        selectedLabelTextStyle: const TextStyle(
                          color: AppColors.teacherColor,
                          fontWeight: FontWeight.bold,
                          fontSize: AppTextSizes.labelMedium,
                        ),
                        unselectedLabelTextStyle: const TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: AppTextSizes.labelMedium,
                        ),
                        destinations: _buildNavDestinations(),
                      ),
                    ),
                  ),
                ),

              // خط فاصل
              if (!isSmallScreen)
                VerticalDivider(
                  thickness: 1,
                  width: 1,
                  color: Colors.green[200],
                ),

              // محتوى الصفحة المحددة
              Expanded(child: _pages[_selectedIndex]),
            ],
          ),
        );
      },
    );
  }

  /// بناء وجهات التنقل للقائمة الجانبية
  List<NavigationRailDestination> _buildNavDestinations() {
    return [
      // الصفحة الرئيسية
      const NavigationRailDestination(
        icon: Icon(Icons.home_outlined),
        selectedIcon: Icon(Icons.home),
        label: Text('الرئيسية'),
      ),

      // إدارة مناهج الامتحانات
      const NavigationRailDestination(
        icon: Icon(Icons.book_outlined),
        selectedIcon: Icon(Icons.book),
        label: Text('مناهج الامتحانات'),
      ),

      // إدخال الدرجات
      const NavigationRailDestination(
        icon: Icon(Icons.grade_outlined),
        selectedIcon: Icon(Icons.grade),
        label: Text('إدخال الدرجات'),
      ),

      // جدول امتحانات المعلم
      const NavigationRailDestination(
        icon: Icon(Icons.schedule_outlined),
        selectedIcon: Icon(Icons.schedule),
        label: Text('جدول امتحاناتي'),
      ),
    ];
  }

  /// بناء القائمة المنسدلة للشاشات الصغيرة
  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          // رأس القائمة
          DrawerHeader(
            decoration: BoxDecoration(color: Colors.green[800]),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(Icons.school, color: Colors.white, size: 48),
                SizedBox(height: 8),
                Text(
                  'لوحة تحكم المعلم',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'نظام إدارة المدرسة',
                  style: TextStyle(color: Colors.white70, fontSize: 14),
                ),
              ],
            ),
          ),

          // عناصر القائمة
          _buildDrawerItem(
            icon: Icons.home,
            title: 'الصفحة الرئيسية',
            index: 0,
          ),
          _buildDrawerItem(
            icon: Icons.book,
            title: 'مناهج الامتحانات',
            index: 1,
          ),
          _buildDrawerItem(icon: Icons.grade, title: 'إدخال الدرجات', index: 2),
          _buildDrawerItem(
            icon: Icons.schedule,
            title: 'جدول امتحاناتي',
            index: 3,
          ),

          const Divider(),

          // الإشعارات
          ListTile(
            leading: const Icon(Icons.notifications_outlined),
            title: const Text('الإشعارات'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('سيتم تطبيق الإشعارات قريباً'),
                  backgroundColor: Colors.blue,
                ),
              );
            },
          ),

          // تسجيل الخروج
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text('تسجيل الخروج'),
            onTap: () {
              Navigator.pop(context);
              _showLogoutDialog();
            },
          ),
        ],
      ),
    );
  }

  /// بناء عنصر واحد في القائمة المنسدلة
  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required int index,
  }) {
    final isSelected = _selectedIndex == index;

    return ListTile(
      leading: Icon(icon, color: isSelected ? Colors.green[800] : null),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? Colors.green[800] : null,
          fontWeight: isSelected ? FontWeight.bold : null,
        ),
      ),
      selected: isSelected,
      selectedTileColor: Colors.green[50],
      onTap: () {
        setState(() {
          _selectedIndex = index;
        });
        Navigator.pop(context); // إغلاق القائمة المنسدلة
      },
    );
  }

  /// عرض حوار تأكيد تسجيل الخروج
  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تسجيل الخروج'),
            content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  FirebaseService().signOut();
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text(
                  'تسجيل الخروج',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }
}
