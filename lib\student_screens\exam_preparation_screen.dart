import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/exam_model.dart';
import 'package:school_management_system/models/syllabus_management_models.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/utils/async_error_handler.dart';

/// شاشة الاستعداد للامتحان للطلاب
///
/// هذه الشاشة تعتبر من أهم الشاشات في النظام للطلاب حيث تساعدهم على:
/// - عرض منهج الامتحان بشكل تفصيلي ومنظم
/// - تتبع تقدمهم في مراجعة المواضيع والفصول
/// - الحصول على نصائح وإرشادات من المعلم
/// - جدولة وقت المراجعة بشكل فعال
/// - تقدير الوقت المطلوب لكل موضوع
/// - عرض الأولويات والمواضيع المهمة
/// - تسجيل الملاحظات الشخصية
/// - متابعة الإنجاز والتحفيز للمتابعة
///
/// تدفق العمل في الشاشة:
/// 1. اختيار الامتحان المراد الاستعداد له
/// 2. عرض منهج الامتحان مقسم إلى مواضيع وفصول
/// 3. تتبع التقدم في المراجعة لكل فصل
/// 4. عرض الإحصائيات والتقدم العام
/// 5. تقديم نصائح وتوجيهات للمراجعة
/// 6. إمكانية تسجيل الملاحظات والأسئلة
///
/// التصميم والواجهة:
/// - تصميم جذاب ومحفز للطلاب
/// - ألوان مريحة للعين أثناء المراجعة الطويلة
/// - تنظيم واضح للمعلومات
/// - مؤشرات تقدم بصرية
/// - أيقونات معبرة وسهلة الفهم
/// - تفاعل سلس ومريح
class ExamPreparationScreen extends ConsumerStatefulWidget {
  /// معرف الطالب الذي يستخدم الشاشة
  /// يستخدم لتحميل البيانات الخاصة بالطالب وحفظ تقدمه
  final String studentId;

  const ExamPreparationScreen({super.key, required this.studentId});

  @override
  ConsumerState<ExamPreparationScreen> createState() =>
      _ExamPreparationScreenState();
}

class _ExamPreparationScreenState extends ConsumerState<ExamPreparationScreen>
    with TickerProviderStateMixin {
  // ===================================================================
  // متحكمات الواجهة والرسوم المتحركة
  // ===================================================================

  /// متحكم التبويبات الرئيسية في الشاشة
  /// يدير التنقل بين: المنهج، التقدم، النصائح، الملاحظات
  late TabController _tabController;

  /// متحكم الرسوم المتحركة لمؤشر التقدم الدائري
  /// يستخدم لإظهار نسبة الإنجاز بشكل جذاب ومتحرك
  late AnimationController _progressAnimationController;

  /// الرسم المتحرك لقيمة التقدم من 0 إلى النسبة الفعلية
  late Animation<double> _progressAnimation;

  /// متحكم البحث في المنهج
  /// يسمح للطالب بالبحث عن مواضيع أو فصول معينة
  final TextEditingController _searchController = TextEditingController();

  /// متحكم إدخال الملاحظات الجديدة
  final TextEditingController _noteController = TextEditingController();

  // ===================================================================
  // متغيرات الحالة الرئيسية
  // ===================================================================

  /// الامتحان المحدد حالياً للاستعداد له
  /// يحتوي على جميع تفاصيل الامتحان والمنهج المرتبط به
  ExamModel? _selectedExam;

  /// منهج الامتحان المحدد مع جميع المواضيع والفصول
  /// يتم تحميله من قاعدة البيانات بناءً على الامتحان المحدد
  ExamSyllabusModel? _examSyllabus;

  /// خريطة تتبع تقدم الطالب في كل فصل
  /// المفتاح: معرف الفصل، القيمة: هل تم إكمال مراجعته أم لا
  final Map<String, bool> _chapterProgress = {};

  /// خريطة ملاحظات الطالب لكل فصل
  /// المفتاح: معرف الفصل، القيمة: نص الملاحظة
  final Map<String, String> _chapterNotes = {};

  /// خريطة تقييم صعوبة كل فصل من وجهة نظر الطالب
  /// المفتاح: معرف الفصل، القيمة: مستوى الصعوبة (1-5)
  final Map<String, int> _chapterDifficulty = {};

  /// خريطة الوقت المقدر لمراجعة كل فصل (بالدقائق)
  /// المفتاح: معرف الفصل، القيمة: الوقت المقدر
  final Map<String, int> _chapterEstimatedTime = {};

  /// خريطة الوقت الفعلي المستغرق في مراجعة كل فصل
  /// المفتاح: معرف الفصل، القيمة: الوقت الفعلي بالدقائق
  final Map<String, int> _chapterActualTime = {};

  // ===================================================================
  // متغيرات حالة التحميل والتفاعل
  // ===================================================================

  /// حالة تحميل البيانات الأولية
  bool _isLoading = false;

  /// حالة حفظ التقدم والملاحظات
  bool _isSaving = false;

  /// حالة تحديث البيانات
  bool _isRefreshing = false;

  // ===================================================================
  // إحصائيات التقدم والإنجاز
  // ===================================================================

  /// إجمالي عدد الفصول في المنهج
  int _totalChapters = 0;

  /// عدد الفصول المكتملة
  int _completedChapters = 0;

  /// نسبة الإنجاز الإجمالية (0-100)
  double _overallProgress = 0.0;

  /// إجمالي الوقت المقدر للمراجعة (بالساعات)
  double _totalEstimatedHours = 0.0;

  /// إجمالي الوقت المستغرق فعلياً (بالساعات)
  double _totalActualHours = 0.0;

  /// عدد الأيام المتبقية للامتحان
  int _daysRemaining = 0;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم التبويبات مع 4 تبويبات رئيسية
    _tabController = TabController(length: 4, vsync: this);

    // إنشاء متحكم الرسوم المتحركة لمؤشر التقدم
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500), // مدة الرسم المتحرك
      vsync: this,
    );

    // إنشاء الرسم المتحرك للتقدم مع منحنى سلس
    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _progressAnimationController,
        curve: Curves.easeInOut, // منحنى سلس للحركة
      ),
    );

    // تحميل البيانات الأولية عند فتح الشاشة
    _loadInitialData();

    // إضافة مستمع لتغييرات التبويبات لتحديث البيانات حسب الحاجة
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    // تنظيف الموارد عند إغلاق الشاشة لتجنب تسريب الذاكرة
    _tabController.dispose();
    _progressAnimationController.dispose();
    _searchController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع التبويبات والإجراءات
      appBar: AppBar(
        title: const Text(
          'الاستعداد للامتحان',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.purple[800], // لون مميز للطلاب
        elevation: 2,

        // التبويبات السفلية في شريط التطبيق
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 13,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.book, size: 20), text: 'المنهج'),
            Tab(icon: Icon(Icons.trending_up, size: 20), text: 'التقدم'),
            Tab(icon: Icon(Icons.lightbulb, size: 20), text: 'النصائح'),
            Tab(icon: Icon(Icons.note, size: 20), text: 'ملاحظاتي'),
          ],
        ),

        // أزرار الإجراءات في شريط التطبيق
        actions: [
          // زر البحث في المنهج
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () => _showSearchDialog(),
            tooltip: 'البحث في المنهج',
          ),

          // زر الفلاتر والترتيب
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: () => _showFilterDialog(),
            tooltip: 'فلترة وترتيب الفصول',
          ),

          // زر حفظ التقدم
          if (_isSaving)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.save, color: Colors.white),
              onPressed: () => _saveProgress(),
              tooltip: 'حفظ التقدم',
            ),
        ],
      ),

      // محتوى التبويبات الرئيسية
      body: TabBarView(
        controller: _tabController,
        children: [
          // تبويب المنهج - عرض المواضيع والفصول
          _buildSyllabusTab(),

          // تبويب التقدم - إحصائيات ومؤشرات الإنجاز
          _buildProgressTab(),

          // تبويب النصائح - إرشادات وتوجيهات للمراجعة
          _buildTipsTab(),

          // تبويب الملاحظات - ملاحظات الطالب الشخصية
          _buildNotesTab(),
        ],
      ),

      // شريط المعلومات السفلي يعرض معلومات سريعة
      bottomNavigationBar: _buildBottomInfoBar(),

      // زر عائم لإضافة ملاحظة سريعة
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddNoteDialog(),
        backgroundColor: Colors.purple[600],
        icon: const Icon(Icons.add_comment, color: Colors.white),
        label: const Text(
          'ملاحظة سريعة',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  // ===================================================================
  // دوال تحميل البيانات والتهيئة
  // ===================================================================

  /// تحميل البيانات الأولية عند فتح الشاشة
  ///
  /// هذه الدالة تقوم بتحميل جميع البيانات المطلوبة للشاشة:
  /// - قائمة الامتحانات المتاحة للطالب
  /// - تقدم الطالب المحفوظ مسبقاً
  /// - الملاحظات والتقييمات السابقة
  /// - حساب الإحصائيات الأولية
  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: تحميل البيانات الفعلية من قاعدة البيانات
      // هنا سيتم استدعاء الخدمات لجلب:
      // 1. قائمة امتحانات الطالب
      // 2. تقدم الطالب المحفوظ
      // 3. الملاحظات والتقييمات

      // محاكاة تحميل البيانات
      await Future.delayed(const Duration(seconds: 2));

      // تحديث الإحصائيات بعد تحميل البيانات
      _updateStatistics();

      // بدء الرسم المتحرك لمؤشر التقدم
      _progressAnimationController.forward();
    } catch (e) {
      // استخدام نظام معالجة الأخطاء المحسن للطلاب
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.school, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('خطأ في تحميل بيانات الاستعداد: $e')),
              ],
            ),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _loadInitialData(),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// معالج تغيير التبويبات
  ///
  /// يتم استدعاؤها عند تغيير التبويب لتحديث البيانات حسب الحاجة
  void _onTabChanged() {
    if (!mounted) return;

    // تحديث البيانات حسب التبويب المحدد
    switch (_tabController.index) {
      case 0: // تبويب المنهج
        // تحديث قائمة الفصول إذا لزم الأمر
        break;
      case 1: // تبويب التقدم
        // إعادة حساب الإحصائيات
        _updateStatistics();
        // إعادة تشغيل الرسم المتحرك
        _progressAnimationController.reset();
        _progressAnimationController.forward();
        break;
      case 2: // تبويب النصائح
        // تحديث النصائح بناءً على التقدم الحالي
        break;
      case 3: // تبويب الملاحظات
        // تحديث قائمة الملاحظات
        break;
    }
  }

  /// تحديث الإحصائيات والحسابات
  ///
  /// هذه الدالة تقوم بحساب جميع الإحصائيات المطلوبة:
  /// - نسبة الإنجاز الإجمالية
  /// - الوقت المقدر والفعلي
  /// - متوسط الصعوبة
  /// - الأيام المتبقية والوقت اليومي المطلوب
  void _updateStatistics() {
    if (_examSyllabus == null) return;

    // حساب إجمالي الفصول والمكتملة
    _totalChapters = 0;
    _completedChapters = 0;

    for (final topic in _examSyllabus!.topics) {
      _totalChapters += topic.chapters.length;
      for (final chapter in topic.chapters) {
        if (_chapterProgress[chapter.id] == true) {
          _completedChapters++;
        }
      }
    }

    // حساب نسبة الإنجاز
    _overallProgress =
        _totalChapters > 0 ? (_completedChapters / _totalChapters) * 100 : 0.0;

    // حساب الوقت الإجمالي
    _totalEstimatedHours = 0.0;
    _totalActualHours = 0.0;
    double totalDifficulty = 0.0;
    int difficultyCount = 0;

    for (final topic in _examSyllabus!.topics) {
      for (final chapter in topic.chapters) {
        // الوقت المقدر
        final estimatedMinutes =
            _chapterEstimatedTime[chapter.id] ?? chapter.estimatedMinutes;
        _totalEstimatedHours += estimatedMinutes / 60.0;

        // الوقت الفعلي
        final actualMinutes = _chapterActualTime[chapter.id] ?? 0;
        _totalActualHours += actualMinutes / 60.0;

        // متوسط الصعوبة
        final difficulty = _chapterDifficulty[chapter.id];
        if (difficulty != null) {
          totalDifficulty += difficulty;
          difficultyCount++;
        }
      }
    }

    // حساب متوسط الصعوبة (للاستخدام المستقبلي)
    final averageDifficulty =
        difficultyCount > 0 ? totalDifficulty / difficultyCount : 0.0;

    // حساب الأيام المتبقية
    if (_selectedExam != null) {
      final now = DateTime.now();
      final examDate = _selectedExam!.startDate;
      _daysRemaining = examDate.difference(now).inDays;

      // حساب الوقت اليومي المطلوب (للاستخدام المستقبلي)
      final remainingHours = _totalEstimatedHours - _totalActualHours;
      final dailyStudyHours =
          _daysRemaining > 0 ? remainingHours / _daysRemaining : 0.0;
    }

    // تحديث قيمة الرسم المتحرك
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: _overallProgress / 100.0,
    ).animate(
      CurvedAnimation(
        parent: _progressAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    setState(() {});
  }

  // ===================================================================
  // دوال الحفظ والتحديث
  // ===================================================================

  /// حفظ تقدم الطالب في قاعدة البيانات
  ///
  /// تحفظ هذه الدالة:
  /// - حالة إكمال كل فصل
  /// - الملاحظات الشخصية
  /// - تقييمات الصعوبة
  /// - الأوقات المستغرقة
  Future<void> _saveProgress() async {
    setState(() {
      _isSaving = true;
    });

    // استخدام نظام معالجة الأخطاء المحسن
    await AsyncErrorHandler.executeAdvanced<void>(
      operation: () async {
        // TODO: حفظ البيانات في قاعدة البيانات
        // هنا سيتم استدعاء الخدمات لحفظ:
        // 1. تقدم الطالب في كل فصل
        // 2. الملاحظات والتقييمات
        // 3. الأوقات المستغرقة

        // محاكاة عملية الحفظ
        await Future.delayed(const Duration(seconds: 1));
      },
      context: context,
      maxRetries: 2,
      timeout: const Duration(seconds: 15),
      loadingMessage: 'جاري حفظ تقدمك...',
      successMessage: 'تم حفظ تقدمك بنجاح',
      showSuccessMessage: true,
      onError: () {
        // يمكن إضافة منطق إضافي عند الفشل
      },
    );

    // إعادة تعيين حالة الحفظ
    if (mounted) {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// تحديث حالة إكمال فصل معين
  ///
  /// [chapterId] معرف الفصل
  /// [isCompleted] هل تم إكمال مراجعة الفصل
  void _updateChapterProgress(String chapterId, bool isCompleted) {
    setState(() {
      _chapterProgress[chapterId] = isCompleted;
    });

    // تحديث الإحصائيات فوراً
    _updateStatistics();

    // حفظ التقدم تلقائياً (يمكن تعطيله إذا كان يسبب بطء)
    _saveProgress();
  }

  // ===================================================================
  // دوال بناء التبويبات الرئيسية
  // ===================================================================

  /// بناء تبويب المنهج
  ///
  /// يعرض هذا التبويب:
  /// - قائمة المواضيع والفصول بشكل منظم
  /// - مؤشرات التقدم لكل فصل
  /// - إمكانية تحديد الفصول كمكتملة
  /// - البحث والفلترة
  Widget _buildSyllabusTab() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    if (_selectedExam == null || _examSyllabus == null) {
      return _buildExamSelectionView();
    }

    return RefreshIndicator(
      onRefresh: _loadInitialData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الامتحان
            _buildExamInfoCard(),
            const SizedBox(height: 16),

            // مؤشر التقدم العام
            _buildOverallProgressCard(),
            const SizedBox(height: 16),

            // قائمة المواضيع والفصول
            _buildTopicsList(),
          ],
        ),
      ),
    );
  }

  /// بناء تبويب التقدم
  ///
  /// يعرض إحصائيات مفصلة عن تقدم الطالب:
  /// - مؤشرات بصرية للإنجاز
  /// - إحصائيات الوقت
  /// - مقارنات ومؤشرات الأداء
  Widget _buildProgressTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // مؤشر التقدم الدائري الكبير
          _buildCircularProgressIndicator(),
          const SizedBox(height: 24),

          // إحصائيات التقدم
          _buildProgressStatistics(),
          const SizedBox(height: 24),

          // مخطط التقدم اليومي
          _buildDailyProgressChart(),
          const SizedBox(height: 24),

          // توصيات التحسين
          _buildImprovementSuggestions(),
        ],
      ),
    );
  }

  /// بناء تبويب النصائح
  ///
  /// يقدم نصائح وإرشادات للمراجعة الفعالة:
  /// - نصائح عامة للمراجعة
  /// - نصائح خاصة بالمادة
  /// - استراتيجيات إدارة الوقت
  /// - تقنيات التذكر والحفظ
  Widget _buildTipsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // نصائح عامة
          _buildGeneralTipsCard(),
          const SizedBox(height: 16),

          // نصائح خاصة بالمادة
          _buildSubjectSpecificTips(),
          const SizedBox(height: 16),

          // استراتيجيات إدارة الوقت
          _buildTimeManagementTips(),
          const SizedBox(height: 16),

          // تقنيات التذكر
          _buildMemoryTechniques(),
        ],
      ),
    );
  }

  /// بناء تبويب الملاحظات
  ///
  /// يعرض ويدير ملاحظات الطالب الشخصية:
  /// - ملاحظات لكل فصل
  /// - ملاحظات عامة
  /// - أسئلة ونقاط مهمة
  /// - تذكيرات شخصية
  Widget _buildNotesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // ملاحظات عامة
          _buildGeneralNotesCard(),
          const SizedBox(height: 16),

          // ملاحظات الفصول
          _buildChapterNotesSection(),
          const SizedBox(height: 16),

          // أسئلة مهمة
          _buildImportantQuestionsCard(),
          const SizedBox(height: 16),

          // تذكيرات
          _buildRemindersCard(),
        ],
      ),
    );
  }

  // ===================================================================
  // دوال بناء المكونات الفرعية
  // ===================================================================

  /// بناء عرض اختيار الامتحان
  ///
  /// يظهر عندما لا يكون هناك امتحان محدد
  Widget _buildExamSelectionView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.quiz_outlined, size: 64, color: Colors.purple[300]),
            const SizedBox(height: 16),
            Text(
              'اختر امتحاناً للاستعداد له',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.purple[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ستتمكن من مراجعة المنهج وتتبع تقدمك',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _showExamSelectionDialog(),
              icon: const Icon(Icons.search),
              label: const Text('اختيار امتحان'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة معلومات الامتحان
  Widget _buildExamInfoCard() {
    if (_selectedExam == null) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.purple[600]!, Colors.purple[800]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _selectedExam!.name,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.calendar_today, color: Colors.purple[100], size: 16),
              const SizedBox(width: 4),
              Text(
                'التاريخ: ${_formatDate(_selectedExam!.startDate)}',
                style: TextStyle(color: Colors.purple[100]),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(Icons.access_time, color: Colors.purple[100], size: 16),
              const SizedBox(width: 4),
              Text(
                'من: ${_selectedExam!.startDate.hour}:${_selectedExam!.startDate.minute.toString().padLeft(2, '0')} إلى ${_selectedExam!.endDate.hour}:${_selectedExam!.endDate.minute.toString().padLeft(2, '0')}',
                style: TextStyle(color: Colors.purple[100]),
              ),
            ],
          ),
          if (_daysRemaining > 0) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.event_note, color: Colors.purple[100], size: 16),
                const SizedBox(width: 4),
                Text(
                  'باقي $_daysRemaining يوم',
                  style: TextStyle(
                    color: Colors.purple[100],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // ===================================================================
  // دوال مساعدة وحوارات
  // ===================================================================

  /// عرض حوار اختيار الامتحان
  void _showExamSelectionDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('اختيار امتحان'),
            content: const Text('سيتم تطبيق قائمة الامتحانات المتاحة قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار البحث
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('البحث في المنهج'),
            content: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'ابحث عن موضوع أو فصل...',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) {
                // تحديث البحث (سيتم تطبيقه لاحقاً)
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('بحث'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار الفلاتر
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('فلترة وترتيب الفصول'),
            content: const Text('سيتم تطبيق خيارات الفلترة والترتيب قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار إضافة ملاحظة
  void _showAddNoteDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إضافة ملاحظة سريعة'),
            content: TextField(
              controller: _noteController,
              decoration: const InputDecoration(
                hintText: 'اكتب ملاحظتك هنا...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  // TODO: حفظ الملاحظة
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حفظ الملاحظة'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                child: const Text('حفظ'),
              ),
            ],
          ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // ===================================================================
  // دوال بناء المكونات البسيطة (مؤقتة)
  // ===================================================================

  Widget _buildOverallProgressCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            'التقدم العام: ${_overallProgress.toStringAsFixed(1)}%',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: _overallProgress / 100,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(Colors.purple[600]!),
          ),
        ],
      ),
    );
  }

  Widget _buildTopicsList() {
    return const Center(
      child: Text(
        'قائمة المواضيع\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  Widget _buildCircularProgressIndicator() {
    return const Center(
      child: Text(
        'مؤشر التقدم الدائري\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  Widget _buildProgressStatistics() {
    return const Center(
      child: Text(
        'إحصائيات التقدم\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  Widget _buildDailyProgressChart() {
    return const Center(
      child: Text(
        'مخطط التقدم اليومي\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  Widget _buildImprovementSuggestions() {
    return const Center(
      child: Text(
        'توصيات التحسين\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  Widget _buildGeneralTipsCard() {
    return const Center(
      child: Text(
        'النصائح العامة\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  Widget _buildSubjectSpecificTips() {
    return const Center(
      child: Text(
        'نصائح خاصة بالمادة\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  Widget _buildTimeManagementTips() {
    return const Center(
      child: Text(
        'نصائح إدارة الوقت\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  Widget _buildMemoryTechniques() {
    return const Center(
      child: Text(
        'تقنيات التذكر\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  Widget _buildGeneralNotesCard() {
    return const Center(
      child: Text(
        'الملاحظات العامة\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  Widget _buildChapterNotesSection() {
    return const Center(
      child: Text(
        'ملاحظات الفصول\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  Widget _buildImportantQuestionsCard() {
    return const Center(
      child: Text(
        'الأسئلة المهمة\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  Widget _buildRemindersCard() {
    return const Center(
      child: Text(
        'التذكيرات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  /// بناء شريط المعلومات السفلي
  Widget _buildBottomInfoBar() {
    if (_selectedExam == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.purple[50],
        border: Border(top: BorderSide(color: Colors.purple[200]!)),
      ),
      child: Row(
        children: [
          Icon(Icons.schedule, size: 16, color: Colors.purple[600]),
          const SizedBox(width: 4),
          Text(
            'مكتمل: $_completedChapters/$_totalChapters',
            style: TextStyle(fontSize: 12, color: Colors.purple[600]),
          ),
          const Spacer(),
          if (_daysRemaining > 0) ...[
            Icon(Icons.event, size: 16, color: Colors.purple[600]),
            const SizedBox(width: 4),
            Text(
              'باقي $_daysRemaining يوم',
              style: TextStyle(fontSize: 12, color: Colors.purple[600]),
            ),
          ],
        ],
      ),
    );
  }
}

/// تعداد فلاتر الفصول
enum ChapterFilter {
  all, // جميع الفصول
  completed, // المكتملة فقط
  pending, // غير المكتملة فقط
}

/// تعداد ترتيب الفصول
enum ChapterSort {
  order, // حسب الترتيب الأصلي
  difficulty, // حسب الصعوبة
  progress, // حسب التقدم
}
