import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/attendance_model.dart';
import 'package:school_management_system/providers/attendance_providers.dart';
import 'package:school_management_system/widgets/enhanced_error_widget.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:table_calendar/table_calendar.dart' as table_calendar;

/// شاشة الحضور والغياب المحسنة للطلاب
/// تعرض سجل الحضور بتصميم عصري مع إحصائيات وتحليلات متقدمة
class StudentAttendanceScreen extends ConsumerStatefulWidget {
  final String studentId;

  const StudentAttendanceScreen({super.key, required this.studentId});

  @override
  ConsumerState<StudentAttendanceScreen> createState() =>
      _StudentAttendanceScreenState();
}

class _StudentAttendanceScreenState
    extends ConsumerState<StudentAttendanceScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    // إعداد التبويبات (التقويم، الإحصائيات، السجل التفصيلي)
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // مراقبة سجل الحضور المحسن
    final attendanceAsyncValue = ref.watch(
      studentAttendanceProvider(widget.studentId),
    );
    // مراقبة نمط العرض الحالي
    final viewMode = ref.watch(attendanceViewModeProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('سجل الحضور والغياب'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        // إضافة أزرار التحكم في شريط التطبيق
        actions: [
          // زر البحث
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
            tooltip: 'البحث في السجل',
          ),
          // قائمة أنماط العرض
          PopupMenuButton<AttendanceViewMode>(
            icon: const Icon(Icons.view_module),
            tooltip: 'تغيير نمط العرض',
            onSelected: (mode) {
              ref.read(attendanceViewModeProvider.notifier).state = mode;
            },
            itemBuilder:
                (context) =>
                    AttendanceViewMode.values.map((mode) {
                      return PopupMenuItem(
                        value: mode,
                        child: Row(
                          children: [
                            Icon(
                              _getViewModeIcon(mode),
                              color: Theme.of(context).primaryColor,
                            ),
                            const SizedBox(width: 8),
                            Text(mode.arabicName),
                          ],
                        ),
                      );
                    }).toList(),
          ),
        ],
        // إضافة التبويبات
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.calendar_month), text: 'التقويم'),
            Tab(icon: Icon(Icons.analytics), text: 'الإحصائيات'),
            Tab(icon: Icon(Icons.list), text: 'السجل'),
          ],
        ),
      ),
      body: attendanceAsyncValue.when(
        loading: () => const LoadingIndicator(),
        error:
            (error, stackTrace) => EnhancedErrorWidget(
              error: error,
              customMessage: 'حدث خطأ في تحميل سجل الحضور',
              onRetry:
                  () => ref.invalidate(
                    studentAttendanceProvider(widget.studentId),
                  ),
              showDetails: true,
            ),
        data: (attendanceRecords) {
          if (attendanceRecords.isEmpty) {
            return _buildEmptyState(context);
          }

          return TabBarView(
            controller: _tabController,
            children: [
              // تبويب التقويم
              _buildCalendarView(context, attendanceRecords),
              // تبويب الإحصائيات
              _buildStatsView(context, attendanceRecords),
              // تبويب السجل التفصيلي
              _buildRecordsListView(context, attendanceRecords),
            ],
          );
        },
      ),
    );
  }

  /// بناء حالة الشاشة الفارغة عند عدم وجود سجل حضور
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.event_busy, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا يوجد سجل حضور مسجل بعد',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيظهر سجل الحضور هنا عند بدء تسجيله',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // إعادة تحميل البيانات
              ref.invalidate(studentAttendanceProvider(widget.studentId));
            },
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء عرض التقويم
  Widget _buildCalendarView(
    BuildContext context,
    List<AttendanceRecord> records,
  ) {
    // تحويل السجلات إلى خريطة للتقويم
    final attendanceMap = <DateTime, AttendanceRecord>{};
    for (final record in records) {
      final dateKey = DateTime.utc(
        record.date.year,
        record.date.month,
        record.date.day,
      );
      attendanceMap[dateKey] = record;
    }

    final focusedDay = ref.watch(calendarFocusedDayProvider);
    final selectedDay = ref.watch(calendarSelectedDayProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // بطاقة معلومات اليوم الحالي
          _buildTodayInfoCard(context, records),

          const SizedBox(height: 16),

          // التقويم المحسن
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: table_calendar.TableCalendar<AttendanceRecord>(
                locale: 'ar_SA',
                firstDay: DateTime.utc(2024, 1, 1),
                lastDay: DateTime.utc(2026, 12, 31),
                focusedDay: focusedDay,
                selectedDayPredicate:
                    (day) => table_calendar.isSameDay(selectedDay, day),
                onDaySelected: (newSelectedDay, newFocusedDay) {
                  ref.read(calendarSelectedDayProvider.notifier).state =
                      newSelectedDay;
                  ref.read(calendarFocusedDayProvider.notifier).state =
                      newFocusedDay;
                },
                calendarFormat: table_calendar.CalendarFormat.month,
                startingDayOfWeek: table_calendar.StartingDayOfWeek.sunday,
                calendarStyle: table_calendar.CalendarStyle(
                  todayDecoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                  ),
                  selectedDecoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.8),
                    shape: BoxShape.circle,
                  ),
                  weekendTextStyle: TextStyle(color: Colors.grey.shade600),
                  outsideDaysVisible: false,
                ),
                headerStyle: const table_calendar.HeaderStyle(
                  formatButtonVisible: false,
                  titleCentered: true,
                  titleTextStyle: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                calendarBuilders: table_calendar.CalendarBuilders<
                  AttendanceRecord
                >(
                  // بناء مؤشرات الحضور
                  markerBuilder: (context, day, records) {
                    final dateKey = DateTime.utc(day.year, day.month, day.day);
                    final record = attendanceMap[dateKey];

                    if (record != null) {
                      return Positioned(
                        bottom: 1,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: record.status.color,
                            shape: BoxShape.circle,
                          ),
                        ),
                      );
                    }
                    return null;
                  },
                  // بناء خلايا مخصصة للأيام
                  defaultBuilder: (context, day, focusedDay) {
                    final dateKey = DateTime.utc(day.year, day.month, day.day);
                    final record = attendanceMap[dateKey];

                    if (record != null) {
                      return Container(
                        margin: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: record.status.color.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: record.status.color.withOpacity(0.3),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            '${day.day}',
                            style: TextStyle(
                              color: record.status.color,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      );
                    }
                    return null;
                  },
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // معلومات اليوم المختار
          if (selectedDay != null)
            _buildSelectedDayInfo(context, selectedDay, attendanceMap),

          const SizedBox(height: 16),

          // مفتاح الألوان
          _buildColorLegend(context),
        ],
      ),
    );
  }

  /// بناء عرض الإحصائيات
  Widget _buildStatsView(BuildContext context, List<AttendanceRecord> records) {
    final stats = AttendanceStats(records: records);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة الإحصائيات العامة
          _buildGeneralStatsCard(context, stats),

          const SizedBox(height: 16),

          // بطاقة نسب الحضور
          _buildAttendanceRatesCard(context, stats),

          const SizedBox(height: 16),

          // بطاقة تحليل الأداء
          _buildPerformanceAnalysisCard(context, stats),

          const SizedBox(height: 16),

          // بطاقة الاتجاهات
          _buildTrendsCard(context, stats),

          const SizedBox(height: 16),

          // رسم بياني للحضور الشهري
          _buildMonthlyChart(context, records),
        ],
      ),
    );
  }

  /// بناء عرض السجل التفصيلي
  Widget _buildRecordsListView(
    BuildContext context,
    List<AttendanceRecord> records,
  ) {
    final filteredRecords = ref.watch(
      filteredAttendanceProvider(widget.studentId),
    );

    return Column(
      children: [
        // شريط البحث
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            decoration: InputDecoration(
              hintText: 'البحث في السجل...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.grey.shade50,
            ),
            onChanged: (value) {
              ref.read(attendanceSearchProvider.notifier).state = value;
            },
          ),
        ),

        // قائمة السجلات
        Expanded(
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: filteredRecords.length,
            separatorBuilder: (context, index) => const SizedBox(height: 8),
            itemBuilder: (context, index) {
              final record = filteredRecords[index];
              return _buildRecordCard(context, record);
            },
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة معلومات اليوم الحالي
  Widget _buildTodayInfoCard(
    BuildContext context,
    List<AttendanceRecord> records,
  ) {
    final todayRecord = ref.watch(todayAttendanceProvider(widget.studentId));
    final today = DateTime.now();
    final isWeekend = today.weekday == 5 || today.weekday == 6; // الجمعة والسبت

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors:
                isWeekend
                    ? [Colors.orange.shade100, Colors.orange.shade50]
                    : todayRecord != null
                    ? [
                      todayRecord.status.color.withOpacity(0.2),
                      todayRecord.status.color.withOpacity(0.1),
                    ]
                    : [Colors.blue.shade100, Colors.blue.shade50],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isWeekend
                      ? Icons.weekend
                      : todayRecord?.status.icon ?? Icons.today,
                  color:
                      isWeekend
                          ? Colors.orange
                          : todayRecord?.status.color ?? Colors.blue,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        DateFormat.yMMMMEEEEd('ar_SA').format(today),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        isWeekend
                            ? 'عطلة نهاية الأسبوع'
                            : todayRecord?.status.arabicName ??
                                'لم يتم التسجيل بعد',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                if (todayRecord != null && todayRecord.checkInTime != null)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: todayRecord.status.color,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      DateFormat.Hm('ar_SA').format(todayRecord.checkInTime!),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات اليوم المختار
  Widget _buildSelectedDayInfo(
    BuildContext context,
    DateTime selectedDay,
    Map<DateTime, AttendanceRecord> attendanceMap,
  ) {
    final dateKey = DateTime.utc(
      selectedDay.year,
      selectedDay.month,
      selectedDay.day,
    );
    final record = attendanceMap[dateKey];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  record?.status.icon ?? Icons.help_outline,
                  color: record?.status.color ?? Colors.grey,
                ),
                const SizedBox(width: 8),
                Text(
                  DateFormat.yMMMMEEEEd('ar_SA').format(selectedDay),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            if (record != null) ...[
              _buildInfoRow(
                'الحالة',
                record.status.arabicName,
                record.status.color,
              ),
              if (record.checkInTime != null)
                _buildInfoRow(
                  'وقت الوصول',
                  DateFormat.Hm('ar_SA').format(record.checkInTime!),
                ),
              if (record.checkOutTime != null)
                _buildInfoRow(
                  'وقت المغادرة',
                  DateFormat.Hm('ar_SA').format(record.checkOutTime!),
                ),
              if (record.notes != null && record.notes!.isNotEmpty)
                _buildInfoRow('ملاحظات', record.notes!),
              if (record.isLateArrival)
                _buildInfoRow(
                  'التأخير',
                  '${record.lateMinutes} دقيقة',
                  Colors.orange,
                ),
            ] else ...[
              Text(
                'لا يوجد سجل حضور لهذا اليوم',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء مفتاح الألوان
  Widget _buildColorLegend(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'مفتاح الألوان',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 16,
              runSpacing: 8,
              children:
                  AttendanceStatus.values
                      .where((status) => status != AttendanceStatus.unknown)
                      .map(
                        (status) => Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: status.color,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              status.arabicName,
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      )
                      .toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الإحصائيات العامة
  Widget _buildGeneralStatsCard(BuildContext context, AttendanceStats stats) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'الإحصائيات العامة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي الأيام',
                    '${stats.totalDays}',
                    Icons.calendar_today,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'أيام الحضور',
                    '${stats.presentDays}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'أيام الغياب',
                    '${stats.absentDays}',
                    Icons.cancel,
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'أيام التأخير',
                    '${stats.lateDays}',
                    Icons.access_time,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة نسب الحضور
  Widget _buildAttendanceRatesCard(
    BuildContext context,
    AttendanceStats stats,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.pie_chart, color: Colors.green),
                SizedBox(width: 8),
                Text(
                  'نسب الحضور',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // نسبة الحضور الرئيسية
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    stats.attendanceGradeColor.withOpacity(0.2),
                    stats.attendanceGradeColor.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: stats.attendanceGradeColor.withOpacity(0.3),
                ),
              ),
              child: Column(
                children: [
                  Text(
                    '${stats.attendancePercentage.toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: stats.attendanceGradeColor,
                    ),
                  ),
                  Text(
                    'نسبة الحضور',
                    style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: stats.attendanceGradeColor,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      stats.attendanceGrade,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // شريط التقدم
            LinearProgressIndicator(
              value: stats.attendanceRate,
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation<Color>(
                stats.attendanceGradeColor,
              ),
              minHeight: 8,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة تحليل الأداء
  Widget _buildPerformanceAnalysisCard(
    BuildContext context,
    AttendanceStats stats,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.trending_up, color: Colors.purple),
                SizedBox(width: 8),
                Text(
                  'تحليل الأداء',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildAnalysisRow(
              'متوسط وقت الوصول',
              _formatAverageTime(stats.averageArrivalTime),
            ),
            _buildAnalysisRow('عدد مرات التأخير', '${stats.lateArrivals} مرة'),
            _buildAnalysisRow(
              'متوسط دقائق التأخير',
              '${stats.averageLateMinutes.toStringAsFixed(1)} دقيقة',
            ),
            _buildAnalysisRow('أكثر الأيام غياباً', stats.mostAbsentWeekday),
            _buildAnalysisRow('اتجاه الحضور', stats.attendanceTrend),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الاتجاهات
  Widget _buildTrendsCard(BuildContext context, AttendanceStats stats) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.show_chart, color: Colors.indigo),
                SizedBox(width: 8),
                Text(
                  'الاتجاهات والتوصيات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.lightbulb, color: Colors.blue.shade700),
                      const SizedBox(width: 8),
                      Text(
                        'توصيات لتحسين الحضور:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  ..._getRecommendations(stats).map(
                    (recommendation) => Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '• ',
                            style: TextStyle(color: Colors.blue.shade700),
                          ),
                          Expanded(
                            child: Text(
                              recommendation,
                              style: const TextStyle(fontSize: 13, height: 1.4),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء الرسم البياني الشهري
  Widget _buildMonthlyChart(
    BuildContext context,
    List<AttendanceRecord> records,
  ) {
    // تجميع البيانات حسب الشهر
    final monthlyData = <String, Map<AttendanceStatus, int>>{};

    for (final record in records) {
      final monthKey = DateFormat.yMMMM('ar_SA').format(record.date);
      monthlyData[monthKey] ??= {};
      monthlyData[monthKey]![record.status] =
          (monthlyData[monthKey]![record.status] ?? 0) + 1;
    }

    if (monthlyData.isEmpty) return const SizedBox.shrink();

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.bar_chart, color: Colors.teal),
                SizedBox(width: 8),
                Text(
                  'التوزيع الشهري',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // عرض بسيط للبيانات الشهرية
            ...monthlyData.entries.take(6).map((entry) {
              final monthName = entry.key;
              final data = entry.value;
              final total = data.values.fold(0, (sum, count) => sum + count);
              final presentCount = data[AttendanceStatus.present] ?? 0;
              final presentRate = total > 0 ? presentCount / total : 0.0;

              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          monthName,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        Text(
                          '${(presentRate * 100).toStringAsFixed(1)}%',
                          style: TextStyle(
                            color:
                                presentRate >= 0.9
                                    ? Colors.green
                                    : presentRate >= 0.8
                                    ? Colors.orange
                                    : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: presentRate,
                      backgroundColor: Colors.grey.shade200,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        presentRate >= 0.9
                            ? Colors.green
                            : presentRate >= 0.8
                            ? Colors.orange
                            : Colors.red,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة سجل واحد
  Widget _buildRecordCard(BuildContext context, AttendanceRecord record) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              record.status.color.withOpacity(0.1),
              record.status.color.withOpacity(0.05),
            ],
          ),
          border: Border.all(
            color: record.status.color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // أيقونة الحالة
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: record.status.color,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(record.status.icon, color: Colors.white, size: 20),
            ),

            const SizedBox(width: 12),

            // معلومات السجل
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    DateFormat.yMMMMEEEEd('ar_SA').format(record.date),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    record.status.arabicName,
                    style: TextStyle(
                      color: record.status.color,
                      fontWeight: FontWeight.w600,
                      fontSize: 13,
                    ),
                  ),
                  if (record.checkInTime != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'الوصول: ${DateFormat.Hm('ar_SA').format(record.checkInTime!)}',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                        if (record.isLateArrival) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.orange.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              'متأخر ${record.lateMinutes} د',
                              style: const TextStyle(
                                color: Colors.orange,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                  if (record.notes != null && record.notes!.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      record.notes!,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),

            // معلومات إضافية
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if (record.stayDuration != null)
                  Text(
                    '${record.stayDuration!.inHours}س ${record.stayDuration!.inMinutes % 60}د',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                if (record.checkOutTime != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    DateFormat.Hm('ar_SA').format(record.checkOutTime!),
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 11),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر إحصائية واحد
  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء صف معلومات
  Widget _buildInfoRow(String label, String value, [Color? valueColor]) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Text('$label: ', style: const TextStyle(fontWeight: FontWeight.w600)),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: valueColor ?? Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء صف تحليل
  Widget _buildAnalysisRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(value, style: TextStyle(color: Colors.grey.shade700)),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة نمط العرض
  IconData _getViewModeIcon(AttendanceViewMode mode) {
    switch (mode) {
      case AttendanceViewMode.monthly:
        return Icons.calendar_view_month;
      case AttendanceViewMode.weekly:
        return Icons.calendar_view_week;
      case AttendanceViewMode.statistics:
        return Icons.analytics;
      case AttendanceViewMode.calendar:
        return Icons.calendar_today;
    }
  }

  /// تنسيق متوسط وقت الوصول
  String _formatAverageTime(double? averageMinutes) {
    if (averageMinutes == null) return 'غير متوفر';

    final hours = (averageMinutes / 60).floor();
    final minutes = (averageMinutes % 60).round();

    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';
  }

  /// الحصول على التوصيات بناءً على الإحصائيات
  List<String> _getRecommendations(AttendanceStats stats) {
    final recommendations = <String>[];

    if (stats.attendancePercentage < 85) {
      recommendations.add('حاول تحسين نسبة الحضور للوصول إلى 85% على الأقل');
    }

    if (stats.lateArrivals > 5) {
      recommendations.add(
        'قلل من عدد مرات التأخير بالنوم مبكراً والاستيقاظ في وقت كافٍ',
      );
    }

    if (stats.averageLateMinutes > 15) {
      recommendations.add(
        'حاول الوصول في الوقت المحدد لتجنب فقدان جزء من الحصص',
      );
    }

    if (stats.attendanceTrend.contains('تراجع')) {
      recommendations.add(
        'لاحظنا تراجعاً في الحضور مؤخراً، حاول العودة للانتظام',
      );
    }

    if (recommendations.isEmpty) {
      recommendations.add('أداؤك في الحضور ممتاز، استمر على هذا المنوال!');
    }

    return recommendations;
  }

  /// عرض حوار البحث
  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.search, color: Colors.blue),
                SizedBox(width: 8),
                Text('البحث في السجل'),
              ],
            ),
            content: TextField(
              decoration: const InputDecoration(
                hintText: 'ابحث عن حالة أو تاريخ...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) {
                ref.read(attendanceSearchProvider.notifier).state = value;
              },
            ),
            actions: [
              TextButton(
                onPressed: () {
                  ref.read(attendanceSearchProvider.notifier).state = '';
                  Navigator.of(context).pop();
                },
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('بحث'),
              ),
            ],
          ),
    );
  }
}
