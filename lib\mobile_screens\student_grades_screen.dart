import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/providers/student_providers.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/error_message.dart';

/// شاشة الدرجات المحسنة للطلاب
///
/// تعرض الدرجات التفصيلية مع الإحصائيات والمقارنات
/// باستخدام البيانات الحقيقية من Firebase
class StudentGradesScreen extends ConsumerWidget {
  final String studentId;

  const StudentGradesScreen({Key? key, required this.studentId})
    : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // استخدام البيانات الحقيقية المفصلة بدلاً من البيانات البسيطة
    final detailedGradesAsyncValue = ref.watch(
      studentDetailedGradesProvider(studentId),
    );
    final performanceStatsAsyncValue = ref.watch(
      studentPerformanceStatsProvider(studentId),
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('الدرجات والتقييم'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () {
              // يمكن إضافة شاشة إحصائيات مفصلة هنا
            },
          ),
        ],
      ),
      body: detailedGradesAsyncValue.when(
        loading: () => const LoadingIndicator(),
        error:
            (err, stack) =>
                ErrorMessage(message: 'حدث خطأ في تحميل الدرجات: $err'),
        data: (detailedGrades) {
          if (detailedGrades.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.grade, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'لا توجد درجات مسجلة حتى الآن',
                    style: TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'ستظهر درجاتك هنا بعد نشرها من قبل المعلمين',
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // بطاقة الإحصائيات السريعة
              _buildQuickStatsCard(context, performanceStatsAsyncValue),

              // قائمة الدرجات المفصلة
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16.0),
                  itemCount: detailedGrades.length,
                  itemBuilder: (context, index) {
                    final gradeData = detailedGrades[index];
                    return _buildDetailedGradeCard(gradeData);
                  },
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// بناء بطاقة الإحصائيات السريعة
  Widget _buildQuickStatsCard(
    BuildContext context,
    AsyncValue<Map<String, dynamic>> statsAsyncValue,
  ) {
    return Container(
      margin: const EdgeInsets.all(16.0),
      child: Card(
        elevation: 4,
        child: statsAsyncValue.when(
          loading:
              () => const Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(child: CircularProgressIndicator()),
              ),
          error:
              (err, stack) => const Padding(
                padding: EdgeInsets.all(16.0),
                child: Text('خطأ في تحميل الإحصائيات'),
              ),
          data: (stats) {
            final overallAverage =
                (stats['overallAverage'] as num?)?.toDouble() ?? 0.0;
            final totalExams = stats['totalExams'] as int? ?? 0;
            final passedExams = stats['passedExams'] as int? ?? 0;
            final classRank = stats['classRank'] as int? ?? 0;
            final totalStudents = stats['totalStudentsInClass'] as int? ?? 0;

            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Text(
                    'ملخص الأداء الأكاديمي',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem(
                        'المعدل العام',
                        '${overallAverage.toStringAsFixed(1)}%',
                        Colors.blue,
                      ),
                      _buildStatItem('الامتحانات', '$totalExams', Colors.green),
                      _buildStatItem('النجاح', '$passedExams', Colors.orange),
                      if (totalStudents > 0)
                        _buildStatItem(
                          'الترتيب',
                          '$classRank/$totalStudents',
                          Colors.purple,
                        ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  /// بناء عنصر إحصائية واحد
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
      ],
    );
  }

  /// بناء بطاقة درجة مفصلة
  Widget _buildDetailedGradeCard(Map<String, dynamic> gradeData) {
    final examName = gradeData['examName'] as String? ?? 'امتحان غير محدد';
    final subjectName = gradeData['subjectName'] as String? ?? 'مادة غير محددة';
    final grade = (gradeData['grade'] as num?)?.toDouble() ?? 0.0;
    final maxGrade = (gradeData['maxGrade'] as num?)?.toDouble() ?? 100.0;
    final percentage = (grade / maxGrade) * 100;
    final examDate = gradeData['examDate'] as Timestamp?;
    final teacherName = gradeData['teacherName'] as String? ?? 'معلم غير محدد';
    final classAverage = (gradeData['classAverage'] as num?)?.toDouble() ?? 0.0;
    final rankInClass = gradeData['rankInClass'] as int? ?? 0;
    final totalStudents = gradeData['totalStudents'] as int? ?? 0;

    final String formattedDate;
    if (examDate != null) {
      final date = examDate.toDate();
      formattedDate = "${date.year}/${date.month}/${date.day}";
    } else {
      formattedDate = 'تاريخ غير محدد';
    }

    // تحديد لون الدرجة حسب الأداء
    Color gradeColor;
    if (percentage >= 90) {
      gradeColor = Colors.green;
    } else if (percentage >= 80) {
      gradeColor = Colors.blue;
    } else if (percentage >= 70) {
      gradeColor = Colors.orange;
    } else if (percentage >= 60) {
      gradeColor = Colors.amber;
    } else {
      gradeColor = Colors.red;
    }

    return Card(
      elevation: 4.0,
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة - اسم الامتحان والمادة
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        examName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        subjectName,
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ],
                  ),
                ),
                // الدرجة مع اللون المناسب
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: gradeColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: gradeColor),
                  ),
                  child: Text(
                    '${grade.toStringAsFixed(1)}/${maxGrade.toStringAsFixed(0)}',
                    style: TextStyle(
                      color: gradeColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // النسبة المئوية
            Row(
              children: [
                Text(
                  'النسبة المئوية: ',
                  style: TextStyle(color: Colors.grey[600]),
                ),
                Text(
                  '${percentage.toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: gradeColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // معلومات إضافية
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'التاريخ: $formattedDate',
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                      Text(
                        'المعلم: $teacherName',
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                    ],
                  ),
                ),
                if (classAverage > 0) ...[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'متوسط الصف: ${classAverage.toStringAsFixed(1)}',
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                      if (rankInClass > 0 && totalStudents > 0)
                        Text(
                          'الترتيب: $rankInClass من $totalStudents',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                    ],
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
}
