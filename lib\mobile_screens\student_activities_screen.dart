import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/activity_model.dart';
import 'package:school_management_system/providers/content_providers.dart';
import 'package:school_management_system/widgets/enhanced_error_widget.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:intl/intl.dart';

/// شاشة عرض الأنشطة المدرسية للطلاب.
class StudentActivitiesScreen extends ConsumerWidget {
  const StudentActivitiesScreen({super.key});

  @override
  Widget build(BuildContext, WidgetRef ref) {
    // مشاهدة الـ provider لجلب بيانات الأنشطة
    final activitiesAsyncValue = ref.watch(activitiesStreamProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('الأنشطة المدرسية')),
      body: activitiesAsyncValue.when(
        // في حالة تحميل البيانات
        loading: () => const LoadingIndicator(),
        // في حالة حدوث خطأ - استخدام معالجة أخطاء محسنة
        error:
            (err, stack) => EnhancedErrorWidget(
              error: err,
              customMessage: 'حدث خطأ في تحميل الأنشطة المدرسية',
              onRetry: () => ref.invalidate(activitiesStreamProvider),
              showDetails: true,
            ),
        // في حالة نجاح جلب البيانات
        data: (snapshot) {
          final activities = snapshot.docs;
          if (activities.isEmpty) {
            return const Center(child: Text('لا توجد أنشطة متاحة حالياً.'));
          }
          // عرض الأنشطة في قائمة
          return ListView.builder(
            itemCount: activities.length,
            itemBuilder: (context, index) {
              final doc = activities[index];
              final activity = ActivityModel.fromMap(
                doc.data() as Map<String, dynamic>,
                doc.id,
              );
              return Card(
                margin: const EdgeInsets.all(8.0),
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        activity.title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        activity.description,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          const Icon(
                            Icons.date_range,
                            size: 16,
                            color: Colors.grey,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'التاريخ: ${DateFormat('yyyy-MM-dd').format(activity.date)}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
