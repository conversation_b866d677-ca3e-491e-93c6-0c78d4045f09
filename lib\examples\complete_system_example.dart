import 'package:flutter/material.dart';
import 'package:school_management_system/shared/app_theme.dart';
import 'package:school_management_system/widgets/unified_components.dart';
import 'package:school_management_system/widgets/unified_layouts.dart';

/// مثال شامل يوضح استخدام النظام المحسن بالكامل
///
/// يعرض هذا الملف:
/// 1. جميع المكونات الموحدة الجديدة
/// 2. التخطيطات المختلفة للشاشات
/// 3. نظام الألوان التلقائي
/// 4. أفضل الممارسات في الاستخدام

/// تطبيق مثال شامل
class CompleteSystemExampleApp extends StatelessWidget {
  const CompleteSystemExampleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'مثال النظام المحسن',
      theme: AppTheme.lightTheme,
      home: const ExampleHomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// الشاشة الرئيسية للمثال
class ExampleHomeScreen extends StatelessWidget {
  const ExampleHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return UnifiedScreenLayout(
      title: 'النظام المحسن - مثال شامل',
      userType: UserType.general,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.screenPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // قسم أنواع المستخدمين
            SectionHeader(
              title: 'أنواع المستخدمين',
              description: 'اختر نوع المستخدم لرؤية التصميم المخصص',
            ),
            const SizedBox(height: AppSpacing.md),

            _buildUserTypeCards(context),
            const SizedBox(height: AppSpacing.sectionSpacing),

            // قسم المكونات
            SectionHeader(
              title: 'المكونات الموحدة',
              description: 'عرض للمكونات الجديدة القابلة لإعادة الاستخدام',
            ),
            const SizedBox(height: AppSpacing.md),

            _buildComponentsDemo(),
            const SizedBox(height: AppSpacing.sectionSpacing),

            // قسم التخطيطات
            SectionHeader(
              title: 'التخطيطات المتاحة',
              description: 'أنواع التخطيطات المختلفة للشاشات',
            ),
            const SizedBox(height: AppSpacing.md),

            _buildLayoutsDemo(context),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقات أنواع المستخدمين
  Widget _buildUserTypeCards(BuildContext context) {
    final userTypes = [
      {
        'type': UserType.student,
        'title': 'الطلاب',
        'description': 'واجهة مخصصة للطلاب',
        'icon': Icons.school,
      },
      {
        'type': UserType.parent,
        'title': 'أولياء الأمور',
        'description': 'واجهة مخصصة لأولياء الأمور',
        'icon': Icons.family_restroom,
      },
      {
        'type': UserType.teacher,
        'title': 'المعلمين',
        'description': 'واجهة مخصصة للمعلمين',
        'icon': Icons.person_outline,
      },
      {
        'type': UserType.admin,
        'title': 'الإدارة',
        'description': 'واجهة مخصصة للإدارة',
        'icon': Icons.admin_panel_settings,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppSpacing.md,
        mainAxisSpacing: AppSpacing.md,
        childAspectRatio: 1.2,
      ),
      itemCount: userTypes.length,
      itemBuilder: (context, index) {
        final userType = userTypes[index];
        return StatCard(
          title: userType['title'] as String,
          value: '',
          icon: userType['icon'] as IconData,
          color: _getUserColor(userType['type'] as UserType),
          subtitle: userType['description'] as String,
          onTap:
              () => _showUserTypeExample(
                context,
                userType['type'] as UserType,
                userType['title'] as String,
              ),
        );
      },
    );
  }

  /// بناء عرض المكونات
  Widget _buildComponentsDemo() {
    return Column(
      children: [
        // بطاقات إحصائية
        Row(
          children: [
            Expanded(
              child: StatCard(
                title: 'المكونات',
                value: '15+',
                icon: Icons.widgets,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: StatCard(
                title: 'التخطيطات',
                value: '4',
                icon: Icons.dashboard,
                color: AppColors.secondary,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppSpacing.md),

        // عناصر قائمة
        UnifiedListTile(
          title: 'شريط التطبيق الموحد',
          subtitle: 'UnifiedAppBar - ألوان تلقائية حسب المستخدم',
          icon: Icons.web_asset,
          statusIcon: Icons.check_circle,
          statusColor: AppColors.success,
          onTap: () {},
        ),
        UnifiedListTile(
          title: 'بطاقة الإحصائيات',
          subtitle: 'StatCard - عرض الأرقام والإحصائيات',
          icon: Icons.analytics,
          statusIcon: Icons.check_circle,
          statusColor: AppColors.success,
          onTap: () {},
        ),
        UnifiedListTile(
          title: 'عنصر القائمة الموحد',
          subtitle: 'UnifiedListTile - عناصر قوائم متناسقة',
          icon: Icons.list,
          statusIcon: Icons.check_circle,
          statusColor: AppColors.success,
          onTap: () {},
        ),
      ],
    );
  }

  /// بناء عرض التخطيطات
  Widget _buildLayoutsDemo(BuildContext context) {
    final layouts = [
      {
        'title': 'تخطيط الشاشة الأساسي',
        'description': 'UnifiedScreenLayout',
        'icon': Icons.phone_android,
        'onTap': () => _showLayoutExample(context, 'screen'),
      },
      {
        'title': 'تخطيط القائمة',
        'description': 'UnifiedListLayout',
        'icon': Icons.list_alt,
        'onTap': () => _showLayoutExample(context, 'list'),
      },
      {
        'title': 'تخطيط التفاصيل',
        'description': 'UnifiedDetailLayout',
        'icon': Icons.info_outline,
        'onTap': () => _showLayoutExample(context, 'detail'),
      },
      {
        'title': 'تخطيط النموذج',
        'description': 'UnifiedFormLayout',
        'icon': Icons.edit_note,
        'onTap': () => _showLayoutExample(context, 'form'),
      },
    ];

    return Column(
      children:
          layouts
              .map(
                (layout) => UnifiedListTile(
                  title: layout['title'] as String,
                  subtitle: layout['description'] as String,
                  icon: layout['icon'] as IconData,
                  onTap: layout['onTap'] as VoidCallback,
                ),
              )
              .toList(),
    );
  }

  /// الحصول على لون المستخدم
  Color _getUserColor(UserType type) {
    switch (type) {
      case UserType.student:
        return AppColors.studentColor;
      case UserType.parent:
        return AppColors.parentColor;
      case UserType.teacher:
        return AppColors.teacherColor;
      case UserType.admin:
        return AppColors.adminColor;
      case UserType.general:
        return AppColors.primary;
    }
  }

  /// عرض مثال نوع المستخدم
  void _showUserTypeExample(
    BuildContext context,
    UserType userType,
    String title,
  ) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                UserTypeExampleScreen(userType: userType, title: title),
      ),
    );
  }

  /// عرض مثال التخطيط
  void _showLayoutExample(BuildContext context, String layoutType) {
    Widget screen;

    switch (layoutType) {
      case 'list':
        screen = const ExampleListScreen();
        break;
      case 'detail':
        screen = const ExampleDetailScreen();
        break;
      case 'form':
        screen = const ExampleFormScreen();
        break;
      default:
        screen = const ExampleScreenLayout();
    }

    Navigator.push(context, MaterialPageRoute(builder: (context) => screen));
  }
}

/// مثال شاشة نوع المستخدم
class UserTypeExampleScreen extends StatelessWidget {
  final UserType userType;
  final String title;

  const UserTypeExampleScreen({
    super.key,
    required this.userType,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return UnifiedScreenLayout(
      title: 'مثال $title',
      userType: userType,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(AppSpacing.xl),
              decoration: BoxDecoration(
                color: _getUserColor(userType).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getUserIcon(userType),
                size: 64,
                color: _getUserColor(userType),
              ),
            ),
            const SizedBox(height: AppSpacing.lg),
            Text(
              'واجهة $title',
              style: TextStyle(
                fontSize: AppTextSizes.displayMedium,
                fontWeight: FontWeight.bold,
                color: _getUserColor(userType),
              ),
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              'تم تطبيق اللون المخصص تلقائياً',
              style: const TextStyle(
                fontSize: AppTextSizes.bodyLarge,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getUserColor(UserType type) {
    switch (type) {
      case UserType.student:
        return AppColors.studentColor;
      case UserType.parent:
        return AppColors.parentColor;
      case UserType.teacher:
        return AppColors.teacherColor;
      case UserType.admin:
        return AppColors.adminColor;
      case UserType.general:
        return AppColors.primary;
    }
  }

  IconData _getUserIcon(UserType type) {
    switch (type) {
      case UserType.student:
        return Icons.school;
      case UserType.parent:
        return Icons.family_restroom;
      case UserType.teacher:
        return Icons.person_outline;
      case UserType.admin:
        return Icons.admin_panel_settings;
      case UserType.general:
        return Icons.person;
    }
  }
}

/// مثال تخطيط الشاشة الأساسي
class ExampleScreenLayout extends StatelessWidget {
  const ExampleScreenLayout({super.key});

  @override
  Widget build(BuildContext context) {
    return UnifiedScreenLayout(
      title: 'مثال التخطيط الأساسي',
      userType: UserType.general,
      enableRefresh: true,
      onRefresh: () async {
        await Future.delayed(const Duration(seconds: 2));
      },
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.phone_android, size: 64, color: AppColors.primary),
            SizedBox(height: AppSpacing.lg),
            Text(
              'UnifiedScreenLayout',
              style: TextStyle(
                fontSize: AppTextSizes.headlineLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppSpacing.sm),
            Text(
              'تخطيط أساسي مع إمكانية السحب للتحديث',
              style: TextStyle(
                fontSize: AppTextSizes.bodyLarge,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// مثال تخطيط القائمة
class ExampleListScreen extends StatelessWidget {
  const ExampleListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final items = List.generate(
      10,
      (index) => UnifiedListTile(
        title: 'عنصر ${index + 1}',
        subtitle: 'وصف العنصر رقم ${index + 1}',
        icon: Icons.star_outline,
        onTap: () {},
      ),
    );

    return UnifiedListLayout(
      title: 'مثال تخطيط القائمة',
      userType: UserType.admin,
      items: items,
      enableSearch: true,
      onSearch: (query) {
        // تنفيذ البحث
      },
      onAdd: () {
        // إضافة عنصر جديد
      },
      emptyState: const EmptyState(
        icon: Icons.inbox_outlined,
        title: 'لا توجد عناصر',
        description: 'لم يتم العثور على أي عناصر في القائمة',
      ),
    );
  }
}

/// مثال تخطيط التفاصيل
class ExampleDetailScreen extends StatelessWidget {
  const ExampleDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return UnifiedDetailLayout(
      title: 'مثال تخطيط التفاصيل',
      userType: UserType.teacher,
      canEdit: true,
      onEdit: () {
        // تعديل العنصر
      },
      canDelete: true,
      onDelete: () {
        // حذف العنصر
      },
      sections: [
        const SectionHeader(title: 'المعلومات الأساسية', showDivider: false),
        const Text(
          'هذا مثال على تخطيط التفاصيل الذي يعرض معلومات عنصر واحد مع إمكانيات التعديل والحذف.',
          style: TextStyle(fontSize: AppTextSizes.bodyLarge, height: 1.5),
        ),
      ],
    );
  }
}

/// مثال تخطيط النموذج
class ExampleFormScreen extends StatelessWidget {
  const ExampleFormScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return UnifiedFormLayout(
      title: 'مثال تخطيط النموذج',
      userType: UserType.student,
      fields: [
        const TextField(
          decoration: InputDecoration(
            labelText: 'الاسم',
            hintText: 'أدخل الاسم',
          ),
        ),
        const TextField(
          decoration: InputDecoration(
            labelText: 'البريد الإلكتروني',
            hintText: 'أدخل البريد الإلكتروني',
          ),
        ),
        DropdownButtonFormField<String>(
          decoration: const InputDecoration(labelText: 'الفئة'),
          items:
              ['طالب', 'معلم', 'إدارة']
                  .map(
                    (item) => DropdownMenuItem(value: item, child: Text(item)),
                  )
                  .toList(),
          onChanged: (value) {},
        ),
      ],
      onSave: () {
        // حفظ البيانات
      },
    );
  }
}
