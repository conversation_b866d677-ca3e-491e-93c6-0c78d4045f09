import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/providers/communication_providers.dart';
import 'package:school_management_system/widgets/enhanced_error_widget.dart';

/// شاشة تتيح لولي الأمر التواصل مع الإدارة، معاد هيكلتها باستخدام Riverpod.
class GuardianCommunicationScreen extends ConsumerWidget {
  final String guardianId;
  const GuardianCommunicationScreen({Key? key, required this.guardianId})
    : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final messageController = TextEditingController();

    // الاستماع لحالة الإرسال لعرض رسائل محسنة
    ref.listen<AsyncValue<void>>(communicationControllerProvider, (_, state) {
      if (state is AsyncError) {
        // استخدام نظام معالجة الأخطاء المحسن
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.white),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text('حدث خطأ في إرسال الرسالة: ${state.error}'),
                  ),
                ],
              ),
              backgroundColor: Colors.red[600],
              behavior: SnackBarBehavior.floating,
              action: SnackBarAction(
                label: 'إعادة المحاولة',
                textColor: Colors.white,
                onPressed: () {
                  // يمكن إضافة منطق إعادة المحاولة هنا
                },
              ),
            ),
          );
        }
      }
      if (state is AsyncData) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 8),
                  Expanded(child: Text('تم إرسال رسالتك بنجاح!')),
                ],
              ),
              backgroundColor: Colors.green[600],
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('التواصل مع الإدارة'),
        backgroundColor: Colors.indigo,
      ),
      body: Column(
        children: [
          // قسم عرض الرسائل السابقة
          Expanded(
            child: ref
                .watch(communicationStreamProvider(guardianId))
                .when(
                  loading:
                      () => const Center(child: CircularProgressIndicator()),
                  error: (err, stack) => Center(child: Text('خطأ: $err')),
                  data: (messages) {
                    if (messages.isEmpty) {
                      return const Center(child: Text('لا توجد رسائل سابقة.'));
                    }
                    return ListView.builder(
                      reverse: true, // لعرض الأحدث في الأسفل
                      itemCount: messages.length,
                      itemBuilder: (context, index) {
                        final message = messages[index];
                        return ListTile(
                          title: Text(message.message),
                          subtitle: Text(
                            message.reply.isNotEmpty
                                ? 'الرد: ${message.reply}'
                                : 'لم يتم الرد بعد',
                          ),
                          isThreeLine: true,
                        );
                      },
                    );
                  },
                ),
          ),
          // قسم إدخال الرسالة الجديدة
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: messageController,
                    decoration: const InputDecoration(
                      hintText: 'اكتب استفسارك هنا...',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                    minLines: 1,
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.send, color: Colors.indigo),
                  onPressed: () {
                    ref
                        .read(communicationControllerProvider.notifier)
                        .sendMessage(guardianId, messageController.text);
                    messageController.clear();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
