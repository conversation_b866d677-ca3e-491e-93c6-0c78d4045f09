/// ملف الثوابت الخاصة بالبيانات اليمنية
/// يحتوي على قوائم المحافظات وفصائل الدم والبيانات الأخرى المطلوبة للمدارس اليمنية

/// قائمة المحافظات اليمنية مرتبة أبجدياً
/// تستخدم في قوائم الاختيار لحقل المحافظة
class YemenDataConstants {
  /// قائمة المحافظات اليمنية الـ 22
  /// مرتبة أبجدياً لسهولة البحث والاختيار
  static const List<String> yemenGovernorates = [
    'أبين',
    'البيضاء',
    'الحديدة',
    'الجوف',
    'المحويت',
    'المهرة',
    'أمانة العاصمة',
    'الضالع',
    'حضرموت',
    'حجة',
    'إب',
    'لحج',
    'مأرب',
    'ريمة',
    'صعدة',
    'صنعاء',
    'شبوة',
    'سقطرى',
    'تعز',
    'عدن',
    'عمران',
  ];

  /// فصائل الدم المعروفة طبياً
  /// تستخدم في حقل فصيلة الدم للطالب
  static const List<String> bloodTypes = [
    'A+',
    'A-',
    'B+',
    'B-',
    'AB+',
    'AB-',
    'O+',
    'O-',
    'غير معروف',
  ];

  /// قائمة الجنسيات الشائعة في اليمن
  /// اليمني هو الافتراضي، مع إضافة جنسيات أخرى للطلاب الأجانب
  static const List<String> nationalities = [
    'يمني', // اليمنية (الافتراضي)
    'سعودي', // السعودية
    'عماني', // عُمانية
    'إماراتي', // الإمارات
    'كويتي', // الكويت
    'قطري', // قطر
    'بحريني', // البحرين
    'مصري', // مصر
    'سوداني', // السودان
    'أردني', // الأردن
    'لبناني', // لبنان
    'سوري', // سوريا
    'عراقي', // العراق
    'فلسطيني', // فلسطين
    'ليبي', // ليبيا
    'تونسي', // تونس
    'جزائري', // الجزائر
    'مغربي', // المغرب
    'صومالي', // الصومال
    'إثيوبي', // إثيوبيا
    'إريتري', // إريتريا
    'جيبوتي', // جيبوتي
    'هندي', // الهند
    'باكستاني', // باكستان
    'بنغلاديشي', // بنغلاديش
    'إندونيسي', // إندونيسيا
    'فلبيني', // الفلبين
    'أخرى', // جنسيات أخرى
  ];

  /// قائمة الأجناس
  /// بسيطة ومباشرة للاستخدام في النماذج
  static const List<String> genders = ['ذكر', 'أنثى'];

  /// قائمة الحالات الصحية الشائعة
  /// تستخدم كاقتراحات في حقل الحالة الصحية
  static const List<String> commonHealthConditions = [
    'سليم', // حالة طبيعية
    'الربو', // Asthma
    'السكري النوع الأول', // Type 1 Diabetes
    'السكري النوع الثاني', // Type 2 Diabetes
    'حساسية الطعام', // Food Allergies
    'حساسية الأدوية', // Drug Allergies
    'حساسية الغبار', // Dust Allergies
    'ضعف البصر', // Vision Problems
    'ضعف السمع', // Hearing Problems
    'صعوبات التعلم', // Learning Difficulties
    'اضطراب نقص الانتباه', // ADHD
    'الصرع', // Epilepsy
    'أمراض القلب', // Heart Conditions
    'فقر الدم', // Anemia
    'الثلاسيميا', // Thalassemia
    'أنيميا الخلايا المنجلية', // Sickle Cell Anemia
    'أخرى', // Other conditions
  ];

  /// دالة للتحقق من صحة الرقم الوطني اليمني
  /// الرقم الوطني اليمني يتكون من 10 أرقام
  static bool isValidYemeniNationalId(String? nationalId) {
    if (nationalId == null || nationalId.isEmpty) {
      return true; // الحقل اختياري
    }

    // إزالة المسافات والرموز الخاصة
    String cleanId = nationalId.replaceAll(RegExp(r'[^\d]'), '');

    // التحقق من أن الطول 10 أرقام
    if (cleanId.length != 10) {
      return false;
    }

    // التحقق من أن جميع الأحرف أرقام
    return RegExp(r'^\d{10}$').hasMatch(cleanId);
  }

  /// دالة للتحقق من صحة رقم الهاتف اليمني
  /// أرقام الهواتف اليمنية تبدأ بـ 77, 73, 70, 71, 78 وتتكون من 9 أرقام
  static bool isValidYemeniPhoneNumber(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      return true; // الحقل اختياري
    }

    // إزالة المسافات والرموز الخاصة والأقواس
    String cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // إزالة رمز الدولة إذا كان موجوداً (+967 أو 00967)
    if (cleanPhone.startsWith('967')) {
      cleanPhone = cleanPhone.substring(3);
    }

    // التحقق من أن الطول 9 أرقام
    if (cleanPhone.length != 9) {
      return false;
    }

    // التحقق من أن الرقم يبدأ بأحد البادئات الصحيحة
    List<String> validPrefixes = ['77', '73', '70', '71', '78'];
    String prefix = cleanPhone.substring(0, 2);

    return validPrefixes.contains(prefix);
  }

  /// دالة لتنسيق رقم الهاتف اليمني
  /// تحويل الرقم إلى تنسيق موحد: 77X XXX XXX
  static String formatYemeniPhoneNumber(String phoneNumber) {
    String cleanPhone = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // إزالة رمز الدولة إذا كان موجوداً
    if (cleanPhone.startsWith('967')) {
      cleanPhone = cleanPhone.substring(3);
    }

    // تنسيق الرقم إذا كان صحيحاً
    if (cleanPhone.length == 9) {
      return '${cleanPhone.substring(0, 3)} ${cleanPhone.substring(3, 6)} ${cleanPhone.substring(6)}';
    }

    return phoneNumber; // إرجاع الرقم كما هو إذا لم يكن صحيحاً
  }

  /// دالة لتنسيق الرقم الوطني اليمني
  /// تحويل الرقم إلى تنسيق موحد: XXXX XXXX XX
  static String formatYemeniNationalId(String nationalId) {
    String cleanId = nationalId.replaceAll(RegExp(r'[^\d]'), '');

    // تنسيق الرقم إذا كان صحيحاً
    if (cleanId.length == 10) {
      return '${cleanId.substring(0, 4)} ${cleanId.substring(4, 8)} ${cleanId.substring(8)}';
    }

    return nationalId; // إرجاع الرقم كما هو إذا لم يكن صحيحاً
  }

  /// دالة للحصول على اسم المحافظة بالإنجليزية
  /// مفيدة للتكامل مع الخدمات الخارجية
  static String getGovernorateEnglishName(String arabicName) {
    const Map<String, String> governorateMap = {
      'أبين': 'Abyan',
      'البيضاء': 'Al Bayda',
      'الحديدة': 'Al Hudaydah',
      'الجوف': 'Al Jawf',
      'المحويت': 'Al Mahwit',
      'المهرة': 'Al Mahrah',
      'أمانة العاصمة': 'Amanat Al Asimah',
      'الضالع': 'Ad Dali',
      'حضرموت': 'Hadramawt',
      'حجة': 'Hajjah',
      'إب': 'Ibb',
      'لحج': 'Lahij',
      'مأرب': 'Marib',
      'ريمة': 'Raymah',
      'صعدة': 'Sa\'dah',
      'صنعاء': 'Sana\'a',
      'شبوة': 'Shabwah',
      'سقطرى': 'Socotra',
      'تعز': 'Taizz',
      'عدن': 'Aden',
      'عمران': 'Amran',
    };

    return governorateMap[arabicName] ?? arabicName;
  }
}
