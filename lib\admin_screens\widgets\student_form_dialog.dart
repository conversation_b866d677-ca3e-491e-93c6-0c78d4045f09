import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/constants/yemen_data_constants.dart';

class StudentFormDialog extends ConsumerStatefulWidget {
  final StudentModel? student;
  const StudentFormDialog({super.key, this.student});

  @override
  ConsumerState<StudentFormDialog> createState() => _StudentFormDialogState();
}

class _StudentFormDialogState extends ConsumerState<StudentFormDialog> {
  final _formKey = GlobalKey<FormState>();

  // ===== متحكمات الحقول الأساسية =====
  late final TextEditingController _nameController;
  late final TextEditingController _numberController;
  late final TextEditingController _emailController;
  late final TextEditingController _passwordController;

  // ===== متحكمات الحقول الشخصية الجديدة =====
  late final TextEditingController _phoneController;
  late final TextEditingController _addressController;
  late final TextEditingController _nationalIdController;
  late final TextEditingController _guardianNameController;
  late final TextEditingController _guardianPhoneController;
  late final TextEditingController _notesController;

  // ===== متغيرات الاختيار من القوائم المنسدلة =====
  String? _selectedClassId;
  String? _selectedClassName;
  String? _selectedGender;
  String? _selectedGovernorate;
  String? _selectedNationality;
  String? _selectedBloodType;
  String? _selectedHealthCondition;
  DateTime? _selectedDateOfBirth;

  // ===== متغيرات أخرى =====
  Uint8List? _imageBytes;
  File? _imageFile; // للاحتفاظ بالملف في الموبايل
  bool _isLoading = false; // حالة التحميل
  bool get _isEditing => widget.student != null; // هل نحن في وضع التعديل؟

  @override
  void initState() {
    super.initState();
    final student = widget.student;

    // ===== تهيئة متحكمات الحقول الأساسية =====
    _nameController = TextEditingController(text: student?.name);
    _numberController = TextEditingController(
      text:
          student?.studentNumber ??
          'S-${DateTime.now().millisecondsSinceEpoch}',
    );
    _emailController = TextEditingController(text: student?.email);
    _passwordController = TextEditingController();

    // ===== تهيئة متحكمات الحقول الشخصية الجديدة =====
    _phoneController = TextEditingController(text: student?.phoneNumber);
    _addressController = TextEditingController(text: student?.address);
    _nationalIdController = TextEditingController(text: student?.nationalId);
    _guardianNameController = TextEditingController(
      text: student?.guardianName,
    );
    _guardianPhoneController = TextEditingController(
      text: student?.guardianPhone,
    );
    _notesController = TextEditingController(text: student?.notes);

    // ===== تهيئة متغيرات الاختيار =====
    _selectedClassId = student?.classId;
    _selectedClassName = student?.studentClass;
    _selectedGender = student?.gender;
    _selectedGovernorate = student?.governorate;
    _selectedNationality = student?.nationality ?? 'يمني';
    _selectedBloodType = student?.bloodType;
    _selectedHealthCondition = student?.healthCondition;
    _selectedDateOfBirth = student?.dateOfBirth;
  }

  @override
  void dispose() {
    // ===== تنظيف متحكمات الحقول الأساسية =====
    _nameController.dispose();
    _numberController.dispose();
    _emailController.dispose();
    _passwordController.dispose();

    // ===== تنظيف متحكمات الحقول الشخصية الجديدة =====
    _phoneController.dispose();
    _addressController.dispose();
    _nationalIdController.dispose();
    _guardianNameController.dispose();
    _guardianPhoneController.dispose();
    _notesController.dispose();

    super.dispose();
  }

  /// دالة اختيار الصورة الشخصية للطالب
  /// تدعم الويب والموبايل
  Future<void> _pickImage() async {
    final pickedFile = await ImagePicker().pickImage(
      source: ImageSource.gallery,
    );
    if (pickedFile != null) {
      final bytes = await pickedFile.readAsBytes();
      setState(() {
        _imageBytes = bytes;
        if (!kIsWeb) {
          _imageFile = File(pickedFile.path);
        }
      });
    }
  }

  /// دالة اختيار تاريخ الميلاد
  /// تعرض منتقي التاريخ وتحدث المتغير المحدد
  Future<void> _pickDateOfBirth() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _selectedDateOfBirth ??
          DateTime.now().subtract(
            const Duration(days: 365 * 10),
          ), // افتراضي: 10 سنوات
      firstDate: DateTime(1950), // أقدم تاريخ ميلاد مسموح
      lastDate: DateTime.now(), // لا يمكن أن يكون تاريخ الميلاد في المستقبل
      helpText: 'اختر تاريخ الميلاد',
      cancelText: 'إلغاء',
      confirmText: 'تأكيد',
    );

    if (picked != null && picked != _selectedDateOfBirth) {
      setState(() {
        _selectedDateOfBirth = picked;
      });
    }
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      final studentService = ref.read(
        firebaseServiceProvider,
      ); // الوصول الصحيح للخدمة
      try {
        setState(() => _isLoading = true);

        // ================== ملاحظة هامة لمرحلة التطوير ==================
        // تم تعطيل ميزة رفع الصور مؤقتًا لأنها تتطلب خطة Blaze المدفوعة
        // لحل مشكلة CORS عند الرفع من localhost.
        // لإعادة تفعيلها في المستقبل، قم بإزالة السطر التالي الذي يفرض
        // قيمة null، وأعد تمرير `_imageBytes` الأصلي.
        Uint8List? imageToUpload = null;
        // =================================================================

        if (widget.student == null) {
          // إضافة طالب جديد مع جميع الحقول الجديدة
          await studentService.addStudent(
            _nameController.text,
            _numberController.text,
            _selectedClassId,
            _selectedClassName,
            _emailController.text,
            _passwordController.text,
            _selectedGender,
            imageToUpload, // تم التعديل هنا
            // ===== الحقول الشخصية الجديدة =====
            dateOfBirth: _selectedDateOfBirth,
            phoneNumber:
                _phoneController.text.isNotEmpty ? _phoneController.text : null,
            address:
                _addressController.text.isNotEmpty
                    ? _addressController.text
                    : null,
            // ===== الحقول المطلوبة للمدارس اليمنية =====
            nationalId:
                _nationalIdController.text.isNotEmpty
                    ? _nationalIdController.text
                    : null,
            guardianName: _guardianNameController.text,
            guardianPhone:
                _guardianPhoneController.text.isNotEmpty
                    ? _guardianPhoneController.text
                    : null,
            governorate: _selectedGovernorate!,
            nationality: _selectedNationality ?? 'يمني',
            bloodType: _selectedBloodType,
            healthCondition: _selectedHealthCondition,
            notes:
                _notesController.text.isNotEmpty ? _notesController.text : null,
          );
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تمت إضافة الطالب بنجاح')),
          );
        } else {
          // تحديث بيانات الطالب مع جميع الحقول الجديدة
          await studentService.updateStudent(
            widget.student!.id,
            _nameController.text,
            _numberController.text,
            _selectedClassId,
            _selectedClassName,
            widget.student!.classId,
            _selectedGender,
            imageToUpload, // تم التعديل هنا
            // ===== الحقول الشخصية الجديدة =====
            dateOfBirth: _selectedDateOfBirth,
            phoneNumber:
                _phoneController.text.isNotEmpty ? _phoneController.text : null,
            address:
                _addressController.text.isNotEmpty
                    ? _addressController.text
                    : null,
            // ===== الحقول المطلوبة للمدارس اليمنية =====
            nationalId:
                _nationalIdController.text.isNotEmpty
                    ? _nationalIdController.text
                    : null,
            guardianName:
                _guardianNameController.text.isNotEmpty
                    ? _guardianNameController.text
                    : null,
            guardianPhone:
                _guardianPhoneController.text.isNotEmpty
                    ? _guardianPhoneController.text
                    : null,
            governorate: _selectedGovernorate,
            nationality: _selectedNationality,
            bloodType: _selectedBloodType,
            healthCondition: _selectedHealthCondition,
            notes:
                _notesController.text.isNotEmpty ? _notesController.text : null,
          );
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تحديث بيانات الطالب بنجاح')),
          );
        }
        Navigator.of(context).pop();
      } catch (e) {
        print('Error in student form submission: $e');
        print('Stack trace: ${StackTrace.current}');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل حفظ بيانات الطالب: ${e.toString()}')),
        );
      } finally {
        if (mounted) {
          // التأكد من أن الويدجت لا يزال في الشجرة
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(_isEditing ? 'تعديل بيانات الطالب' : 'إضافة طالب جديد'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_imageBytes != null)
                Image.memory(_imageBytes!, height: 100)
              else if (widget.student?.imageUrl != null)
                Image.network(widget.student!.imageUrl!, height: 100),
              TextButton.icon(
                icon: const Icon(Icons.image),
                label: const Text('اختيار صورة'),
                onPressed: _pickImage,
              ),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'الاسم الكامل'),
                validator: (v) => v!.isEmpty ? 'الاسم مطلوب' : null,
              ),
              TextFormField(
                controller: _numberController,
                decoration: const InputDecoration(labelText: 'الرقم الأكاديمي'),
                readOnly: true,
                validator: (v) => v!.isEmpty ? 'الرقم الأكاديمي مطلوب' : null,
              ),
              DropdownButtonFormField<String>(
                items: const [
                  DropdownMenuItem(value: 'ذكر', child: Text('ذكر')),
                  DropdownMenuItem(value: 'أنثى', child: Text('أنثى')),
                ],
                value: _selectedGender,
                onChanged: (value) {
                  setState(() {
                    _selectedGender = value;
                  });
                },
                decoration: const InputDecoration(
                  labelText: 'الجنس',
                  border: OutlineInputBorder(),
                ),
                validator: (v) => v == null ? 'الجنس مطلوب' : null,
              ),
              const SizedBox(height: 16),
              Consumer(
                builder: (context, ref, child) {
                  final classesAsyncValue = ref.watch(classesStreamProvider);
                  return classesAsyncValue.when(
                    data: (classes) {
                      final items =
                          classes.map((classModel) {
                            return DropdownMenuItem<String>(
                              value: classModel.id,
                              child: Text(classModel.name),
                            );
                          }).toList();

                      return DropdownButtonFormField<String>(
                        items: items,
                        value: _selectedClassId,
                        onChanged: (value) {
                          setState(() {
                            _selectedClassId = value;
                            _selectedClassName =
                                classes
                                    .firstWhere(
                                      (classModel) => classModel.id == value,
                                    )
                                    .name;
                          });
                        },
                        decoration: const InputDecoration(
                          labelText: 'الصف',
                          border: OutlineInputBorder(),
                        ),
                        validator: (v) => v == null ? 'الصف مطلوب' : null,
                      );
                    },
                    loading: () => const CircularProgressIndicator(),
                    error: (err, stack) => const Text('فشل تحميل الصفوف'),
                  );
                },
              ),

              // ===== قسم المعلومات الشخصية =====
              const SizedBox(height: 16),
              const Divider(),
              const Text(
                'المعلومات الشخصية',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              // حقل تاريخ الميلاد
              InkWell(
                onTap: _pickDateOfBirth,
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'تاريخ الميلاد',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    _selectedDateOfBirth != null
                        ? '${_selectedDateOfBirth!.day}/${_selectedDateOfBirth!.month}/${_selectedDateOfBirth!.year}'
                        : 'اختر تاريخ الميلاد',
                    style: TextStyle(
                      color:
                          _selectedDateOfBirth != null
                              ? Colors.black
                              : Colors.grey,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // حقل رقم الهاتف
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف (اختياري)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.phone),
                  hintText: '77X XXX XXX',
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (!YemenDataConstants.isValidYemeniPhoneNumber(value)) {
                      return 'رقم هاتف غير صحيح';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // حقل العنوان
              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(
                  labelText: 'العنوان (اختياري)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.location_on),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 16),

              // ===== قسم معلومات ولي الأمر =====
              const Divider(),
              const Text(
                'معلومات ولي الأمر',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              // حقل اسم ولي الأمر
              TextFormField(
                controller: _guardianNameController,
                decoration: const InputDecoration(
                  labelText: 'اسم ولي الأمر *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person),
                ),
                validator: (v) => v!.isEmpty ? 'اسم ولي الأمر مطلوب' : null,
              ),
              const SizedBox(height: 16),

              // حقل رقم هاتف ولي الأمر
              TextFormField(
                controller: _guardianPhoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم هاتف ولي الأمر (اختياري)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.phone),
                  hintText: '77X XXX XXX',
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (!YemenDataConstants.isValidYemeniPhoneNumber(value)) {
                      return 'رقم هاتف غير صحيح';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // ===== قسم المعلومات الإضافية =====
              const Divider(),
              const Text(
                'المعلومات الإضافية',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              // حقل المحافظة
              DropdownButtonFormField<String>(
                items:
                    YemenDataConstants.yemenGovernorates.map((governorate) {
                      return DropdownMenuItem<String>(
                        value: governorate,
                        child: Text(governorate),
                      );
                    }).toList(),
                value: _selectedGovernorate,
                onChanged: (value) {
                  setState(() {
                    _selectedGovernorate = value;
                  });
                },
                decoration: const InputDecoration(
                  labelText: 'المحافظة *',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.location_city),
                ),
                validator: (v) => v == null ? 'المحافظة مطلوبة' : null,
              ),
              const SizedBox(height: 16),

              // حقل الجنسية
              DropdownButtonFormField<String>(
                items:
                    YemenDataConstants.nationalities.map((nationality) {
                      return DropdownMenuItem<String>(
                        value: nationality,
                        child: Text(nationality),
                      );
                    }).toList(),
                value: _selectedNationality,
                onChanged: (value) {
                  setState(() {
                    _selectedNationality = value;
                  });
                },
                decoration: const InputDecoration(
                  labelText: 'الجنسية',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.flag),
                ),
              ),
              const SizedBox(height: 16),

              // حقل الرقم الوطني
              TextFormField(
                controller: _nationalIdController,
                decoration: const InputDecoration(
                  labelText: 'الرقم الوطني (اختياري)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.credit_card),
                  hintText: 'XXXX XXXX XX',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (!YemenDataConstants.isValidYemeniNationalId(value)) {
                      return 'الرقم الوطني غير صحيح (10 أرقام)';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // حقل فصيلة الدم
              DropdownButtonFormField<String>(
                items:
                    YemenDataConstants.bloodTypes.map((bloodType) {
                      return DropdownMenuItem<String>(
                        value: bloodType,
                        child: Text(bloodType),
                      );
                    }).toList(),
                value: _selectedBloodType,
                onChanged: (value) {
                  setState(() {
                    _selectedBloodType = value;
                  });
                },
                decoration: const InputDecoration(
                  labelText: 'فصيلة الدم (اختياري)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.bloodtype),
                ),
              ),
              const SizedBox(height: 16),

              // حقل الحالة الصحية
              DropdownButtonFormField<String>(
                items:
                    YemenDataConstants.commonHealthConditions.map((condition) {
                      return DropdownMenuItem<String>(
                        value: condition,
                        child: Text(condition),
                      );
                    }).toList(),
                value: _selectedHealthCondition,
                onChanged: (value) {
                  setState(() {
                    _selectedHealthCondition = value;
                  });
                },
                decoration: const InputDecoration(
                  labelText: 'الحالة الصحية (اختياري)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.health_and_safety),
                ),
              ),
              const SizedBox(height: 16),

              // حقل الملاحظات
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات خاصة (اختياري)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.note),
                  hintText: 'أي ملاحظات إضافية مهمة...',
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),

              if (!_isEditing)
                TextFormField(
                  controller: _emailController,
                  decoration: const InputDecoration(
                    labelText: 'البريد الإلكتروني',
                  ),
                  validator:
                      (v) =>
                          v!.isEmpty || !v.contains('@')
                              ? 'بريد إلكتروني غير صالح'
                              : null,
                ),
              if (!_isEditing)
                TextFormField(
                  controller: _passwordController,
                  decoration: const InputDecoration(labelText: 'كلمة المرور'),
                  obscureText: true,
                  validator:
                      (v) => v!.length < 6 ? 'كلمة المرور قصيرة جداً' : null,
                ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _submit, // تعطيل الزر أثناء التحميل
          child:
              _isLoading
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : const Text('حفظ'),
        ),
      ],
    );
  }
}
