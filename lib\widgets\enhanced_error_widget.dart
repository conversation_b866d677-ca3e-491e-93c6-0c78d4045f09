import 'package:flutter/material.dart';
import 'package:school_management_system/utils/error_handler.dart';

/// Widget محسن لعرض الأخطاء بشكل جميل ومفيد
/// 
/// يعرض رسالة خطأ واضحة مع أيقونة مناسبة وإمكانية إعادة المحاولة
class EnhancedErrorWidget extends StatelessWidget {
  final dynamic error;
  final VoidCallback? onRetry;
  final String? customMessage;
  final bool showDetails;
  final EdgeInsets padding;

  const EnhancedErrorWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.customMessage,
    this.showDetails = false,
    this.padding = const EdgeInsets.all(16.0),
  });

  @override
  Widget build(BuildContext context) {
    final errorInfo = ErrorHandler.analyzeError(error);
    final theme = Theme.of(context);

    return Container(
      padding: padding,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة الخطأ
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getErrorColor(errorInfo.type).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getErrorIcon(errorInfo.type),
                size: 48,
                color: _getErrorColor(errorInfo.type),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // عنوان الخطأ
            Text(
              _getErrorTitle(errorInfo.type),
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: _getErrorColor(errorInfo.type),
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 8),
            
            // رسالة الخطأ
            Text(
              customMessage ?? errorInfo.message,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 24),
            
            // أزرار الإجراءات
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // زر إعادة المحاولة
                if (errorInfo.canRetry && onRetry != null) ...[
                  ElevatedButton.icon(
                    onPressed: onRetry,
                    icon: const Icon(Icons.refresh),
                    label: Text(errorInfo.actionText),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getErrorColor(errorInfo.type),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                
                // زر عرض التفاصيل
                if (showDetails)
                  OutlinedButton.icon(
                    onPressed: () => _showErrorDetails(context, errorInfo),
                    icon: const Icon(Icons.info_outline),
                    label: const Text('التفاصيل'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: _getErrorColor(errorInfo.type),
                      side: BorderSide(color: _getErrorColor(errorInfo.type)),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // نصائح للمستخدم
            if (_getErrorTips(errorInfo.type).isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.lightbulb_outline, 
                             color: Colors.blue[600], size: 16),
                        const SizedBox(width: 4),
                        Text(
                          'نصائح:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    ..._getErrorTips(errorInfo.type).map(
                      (tip) => Padding(
                        padding: const EdgeInsets.only(top: 2),
                        child: Text(
                          '• $tip',
                          style: TextStyle(
                            color: Colors.blue[600],
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// عرض تفاصيل الخطأ في dialog
  void _showErrorDetails(BuildContext context, ErrorInfo errorInfo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفاصيل الخطأ'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('نوع الخطأ: ${errorInfo.type}'),
              const SizedBox(height: 8),
              Text('الرسالة: ${errorInfo.message}'),
              const SizedBox(height: 8),
              if (errorInfo.originalError != null) ...[
                const Text('التفاصيل التقنية:'),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    errorInfo.originalError.toString(),
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// الحصول على عنوان مناسب لنوع الخطأ
  String _getErrorTitle(String errorType) {
    switch (errorType) {
      case ErrorHandler.authError:
        return 'خطأ في المصادقة';
      case ErrorHandler.firestoreError:
        return 'خطأ في قاعدة البيانات';
      case ErrorHandler.networkError:
        return 'مشكلة في الاتصال';
      case ErrorHandler.validationError:
        return 'خطأ في البيانات';
      case ErrorHandler.permissionError:
        return 'ليس لديك صلاحية';
      default:
        return 'حدث خطأ';
    }
  }

  /// الحصول على أيقونة مناسبة لنوع الخطأ
  IconData _getErrorIcon(String errorType) {
    switch (errorType) {
      case ErrorHandler.authError:
        return Icons.lock_outline;
      case ErrorHandler.firestoreError:
        return Icons.cloud_off;
      case ErrorHandler.networkError:
        return Icons.wifi_off;
      case ErrorHandler.validationError:
        return Icons.error_outline;
      case ErrorHandler.permissionError:
        return Icons.security;
      default:
        return Icons.warning;
    }
  }

  /// الحصول على لون مناسب لنوع الخطأ
  Color _getErrorColor(String errorType) {
    switch (errorType) {
      case ErrorHandler.authError:
        return Colors.red[700]!;
      case ErrorHandler.firestoreError:
        return Colors.orange[700]!;
      case ErrorHandler.networkError:
        return Colors.blue[700]!;
      case ErrorHandler.validationError:
        return Colors.amber[700]!;
      case ErrorHandler.permissionError:
        return Colors.purple[700]!;
      default:
        return Colors.red[600]!;
    }
  }

  /// الحصول على نصائح مفيدة للمستخدم حسب نوع الخطأ
  List<String> _getErrorTips(String errorType) {
    switch (errorType) {
      case ErrorHandler.authError:
        return [
          'تأكد من صحة البريد الإلكتروني وكلمة المرور',
          'تواصل مع الإدارة إذا كان الحساب معطلاً',
        ];
      case ErrorHandler.firestoreError:
        return [
          'تأكد من اتصالك بالإنترنت',
          'حاول مرة أخرى بعد قليل',
        ];
      case ErrorHandler.networkError:
        return [
          'تحقق من اتصالك بالإنترنت',
          'تأكد من أن الواي فاي يعمل بشكل صحيح',
          'حاول التبديل بين الواي فاي وبيانات الجوال',
        ];
      case ErrorHandler.validationError:
        return [
          'تحقق من صحة البيانات المدخلة',
          'تأكد من ملء جميع الحقول المطلوبة',
        ];
      case ErrorHandler.permissionError:
        return [
          'تواصل مع الإدارة للحصول على الصلاحيات المطلوبة',
          'تأكد من أنك تستخدم الحساب الصحيح',
        ];
      default:
        return [
          'حاول إعادة تشغيل التطبيق',
          'تواصل مع الدعم الفني إذا استمرت المشكلة',
        ];
    }
  }
}
