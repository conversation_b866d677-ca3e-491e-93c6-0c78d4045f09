import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/constants/yemen_data_constants.dart';

// TODO: نقل منطق العمليات (add, update) إلى ViewModel/Controller خاص بالموظفين.

class StaffFormDialog extends ConsumerStatefulWidget {
  final UserModel? staff;
  const StaffFormDialog({super.key, this.staff});

  @override
  ConsumerState<StaffFormDialog> createState() => _StaffFormDialogState();
}

class _StaffFormDialogState extends ConsumerState<StaffFormDialog> {
  final _formKey = GlobalKey<FormState>();

  // ===== متحكمات الحقول الأساسية =====
  late final TextEditingController _nameController;
  late final TextEditingController _emailController;
  late final TextEditingController _passwordController;
  late final TextEditingController _jobTitleController;
  late final TextEditingController _bioController;

  // ===== متحكمات الحقول الشخصية الجديدة =====
  late final TextEditingController _phoneController;
  late final TextEditingController _addressController;
  late final TextEditingController _nationalIdController; // اختياري
  late final TextEditingController _notesController; // اختياري

  // ===== متغيرات الاختيار =====
  String _selectedRole = 'teacher';
  String? _selectedGender;
  String? _selectedGovernorate; // اختياري
  String? _selectedNationality;
  String? _selectedBloodType; // اختياري
  String? _selectedHealthCondition; // اختياري
  DateTime? _selectedDateOfBirth;

  // ===== متغيرات الصورة =====
  XFile? _imageFile;
  String? _existingImageUrl;

  // ===== حالة النموذج =====
  bool get _isEditing => widget.staff != null;

  @override
  void initState() {
    super.initState();
    final staff = widget.staff;

    // ===== تهيئة متحكمات الحقول الأساسية =====
    _nameController = TextEditingController(text: staff?.name);
    _emailController = TextEditingController(text: staff?.email);
    _passwordController = TextEditingController();
    _jobTitleController = TextEditingController(text: staff?.jobTitle);
    _bioController = TextEditingController(text: staff?.bio);

    // ===== تهيئة متحكمات الحقول الشخصية الجديدة =====
    _phoneController = TextEditingController(text: staff?.phoneNumber);
    _addressController = TextEditingController(text: staff?.address);
    _nationalIdController = TextEditingController(text: staff?.nationalId);
    _notesController = TextEditingController(text: staff?.notes);

    // ===== تهيئة متغيرات الاختيار =====
    _selectedRole = staff?.role ?? 'teacher';
    _selectedGender = staff?.gender;
    _selectedGovernorate = staff?.governorate;
    _selectedNationality = staff?.nationality ?? 'يمني'; // افتراضي يمني
    _selectedBloodType = staff?.bloodType;
    _selectedHealthCondition = staff?.healthCondition;
    _selectedDateOfBirth = staff?.dateOfBirth;

    // ===== تهيئة الصورة =====
    _existingImageUrl = staff?.profileImageUrl;
  }

  @override
  void dispose() {
    // ===== تنظيف متحكمات الحقول الأساسية =====
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _jobTitleController.dispose();
    _bioController.dispose();

    // ===== تنظيف متحكمات الحقول الشخصية الجديدة =====
    _phoneController.dispose();
    _addressController.dispose();
    _nationalIdController.dispose();
    _notesController.dispose();

    super.dispose();
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate()) {
      // ===== التحقق من الحقول المطلوبة الإضافية =====
      if (_selectedDateOfBirth == null) {
        showErrorSnackBar(context, 'يرجى اختيار تاريخ الميلاد');
        return;
      }

      if (_selectedGender == null || _selectedGender!.isEmpty) {
        showErrorSnackBar(context, 'يرجى اختيار الجنس');
        return;
      }

      if (_selectedNationality == null || _selectedNationality!.isEmpty) {
        showErrorSnackBar(context, 'يرجى اختيار الجنسية');
        return;
      }
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      final firebaseService = ref.read(firebaseServiceProvider);

      try {
        if (_isEditing) {
          // ===== تحديث بيانات الموظف مع الحقول الجديدة =====
          await firebaseService.updateStaff(
            // ===== الحقول الأساسية =====
            staff: widget.staff!,
            name: _nameController.text,
            jobTitle: _jobTitleController.text,
            bio: _bioController.text,
            role: _selectedRole,
            newImage: _imageFile,

            // ===== الحقول الشخصية الجديدة المطلوبة =====
            phoneNumber: _phoneController.text,
            dateOfBirth: _selectedDateOfBirth!,
            address: _addressController.text,
            gender: _selectedGender!,
            nationality: _selectedNationality!,

            // ===== الحقول الاختيارية =====
            nationalId:
                _nationalIdController.text.isNotEmpty
                    ? _nationalIdController.text
                    : null,
            governorate: _selectedGovernorate,
            bloodType: _selectedBloodType,
            healthCondition: _selectedHealthCondition,
            notes:
                _notesController.text.isNotEmpty ? _notesController.text : null,
          );
        } else {
          // ===== إضافة موظف جديد مع الحقول الجديدة =====
          await firebaseService.addStaff(
            // ===== الحقول الأساسية =====
            name: _nameController.text,
            email: _emailController.text,
            password: _passwordController.text,
            jobTitle: _jobTitleController.text,
            bio: _bioController.text,
            role: _selectedRole,
            image: _imageFile,

            // ===== الحقول الشخصية الجديدة المطلوبة =====
            phoneNumber: _phoneController.text,
            dateOfBirth: _selectedDateOfBirth!,
            address: _addressController.text,
            gender: _selectedGender!,
            nationality: _selectedNationality!,

            // ===== الحقول الاختيارية =====
            nationalId:
                _nationalIdController.text.isNotEmpty
                    ? _nationalIdController.text
                    : null,
            governorate: _selectedGovernorate,
            bloodType: _selectedBloodType,
            healthCondition: _selectedHealthCondition,
            notes:
                _notesController.text.isNotEmpty ? _notesController.text : null,
          );
        }
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        Navigator.of(context).pop(); // إغلاق الـ Dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _isEditing ? 'تم تحديث البيانات بنجاح' : 'تمت إضافة الموظف بنجاح',
            ),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// دالة مساعدة لعرض رسائل الخطأ
  void showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(_isEditing ? 'تعديل بيانات الموظف' : 'إضافة موظف جديد'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // --- قسم الصورة الشخصية ---
              Stack(
                alignment: Alignment.bottomRight,
                children: [
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: Colors.grey.shade200,
                    backgroundImage:
                        _imageFile != null
                            ? FileImage(File(_imageFile!.path))
                            : (_existingImageUrl != null &&
                                        _existingImageUrl!.isNotEmpty
                                    ? NetworkImage(_existingImageUrl!)
                                    : null)
                                as ImageProvider?,
                    child:
                        (_imageFile == null &&
                                (_existingImageUrl == null ||
                                    _existingImageUrl!.isEmpty))
                            ? const Icon(
                              Icons.person,
                              size: 50,
                              color: Colors.grey,
                            )
                            : null,
                  ),
                  IconButton(
                    icon: const CircleAvatar(
                      radius: 18,
                      backgroundColor: Colors.blue,
                      child: Icon(Icons.edit, color: Colors.white, size: 18),
                    ),
                    onPressed: () async {
                      final pickedImage =
                          await ref.read(firebaseServiceProvider).pickImage();
                      if (pickedImage != null) {
                        setState(() {
                          _imageFile = pickedImage;
                        });
                      }
                    },
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // --- حقول البيانات ---
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'الاسم الكامل'),
                validator:
                    (value) => value!.isEmpty ? 'الرجاء إدخال الاسم' : null,
              ),
              if (!_isEditing) ...[
                TextFormField(
                  controller: _emailController,
                  decoration: const InputDecoration(
                    labelText: 'البريد الإلكتروني',
                  ),
                  validator:
                      (value) =>
                          value!.isEmpty || !value.contains('@')
                              ? 'بريد إلكتروني غير صالح'
                              : null,
                ),
                TextFormField(
                  controller: _passwordController,
                  decoration: const InputDecoration(labelText: 'كلمة المرور'),
                  obscureText: true,
                  validator:
                      (value) =>
                          value!.length < 6
                              ? 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
                              : null,
                ),
              ],
              TextFormField(
                controller: _jobTitleController,
                decoration: const InputDecoration(
                  labelText: 'المسمى الوظيفي (مثال: معلم رياضيات)',
                ),
                validator:
                    (value) =>
                        value!.isEmpty ? 'الرجاء إدخال المسمى الوظيفي' : null,
              ),
              TextFormField(
                controller: _bioController,
                decoration: const InputDecoration(labelText: 'نبذة تعريفية'),
                maxLines: 3,
              ),

              const SizedBox(height: 24),

              // ===== قسم المعلومات الشخصية المطلوبة =====
              const Align(
                alignment: Alignment.centerRight,
                child: Text(
                  'المعلومات الشخصية المطلوبة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // رقم الهاتف (مطلوب)
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف *',
                  hintText: 'مثال: 777123456',
                  prefixIcon: Icon(Icons.phone),
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'رقم الهاتف مطلوب';
                  }
                  if (value.length < 9) {
                    return 'رقم الهاتف يجب أن يكون 9 أرقام على الأقل';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // تاريخ الميلاد (مطلوب)
              InkWell(
                onTap: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: _selectedDateOfBirth ?? DateTime(1990),
                    firstDate: DateTime(1950),
                    lastDate: DateTime.now().subtract(
                      const Duration(days: 365 * 18),
                    ), // 18 سنة على الأقل
                    helpText: 'اختر تاريخ الميلاد',
                    cancelText: 'إلغاء',
                    confirmText: 'تأكيد',
                  );
                  if (date != null) {
                    setState(() {
                      _selectedDateOfBirth = date;
                    });
                  }
                },
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'تاريخ الميلاد *',
                    prefixIcon: Icon(Icons.calendar_today),
                    border: OutlineInputBorder(),
                  ),
                  child: Text(
                    _selectedDateOfBirth != null
                        ? '${_selectedDateOfBirth!.day}/${_selectedDateOfBirth!.month}/${_selectedDateOfBirth!.year}'
                        : 'اختر تاريخ الميلاد',
                    style: TextStyle(
                      color:
                          _selectedDateOfBirth != null
                              ? Colors.black
                              : Colors.grey,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // الجنس (مطلوب)
              DropdownButtonFormField<String>(
                value: _selectedGender,
                decoration: const InputDecoration(
                  labelText: 'الجنس *',
                  prefixIcon: Icon(Icons.person),
                  border: OutlineInputBorder(),
                ),
                items:
                    YemenDataConstants.genders.map((gender) {
                      return DropdownMenuItem(
                        value: gender,
                        child: Text(gender),
                      );
                    }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedGender = value;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الجنس مطلوب';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // العنوان (مطلوب)
              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(
                  labelText: 'العنوان *',
                  hintText: 'مثال: شارع الستين، صنعاء',
                  prefixIcon: Icon(Icons.location_on),
                ),
                maxLines: 2,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'العنوان مطلوب';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // الجنسية (مطلوب - افتراضي يمني)
              DropdownButtonFormField<String>(
                value: _selectedNationality,
                decoration: const InputDecoration(
                  labelText: 'الجنسية *',
                  prefixIcon: Icon(Icons.flag),
                  border: OutlineInputBorder(),
                ),
                items:
                    YemenDataConstants.nationalities.map((nationality) {
                      return DropdownMenuItem(
                        value: nationality,
                        child: Text(nationality),
                      );
                    }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedNationality = value;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الجنسية مطلوبة';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 24),

              // ===== قسم المعلومات الاختيارية =====
              const Align(
                alignment: Alignment.centerRight,
                child: Text(
                  'المعلومات الاختيارية',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // الرقم الوطني (اختياري)
              TextFormField(
                controller: _nationalIdController,
                decoration: const InputDecoration(
                  labelText: 'الرقم الوطني (اختياري)',
                  hintText: 'مثال: 01234567890',
                  prefixIcon: Icon(Icons.credit_card),
                ),
                keyboardType: TextInputType.number,
                // لا يوجد validator لأنه اختياري
              ),

              const SizedBox(height: 16),

              // المحافظة (اختيارية)
              DropdownButtonFormField<String>(
                value: _selectedGovernorate,
                decoration: const InputDecoration(
                  labelText: 'المحافظة (اختيارية)',
                  prefixIcon: Icon(Icons.location_city),
                  border: OutlineInputBorder(),
                ),
                items:
                    YemenDataConstants.yemenGovernorates.map((governorate) {
                      return DropdownMenuItem(
                        value: governorate,
                        child: Text(governorate),
                      );
                    }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedGovernorate = value;
                  });
                },
                // لا يوجد validator لأنه اختياري
              ),

              const SizedBox(height: 16),

              // فصيلة الدم (اختيارية)
              DropdownButtonFormField<String>(
                value: _selectedBloodType,
                decoration: const InputDecoration(
                  labelText: 'فصيلة الدم (اختيارية)',
                  prefixIcon: Icon(Icons.bloodtype),
                  border: OutlineInputBorder(),
                ),
                items:
                    YemenDataConstants.bloodTypes.map((bloodType) {
                      return DropdownMenuItem(
                        value: bloodType,
                        child: Text(bloodType),
                      );
                    }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedBloodType = value;
                  });
                },
                // لا يوجد validator لأنه اختياري
              ),

              const SizedBox(height: 16),

              // الحالة الصحية (اختيارية)
              DropdownButtonFormField<String>(
                value: _selectedHealthCondition,
                decoration: const InputDecoration(
                  labelText: 'الحالة الصحية (اختيارية)',
                  prefixIcon: Icon(Icons.health_and_safety),
                  border: OutlineInputBorder(),
                ),
                items:
                    YemenDataConstants.commonHealthConditions.map((condition) {
                      return DropdownMenuItem(
                        value: condition,
                        child: Text(condition),
                      );
                    }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedHealthCondition = value;
                  });
                },
                // لا يوجد validator لأنه اختياري
              ),

              const SizedBox(height: 16),

              // ملاحظات (اختيارية)
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات إضافية (اختيارية)',
                  hintText: 'أي معلومات إضافية مهمة...',
                  prefixIcon: Icon(Icons.note),
                ),
                maxLines: 3,
                // لا يوجد validator لأنه اختياري
              ),

              const SizedBox(height: 24),

              // ===== قسم الدور والصلاحيات =====
              const Align(
                alignment: Alignment.centerRight,
                child: Text(
                  'الدور والصلاحيات',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
              ),
              const SizedBox(height: 16),

              DropdownButtonFormField<String>(
                value: _selectedRole,
                decoration: const InputDecoration(
                  labelText: 'الدور والصلاحيات *',
                  border: OutlineInputBorder(),
                ),
                items:
                    ['teacher', 'staff']
                        .map(
                          (role) => DropdownMenuItem(
                            value: role,
                            child: Text(role == 'teacher' ? 'معلم' : 'إداري'),
                          ),
                        )
                        .toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() => _selectedRole = value);
                  }
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(onPressed: _submit, child: const Text('حفظ')),
      ],
    );
  }
}
