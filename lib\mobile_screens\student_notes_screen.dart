import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/note_model.dart';
import 'package:school_management_system/providers/note_providers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/widgets/enhanced_error_widget.dart';

/// شاشة لعرض وإرسال الملاحظات بين الطالب والإدارة
class StudentNotesScreen extends ConsumerWidget {
  final String studentId;
  const StudentNotesScreen({super.key, required this.studentId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notesAsyncValue = ref.watch(notesStreamProvider(studentId));
    final currentUser = FirebaseAuth.instance.currentUser;

    if (currentUser == null) {
      return const Center(child: Text('يجب تسجيل الدخول.'));
    }

    return Scaffold(
      body: Column(
        children: [
          // قائمة الملاحظات
          Expanded(
            child: notesAsyncValue.when(
              loading: () => const LoadingIndicator(),
              error:
                  (err, stack) => EnhancedErrorWidget(
                    error: err,
                    customMessage: 'حدث خطأ في تحميل الملاحظات',
                    onRetry:
                        () => ref.invalidate(notesStreamProvider(studentId)),
                    showDetails: true,
                  ),
              data: (notes) {
                if (notes.isEmpty) {
                  return const Center(child: Text('لا توجد ملاحظات لعرضها.'));
                }
                return ListView.builder(
                  reverse: true, // لعرض أحدث الملاحظات في الأسفل
                  padding: const EdgeInsets.all(16.0),
                  itemCount: notes.length,
                  itemBuilder: (context, index) {
                    final note = notes[index];
                    final isMe = note.senderId == currentUser.uid;
                    return _NoteBubble(note: note, isMe: isMe);
                  },
                );
              },
            ),
          ),
          // حقل إدخال الملاحظة
          const _NoteInputField(),
        ],
      ),
    );
  }
}

/// ويدجت لعرض فقاعة الملاحظة
class _NoteBubble extends StatelessWidget {
  const _NoteBubble({required this.note, required this.isMe});

  final NoteModel note;
  final bool isMe;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 5),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color:
              isMe
                  ? Theme.of(context).primaryColor.withOpacity(0.8)
                  : Colors.grey.shade300,
          borderRadius: BorderRadius.only(
            topLeft: const Radius.circular(12),
            topRight: const Radius.circular(12),
            bottomLeft: isMe ? const Radius.circular(12) : Radius.zero,
            bottomRight: isMe ? Radius.zero : const Radius.circular(12),
          ),
        ),
        child: Column(
          crossAxisAlignment:
              isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
          children: [
            Text(
              note.content,
              style: GoogleFonts.cairo(
                color: isMe ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              DateFormat.jm('ar').format(note.timestamp), // عرض الوقت فقط
              style: GoogleFonts.cairo(
                fontSize: 10,
                color: isMe ? Colors.white70 : Colors.black54,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// ويدجت لحقل إدخال الملاحظة
class _NoteInputField extends ConsumerStatefulWidget {
  const _NoteInputField();

  @override
  ConsumerState<_NoteInputField> createState() => _NoteInputFieldState();
}

class _NoteInputFieldState extends ConsumerState<_NoteInputField> {
  final _noteController = TextEditingController();

  Future<void> _sendNote() async {
    if (_noteController.text.trim().isEmpty) {
      return;
    }
    await ref
        .read(noteControllerProvider.notifier)
        .sendNote(_noteController.text);
    _noteController.clear();
    FocusScope.of(context).unfocus();
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تم إرسال ملاحظتك بنجاح.')));
  }

  @override
  Widget build(BuildContext context) {
    final isSending = ref.watch(noteControllerProvider);

    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 2,
            blurRadius: 5,
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _noteController,
              decoration: const InputDecoration(
                hintText: 'اكتب ملاحظتك هنا...',
                border: InputBorder.none,
              ),
              textCapitalization: TextCapitalization.sentences,
            ),
          ),
          isSending
              ? const Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.0),
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(),
                ),
              )
              : IconButton(
                icon: const Icon(Icons.send),
                onPressed: _sendNote,
                color: Theme.of(context).primaryColor,
              ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }
}
