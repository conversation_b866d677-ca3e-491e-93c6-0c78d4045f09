import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/teacher_model.dart';
import 'package:school_management_system/services/teacher_service.dart';
import 'package:school_management_system/providers/services_provider.dart';

// Provider لخدمة المعلمين
final teacherServiceProvider = Provider<TeacherService>((ref) {
  return TeacherService();
});

// StreamProvider لجلب قائمة المعلمين
final teachersStreamProvider = StreamProvider.autoDispose<List<TeacherModel>>((
  ref,
) {
  return ref.watch(teacherServiceProvider).getTeachers();
});

//======================================================================
// Providers جديدة للبيانات الحقيقية للمعلمين
//======================================================================

/// Provider لجلب إحصائيات المعلم الحقيقية
///
/// يستبدل البيانات الوهمية في TeacherHomePage
/// بإحصائيات حقيقية من قاعدة البيانات:
/// - عدد الامتحانات، الدرجات المعلقة، الامتحانات القادمة
/// - عدد المناهج المكتملة، عدد الطلاب المُدرَّسين
final teacherRealStatsProvider = FutureProvider.autoDispose
    .family<Map<String, dynamic>, String>((ref, teacherId) {
      final firebaseService = ref.watch(firebaseServiceProvider);
      return firebaseService.getTeacherRealStats(teacherId);
    });
