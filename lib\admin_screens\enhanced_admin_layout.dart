import 'package:flutter/material.dart';
import 'package:school_management_system/shared/app_theme.dart';
import 'package:school_management_system/admin_screens/admin_navigation_system.dart';
import 'package:school_management_system/widgets/enhanced_widgets.dart';
import 'package:school_management_system/services/firebase_service.dart';

/// لوحة تحكم الإدارة المحسنة مع التنقل الهرمي
/// 
/// تطبق التصميم الموحد الجديد مع تنظيم منطقي للشاشات
/// وتحسينات في تجربة المستخدم للإدارة المدرسية
class EnhancedAdminLayout extends StatefulWidget {
  /// قائمة الصفحات (نفس الترتيب من AdminMainLayout)
  final List<Widget> pages;

  const EnhancedAdminLayout({
    super.key,
    required this.pages,
  });

  @override
  State<EnhancedAdminLayout> createState() => _EnhancedAdminLayoutState();
}

class _EnhancedAdminLayoutState extends State<EnhancedAdminLayout> {
  /// الفئة المحددة حالياً
  AdminSection _selectedSection = AdminSection.dashboard;
  
  /// فهرس الصفحة المحددة حالياً
  int _selectedPageIndex = 0;
  
  /// حالة توسيع القائمة الجانبية
  bool _isExpanded = true;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 1200;
        
        // في الشاشات الصغيرة، استخدم Drawer
        if (isSmallScreen) {
          return _buildSmallScreenLayout();
        }
        
        // في الشاشات الكبيرة، استخدم التخطيط الجانبي
        return _buildLargeScreenLayout();
      },
    );
  }

  /// بناء التخطيط للشاشات الصغيرة (مع Drawer)
  Widget _buildSmallScreenLayout() {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      drawer: _buildNavigationDrawer(),
      body: _buildPageContent(),
    );
  }

  /// بناء التخطيط للشاشات الكبيرة (مع قائمة جانبية)
  Widget _buildLargeScreenLayout() {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Row(
        children: [
          // القائمة الجانبية
          _buildSideNavigation(),
          
          // خط فاصل
          const VerticalDivider(
            width: 1,
            thickness: 1,
            color: AppColors.border,
          ),
          
          // المحتوى الرئيسي
          Expanded(
            child: Column(
              children: [
                _buildAppBar(),
                Expanded(child: _buildPageContent()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(_getCurrentPageTitle()),
      centerTitle: false,
      backgroundColor: AppColors.surface,
      foregroundColor: AppColors.textPrimary,
      elevation: 1,
      shadowColor: AppColors.border,
      actions: [
        // زر الإشعارات
        IconButton(
          icon: const Icon(Icons.notifications_outlined),
          tooltip: 'الإشعارات',
          onPressed: () {
            // TODO: فتح صفحة الإشعارات
          },
        ),
        
        // قائمة المستخدم
        PopupMenuButton<String>(
          icon: const CircleAvatar(
            backgroundColor: AppColors.primary,
            child: Icon(
              Icons.person,
              color: AppColors.textOnPrimary,
            ),
          ),
          tooltip: 'قائمة المستخدم',
          onSelected: (value) {
            switch (value) {
              case 'profile':
                // TODO: فتح الملف الشخصي
                break;
              case 'settings':
                // TODO: فتح الإعدادات
                break;
              case 'logout':
                _handleLogout();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'profile',
              child: ListTile(
                leading: Icon(Icons.person_outline),
                title: Text('الملف الشخصي'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: ListTile(
                leading: Icon(Icons.settings_outlined),
                title: Text('الإعدادات'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: 'logout',
              child: ListTile(
                leading: Icon(Icons.logout, color: AppColors.error),
                title: Text('تسجيل الخروج', style: TextStyle(color: AppColors.error)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        
        const SizedBox(width: AppSpacing.sm),
      ],
    );
  }

  /// بناء القائمة الجانبية للشاشات الكبيرة
  Widget _buildSideNavigation() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: _isExpanded ? 280 : 80,
      child: Container(
        color: AppColors.surface,
        child: Column(
          children: [
            // رأس القائمة
            _buildNavigationHeader(),
            
            // زر التوسيع/الطي
            _buildExpandButton(),
            
            // قائمة الفئات
            Expanded(
              child: _buildSectionsList(),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء رأس القائمة الجانبية
  Widget _buildNavigationHeader() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppColors.border),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppSpacing.sm),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppBorderRadius.medium),
            ),
            child: const Icon(
              Icons.school,
              color: AppColors.primary,
              size: 32,
            ),
          ),
          if (_isExpanded) ...[
            const SizedBox(width: AppSpacing.md),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'لوحة تحكم الإدارة',
                    style: TextStyle(
                      fontSize: AppTextSizes.titleLarge,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    'نظام إدارة المدرسة',
                    style: TextStyle(
                      fontSize: AppTextSizes.bodySmall,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء زر التوسيع/الطي
  Widget _buildExpandButton() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),
      child: Row(
        children: [
          IconButton(
            icon: Icon(
              _isExpanded ? Icons.menu_open : Icons.menu,
              color: AppColors.textSecondary,
            ),
            tooltip: _isExpanded ? 'طي القائمة' : 'توسيع القائمة',
            onPressed: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
          ),
          if (_isExpanded) ...[
            const SizedBox(width: AppSpacing.sm),
            const Text(
              'القائمة الرئيسية',
              style: TextStyle(
                fontSize: AppTextSizes.labelMedium,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء قائمة الفئات
  Widget _buildSectionsList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
      itemCount: AdminSection.values.length,
      itemBuilder: (context, index) {
        final section = AdminSection.values[index];
        final items = AdminNavigationManager.getItemsBySection(section);
        
        if (items.isEmpty) return const SizedBox.shrink();
        
        return _buildSectionGroup(section, items);
      },
    );
  }

  /// بناء مجموعة فئة واحدة
  Widget _buildSectionGroup(AdminSection section, List<AdminNavigationItem> items) {
    final isSelected = _selectedSection == section;
    final sectionColor = AdminNavigationManager.getSectionColor(section);
    
    return Column(
      children: [
        // عنوان الفئة
        if (_isExpanded)
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpacing.lg,
              vertical: AppSpacing.sm,
            ),
            child: Row(
              children: [
                Icon(
                  AdminNavigationManager.getSectionIcon(section),
                  size: 16,
                  color: sectionColor,
                ),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  AdminNavigationManager.getSectionTitle(section),
                  style: TextStyle(
                    fontSize: AppTextSizes.labelMedium,
                    fontWeight: FontWeight.w600,
                    color: sectionColor,
                  ),
                ),
              ],
            ),
          ),
        
        // عناصر الفئة
        ...items.map((item) => _buildNavigationItem(item)),
        
        // فاصل بين الفئات
        if (_isExpanded && section != AdminSection.values.last)
          const Divider(
            height: AppSpacing.lg,
            indent: AppSpacing.lg,
            endIndent: AppSpacing.lg,
          ),
      ],
    );
  }

  /// بناء عنصر تنقل واحد
  Widget _buildNavigationItem(AdminNavigationItem item) {
    final isSelected = _selectedPageIndex == item.pageIndex;
    final itemColor = item.color ?? AdminNavigationManager.getSectionColor(item.section);
    
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: _isExpanded ? AppSpacing.sm : AppSpacing.xs,
        vertical: AppSpacing.xs,
      ),
      child: Material(
        color: isSelected 
            ? itemColor.withValues(alpha: 0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(AppBorderRadius.medium),
        child: InkWell(
          onTap: () => _selectPage(item),
          borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: _isExpanded ? AppSpacing.md : AppSpacing.sm,
              vertical: AppSpacing.md,
            ),
            child: Row(
              children: [
                Icon(
                  isSelected ? item.selectedIcon : item.icon,
                  color: isSelected ? itemColor : AppColors.textSecondary,
                  size: 20,
                ),
                if (_isExpanded) ...[
                  const SizedBox(width: AppSpacing.md),
                  Expanded(
                    child: Text(
                      item.title,
                      style: TextStyle(
                        fontSize: AppTextSizes.bodyMedium,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        color: isSelected ? itemColor : AppColors.textPrimary,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء القائمة المنسدلة للشاشات الصغيرة
  Widget _buildNavigationDrawer() {
    return Drawer(
      backgroundColor: AppColors.surface,
      child: Column(
        children: [
          // رأس القائمة
          DrawerHeader(
            decoration: const BoxDecoration(
              color: AppColors.primary,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(
                  Icons.school,
                  color: AppColors.textOnPrimary,
                  size: 48,
                ),
                const SizedBox(height: AppSpacing.sm),
                const Text(
                  'لوحة تحكم الإدارة',
                  style: TextStyle(
                    color: AppColors.textOnPrimary,
                    fontSize: AppTextSizes.titleLarge,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'نظام إدارة المدرسة',
                  style: TextStyle(
                    color: AppColors.textOnPrimary.withValues(alpha: 0.8),
                    fontSize: AppTextSizes.bodyMedium,
                  ),
                ),
              ],
            ),
          ),
          
          // قائمة العناصر
          Expanded(
            child: ListView.builder(
              itemCount: AdminSection.values.length,
              itemBuilder: (context, index) {
                final section = AdminSection.values[index];
                final items = AdminNavigationManager.getItemsBySection(section);
                
                if (items.isEmpty) return const SizedBox.shrink();
                
                return ExpansionTile(
                  leading: Icon(AdminNavigationManager.getSectionIcon(section)),
                  title: Text(AdminNavigationManager.getSectionTitle(section)),
                  children: items.map((item) => ListTile(
                    leading: Icon(item.icon),
                    title: Text(item.title),
                    selected: _selectedPageIndex == item.pageIndex,
                    onTap: () {
                      _selectPage(item);
                      Navigator.pop(context); // إغلاق القائمة
                    },
                  )).toList(),
                );
              },
            ),
          ),
          
          // زر تسجيل الخروج
          Container(
            padding: const EdgeInsets.all(AppSpacing.md),
            decoration: const BoxDecoration(
              border: Border(top: BorderSide(color: AppColors.border)),
            ),
            child: ListTile(
              leading: const Icon(Icons.logout, color: AppColors.error),
              title: const Text('تسجيل الخروج', style: TextStyle(color: AppColors.error)),
              onTap: _handleLogout,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى الصفحة
  Widget _buildPageContent() {
    if (_selectedPageIndex >= 0 && _selectedPageIndex < widget.pages.length) {
      return widget.pages[_selectedPageIndex];
    }
    
    return const Center(
      child: Text(
        'الصفحة غير موجودة',
        style: TextStyle(
          fontSize: AppTextSizes.titleLarge,
          color: AppColors.textSecondary,
        ),
      ),
    );
  }

  /// اختيار صفحة جديدة
  void _selectPage(AdminNavigationItem item) {
    setState(() {
      _selectedSection = item.section;
      _selectedPageIndex = item.pageIndex;
    });
  }

  /// الحصول على عنوان الصفحة الحالية
  String _getCurrentPageTitle() {
    final item = AdminNavigationManager.allItems
        .where((item) => item.pageIndex == _selectedPageIndex)
        .firstOrNull;
    
    return item?.title ?? 'لوحة تحكم الإدارة';
  }

  /// معالجة تسجيل الخروج
  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          EnhancedButton(
            text: 'تسجيل الخروج',
            color: AppColors.error,
            onPressed: () {
              Navigator.pop(context);
              FirebaseService().signOut();
            },
          ),
        ],
      ),
    );
  }
}
