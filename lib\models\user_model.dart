/// نموذج البيانات الأساسي للمستخدم - محدث للمدارس اليمنية
/// يحتوي على الخصائص المشتركة بين جميع أنواع المستخدمين مع الحقول المطلوبة للمدارس اليمنية
class UserModel {
  // ===== الحقول الأساسية المطلوبة =====
  final String id; // المعرّف الفريد للمستخدم (من Firebase Auth)
  final String name; // الاسم الكامل
  final String email; // البريد الإلكتروني
  final String role; // الدور (مثال: 'student', 'admin', 'teacher', 'staff')

  // ===== حقول إضافية خاصة بالموظفين =====
  final String? jobTitle; // المسمى الوظيفي (مثال: معلم رياضيات)
  final String? bio; // نبذة تعريفية
  final String? profileImageUrl; // رابط الصورة الشخصية

  // ===== الحقول الشخصية الجديدة المطلوبة للمدارس اليمنية =====
  final String? phoneNumber; // رقم الهاتف (مطلوب)
  final DateTime? dateOfBirth; // تاريخ الميلاد (مطلوب)
  final String? address; // العنوان (مطلوب)
  final String? gender; // الجنس (مطلوب)
  final String nationality; // الجنسية (مطلوب - افتراضي يمني)

  // ===== الحقول الاختيارية =====
  final String? nationalId; // الرقم الوطني (اختياري)
  final String? governorate; // المحافظة (اختيارية)
  final String? bloodType; // فصيلة الدم (اختيارية)
  final String? healthCondition; // الحالة الصحية (اختيارية)
  final String? notes; // ملاحظات (اختيارية)

  UserModel({
    // ===== الحقول الأساسية المطلوبة =====
    required this.id,
    required this.name,
    required this.email,
    required this.role,

    // ===== حقول إضافية خاصة بالموظفين =====
    this.jobTitle,
    this.bio,
    this.profileImageUrl,

    // ===== الحقول الشخصية الجديدة المطلوبة للمدارس اليمنية =====
    this.phoneNumber,
    this.dateOfBirth,
    this.address,
    this.gender,
    this.nationality = 'يمني', // افتراضي يمني
    // ===== الحقول الاختيارية =====
    this.nationalId,
    this.governorate,
    this.bloodType,
    this.healthCondition,
    this.notes,
  });

  /// دالة محدثة لتحويل بيانات المستخدم من Firestore (Map) إلى كائن UserModel
  /// تدعم جميع الحقول الجديدة المطلوبة للمدارس اليمنية
  factory UserModel.fromMap(Map<String, dynamic> data, String documentId) {
    return UserModel(
      // ===== الحقول الأساسية =====
      id: documentId,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      role: data['role'] ?? '',

      // ===== حقول إضافية خاصة بالموظفين =====
      jobTitle: data['jobTitle'],
      bio: data['bio'],
      profileImageUrl: data['profileImageUrl'],

      // ===== الحقول الشخصية الجديدة =====
      phoneNumber: data['phoneNumber'],
      dateOfBirth:
          data['dateOfBirth'] != null
              ? DateTime.fromMillisecondsSinceEpoch(data['dateOfBirth'])
              : null,
      address: data['address'],
      gender: data['gender'],
      nationality: data['nationality'] ?? 'يمني',

      // ===== الحقول الاختيارية =====
      nationalId: data['nationalId'],
      governorate: data['governorate'],
      bloodType: data['bloodType'],
      healthCondition: data['healthCondition'],
      notes: data['notes'],
    );
  }

  /// دالة محدثة لتحويل كائن UserModel إلى Map لتخزينه في Firestore
  /// تتضمن جميع الحقول الجديدة المطلوبة للمدارس اليمنية
  Map<String, dynamic> toMap() {
    return {
      // ===== الحقول الأساسية =====
      'name': name,
      'email': email,
      'role': role,

      // ===== حقول إضافية خاصة بالموظفين =====
      'jobTitle': jobTitle,
      'bio': bio,
      'profileImageUrl': profileImageUrl,

      // ===== الحقول الشخصية الجديدة =====
      'phoneNumber': phoneNumber,
      'dateOfBirth': dateOfBirth?.millisecondsSinceEpoch,
      'address': address,
      'gender': gender,
      'nationality': nationality,

      // ===== الحقول الاختيارية =====
      'nationalId': nationalId,
      'governorate': governorate,
      'bloodType': bloodType,
      'healthCondition': healthCondition,
      'notes': notes,
    };
  }
}
