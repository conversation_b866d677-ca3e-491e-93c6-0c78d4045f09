import 'package:flutter/material.dart';
import 'package:school_management_system/shared/app_theme.dart';

/// بطاقة عرض مخصصة وقابلة لإعادة الاستخدام (محدثة)
///
/// توفر تصميمًا موحدًا لعرض المحتوى في التطبيق باستخدام النظام الجديد
/// للألوان والمسافات والحدود
///
/// ملاحظة: يُنصح باستخدام EnhancedCard من enhanced_widgets.dart
/// للمشاريع الجديدة حيث توفر مرونة أكبر في التخصيص
class CustomCard extends StatelessWidget {
  /// المحتوى الذي سيوضع داخل البطاقة
  final Widget child;

  /// دالة تُستدعى عند الضغط على البطاقة (اختياري)
  final VoidCallback? onTap;

  /// لون خلفية البطاقة (اختياري - افتراضي: سطح أبيض)
  final Color? color;

  const CustomCard({super.key, required this.child, this.onTap, this.color});

  @override
  Widget build(BuildContext context) {
    return Card(
      // تطبيق اللون الموحد أو اللون المخصص
      color: color ?? AppColors.surface,

      // تطبيق شكل البطاقة الموحد
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppBorderRadius.large),
      ),

      // تطبيق الظل الموحد
      elevation: AppElevation.medium,

      // تطبيق الهامش الموحد
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpacing.md,
        vertical: AppSpacing.sm,
      ),

      // لون الظل الموحد
      shadowColor: AppColors.textSecondary.withValues(alpha: 0.2),

      // استخدام InkWell لجعل البطاقة قابلة للضغط مع تأثير بصري
      child: InkWell(
        borderRadius: BorderRadius.circular(AppBorderRadius.large),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.cardPadding),
          child: child,
        ),
      ),
    );
  }
}
