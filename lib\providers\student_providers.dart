import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

//======================================================================
// Providers for Admin Student Management Screen
//======================================================================

// Note: studentServiceProvider is removed as FirebaseService is now used directly.

final studentsStreamProvider = StreamProvider.autoDispose<List<StudentModel>>((
  ref,
) {
  // This now correctly points to the stream from the unified service.
  return ref.watch(firebaseServiceProvider).getAllStudentsStream();
});

final studentSearchQueryProvider = StateProvider.autoDispose<String>(
  (ref) => '',
);

final filteredStudentsProvider = Provider.autoDispose<List<StudentModel>>((
  ref,
) {
  final students = ref.watch(studentsStreamProvider).asData?.value ?? [];
  final searchQuery = ref.watch(studentSearchQueryProvider).toLowerCase();

  if (searchQuery.isEmpty) {
    return students;
  }

  return students.where((student) {
    // البحث في الحقول الأساسية
    final nameMatches = student.name.toLowerCase().contains(searchQuery);
    final idMatches = student.id.toLowerCase().contains(searchQuery);
    final emailMatches = student.email.toLowerCase().contains(searchQuery);
    final studentNumberMatches = student.studentNumber.toLowerCase().contains(
      searchQuery,
    );

    // البحث في الحقول الجديدة
    final guardianNameMatches = student.guardianName.toLowerCase().contains(
      searchQuery,
    );
    final guardianPhoneMatches = (student.guardianPhone ?? '')
        .toLowerCase()
        .contains(searchQuery);
    final governorateMatches = student.governorate.toLowerCase().contains(
      searchQuery,
    );
    final nationalityMatches = student.nationality.toLowerCase().contains(
      searchQuery,
    );
    final nationalIdMatches = (student.nationalId ?? '').toLowerCase().contains(
      searchQuery,
    );
    final phoneMatches = (student.phoneNumber ?? '').toLowerCase().contains(
      searchQuery,
    );
    final addressMatches = (student.address ?? '').toLowerCase().contains(
      searchQuery,
    );
    final bloodTypeMatches = (student.bloodType ?? '').toLowerCase().contains(
      searchQuery,
    );
    final healthConditionMatches = (student.healthCondition ?? '')
        .toLowerCase()
        .contains(searchQuery);

    return nameMatches ||
        idMatches ||
        emailMatches ||
        studentNumberMatches ||
        guardianNameMatches ||
        guardianPhoneMatches ||
        governorateMatches ||
        nationalityMatches ||
        nationalIdMatches ||
        phoneMatches ||
        addressMatches ||
        bloodTypeMatches ||
        healthConditionMatches;
  }).toList();
});

//======================================================================
// Providers for Student Mobile Screens
//======================================================================

// Provider to fetch the main dashboard data for a student
final studentDashboardProvider = FutureProvider.autoDispose
    .family<Map<String, dynamic>, String>((ref, studentId) {
      final firebaseService = ref.watch(firebaseServiceProvider);
      return firebaseService.getStudentDashboardData(studentId);
    });

// Provider to fetch a student's grades
final studentGradesProvider = StreamProvider.autoDispose
    .family<List<Map<String, dynamic>>, String>((ref, studentId) {
      final firebaseService = ref.watch(firebaseServiceProvider);
      return firebaseService.getStudentGrades(studentId);
    });

/// Provider لجلب بيانات طالب معين باستخدام معرفه
final currentStudentProvider = StreamProvider.autoDispose
    .family<StudentModel?, String>((ref, studentId) {
      final firebaseService = ref.watch(firebaseServiceProvider);
      return firebaseService.getStudentById(studentId);
    });

//======================================================================
// Providers جديدة للبيانات الحقيقية (بدلاً من البيانات الوهمية)
//======================================================================

/// Provider لجلب درجات الطالب التفصيلية الحقيقية
///
/// يستبدل البيانات الوهمية في StudentResultsScreen
/// بدرجات حقيقية من قاعدة البيانات مع جميع التفاصيل
final studentDetailedGradesProvider = FutureProvider.autoDispose
    .family<List<Map<String, dynamic>>, String>((ref, studentId) {
      final firebaseService = ref.watch(firebaseServiceProvider);
      return firebaseService.getStudentDetailedGrades(studentId);
    });

/// Provider لجلب إحصائيات أداء الطالب الحقيقية
///
/// يحسب ويجلب جميع الإحصائيات من البيانات الفعلية:
/// - المعدل العام، عدد الامتحانات المجتازة/الراسبة
/// - ترتيب الطالب، نسبة التحسن، أفضل/أسوأ المواد
final studentPerformanceStatsProvider = FutureProvider.autoDispose
    .family<Map<String, dynamic>, String>((ref, studentId) {
      final firebaseService = ref.watch(firebaseServiceProvider);
      return firebaseService.getStudentPerformanceStats(studentId);
    });
