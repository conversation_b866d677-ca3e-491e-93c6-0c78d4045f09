import 'package:flutter/material.dart';
import 'package:school_management_system/services/firebase_service.dart';
import 'package:school_management_system/utils/async_error_handler.dart';
import 'package:school_management_system/utils/error_handler.dart';

/// شاشة تسجيل الدخول للمعلمين
///
/// هذه الشاشة مخصصة لتسجيل دخول المعلمين إلى لوحة التحكم
/// الخاصة بهم مع تصميم مميز وألوان خاصة بالمعلمين
///
/// الوظائف:
/// - تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
/// - التحقق من صحة البيانات
/// - رسائل خطأ واضحة
/// - تصميم متجاوب
/// - ألوان مميزة للمعلمين (أخضر)
class TeacherLoginScreen extends StatefulWidget {
  const TeacherLoginScreen({super.key});

  @override
  State<TeacherLoginScreen> createState() => _TeacherLoginScreenState();
}

class _TeacherLoginScreenState extends State<TeacherLoginScreen> {
  // ===================================================================
  // متحكمات النماذج والحالة
  // ===================================================================

  /// مفتاح النموذج للتحقق من صحة البيانات
  final _formKey = GlobalKey<FormState>();

  /// متحكمات حقول الإدخال
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  /// حالة التحميل
  bool _isLoading = false;

  /// إظهار/إخفاء كلمة المرور
  bool _obscurePassword = true;

  /// خدمة Firebase
  final FirebaseService _firebaseService = FirebaseService();

  @override
  void dispose() {
    // تنظيف المتحكمات عند إغلاق الشاشة
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.green[50],
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 400),
            child: Card(
              elevation: 8.0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16.0),
              ),
              child: Padding(
                padding: const EdgeInsets.all(32.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // شعار ورأس الصفحة
                      _buildHeader(),
                      const SizedBox(height: 32),

                      // حقل البريد الإلكتروني
                      _buildEmailField(),
                      const SizedBox(height: 16),

                      // حقل كلمة المرور
                      _buildPasswordField(),
                      const SizedBox(height: 24),

                      // زر تسجيل الدخول
                      _buildLoginButton(),
                      const SizedBox(height: 16),

                      // رابط نسيان كلمة المرور
                      _buildForgotPasswordLink(),
                      const SizedBox(height: 24),

                      // معلومات المساعدة
                      _buildHelpInfo(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء رأس الصفحة والشعار
  Widget _buildHeader() {
    return Column(
      children: [
        // أيقونة المعلم
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.green[100],
            shape: BoxShape.circle,
          ),
          child: Icon(Icons.school, size: 48, color: Colors.green[800]),
        ),
        const SizedBox(height: 16),

        // عنوان الصفحة
        Text(
          'لوحة تحكم المعلم',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.green[800],
          ),
        ),
        const SizedBox(height: 8),

        // وصف الصفحة
        Text(
          'سجل دخولك للوصول إلى أدوات التدريس',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 14, color: Colors.grey[600]),
        ),
      ],
    );
  }

  /// بناء حقل البريد الإلكتروني
  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      decoration: InputDecoration(
        labelText: 'البريد الإلكتروني',
        hintText: 'أدخل بريدك الإلكتروني',
        prefixIcon: Icon(Icons.email_outlined, color: Colors.green[600]),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.green[600]!, width: 2),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال البريد الإلكتروني';
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return 'يرجى إدخال بريد إلكتروني صحيح';
        }
        return null;
      },
    );
  }

  /// بناء حقل كلمة المرور
  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      textInputAction: TextInputAction.done,
      onFieldSubmitted: (_) => _login(),
      decoration: InputDecoration(
        labelText: 'كلمة المرور',
        hintText: 'أدخل كلمة المرور',
        prefixIcon: Icon(Icons.lock_outlined, color: Colors.green[600]),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword
                ? Icons.visibility_outlined
                : Icons.visibility_off_outlined,
            color: Colors.green[600],
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.green[600]!, width: 2),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال كلمة المرور';
        }
        if (value.length < 6) {
          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }
        return null;
      },
    );
  }

  /// بناء زر تسجيل الدخول
  Widget _buildLoginButton() {
    return ElevatedButton(
      onPressed: _isLoading ? null : _login,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.green[600],
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 2,
      ),
      child:
          _isLoading
              ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
              : const Text(
                'تسجيل الدخول',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
    );
  }

  /// بناء رابط نسيان كلمة المرور
  Widget _buildForgotPasswordLink() {
    return TextButton(
      onPressed: _isLoading ? null : _showForgotPasswordDialog,
      child: Text(
        'نسيت كلمة المرور؟',
        style: TextStyle(color: Colors.green[600], fontWeight: FontWeight.w600),
      ),
    );
  }

  /// بناء معلومات المساعدة
  Widget _buildHelpInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.green[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'معلومات مهمة',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.green[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'هذه الصفحة مخصصة للمعلمين فقط. إذا لم يكن لديك حساب، تواصل مع الإدارة.',
            style: TextStyle(fontSize: 12, color: Colors.green[700]),
          ),
        ],
      ),
    );
  }

  /// تسجيل الدخول مع معالجة أخطاء محسنة
  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    // استخدام نظام معالجة الأخطاء المحسن
    final user = await AsyncErrorHandler.executeAdvanced<dynamic>(
      operation:
          () => _firebaseService.signInWithEmailAndPassword(
            _emailController.text.trim(),
            _passwordController.text.trim(),
          ),
      context: context,
      maxRetries: 2,
      timeout: const Duration(seconds: 15),
      loadingMessage: 'جاري تسجيل الدخول...',
      successMessage: 'تم تسجيل الدخول بنجاح',
      showSuccessMessage: true,
      onSuccess: () {
        // سيتم التوجيه تلقائياً بواسطة AuthGate
      },
      onError: () {
        // إعادة تعيين حالة التحميل
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      },
    );

    // التحقق من نجاح تسجيل الدخول
    if (user == null && mounted) {
      setState(() {
        _isLoading = false;
      });

      // عرض رسالة خطأ مخصصة إذا فشل تسجيل الدخول بدون استثناء
      ErrorHandler.showErrorSnackBar(
        context,
        Exception('فشل تسجيل الدخول. تحقق من البريد الإلكتروني وكلمة المرور'),
        onRetry: _login,
      );
    } else if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// عرض حوار نسيان كلمة المرور
  void _showForgotPasswordDialog() {
    final emailController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إعادة تعيين كلمة المرور'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('أدخل بريدك الإلكتروني لإرسال رابط إعادة التعيين:'),
                const SizedBox(height: 16),
                TextField(
                  controller: emailController,
                  keyboardType: TextInputType.emailAddress,
                  decoration: const InputDecoration(
                    labelText: 'البريد الإلكتروني',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (emailController.text.isNotEmpty) {
                    // استخدام نظام معالجة الأخطاء المحسن
                    await AsyncErrorHandler.execute<void>(
                      operation:
                          () => _firebaseService.sendPasswordResetEmail(
                            emailController.text.trim(),
                          ),
                      context: context,
                      successMessage:
                          'تم إرسال رابط إعادة التعيين إلى بريدك الإلكتروني',
                      showSuccessMessage: true,
                      onSuccess: () {
                        if (context.mounted) {
                          Navigator.pop(context);
                        }
                      },
                      onError: () {
                        if (context.mounted) {
                          Navigator.pop(context);
                        }
                      },
                    );
                  }
                },
                child: const Text('إرسال'),
              ),
            ],
          ),
    );
  }
}
