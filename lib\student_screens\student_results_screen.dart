import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/exam_model.dart';
import 'package:school_management_system/models/student_grade_model.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/providers/student_providers.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// شاشة عرض النتائج للطلاب
///
/// هذه الشاشة تعرض للطالب جميع نتائجه وإنجازاته الأكاديمية بشكل شامل ومفصل:
///
/// الوظائف الرئيسية:
/// - عرض الدرجات التفصيلية لجميع الامتحانات
/// - إحصائيات شاملة عن الأداء الأكاديمي
/// - مقارنات مع المتوسط العام للصف
/// - تتبع التحسن والتطور عبر الزمن
/// - عرض نقاط القوة والضعف
/// - توصيات للتحسين والتطوير
/// - إمكانية تصدير التقارير والشهادات
/// - عرض التقديرات والمراتب
///
/// أقسام الشاشة:
/// 1. الملخص العام - نظرة سريعة على الأداء
/// 2. الدرجات التفصيلية - جميع النتائج مع التفاصيل
/// 3. الإحصائيات والمقارنات - تحليل الأداء
/// 4. التقارير والشهادات - وثائق رسمية
///
/// التصميم والواجهة:
/// - ألوان محفزة ومشجعة للطلاب
/// - مخططات بيانية واضحة وجذابة
/// - تنظيم هرمي للمعلومات
/// - مؤشرات بصرية للإنجازات
/// - تصميم متجاوب لجميع الأحجام
/// - تفاعل سلس ومريح للاستخدام
class StudentResultsScreen extends ConsumerStatefulWidget {
  /// معرف الطالب الذي تعرض نتائجه
  /// يستخدم لتحميل جميع البيانات الخاصة بالطالب
  final String studentId;

  const StudentResultsScreen({super.key, required this.studentId});

  @override
  ConsumerState<StudentResultsScreen> createState() =>
      _StudentResultsScreenState();
}

class _StudentResultsScreenState extends ConsumerState<StudentResultsScreen>
    with TickerProviderStateMixin {
  // ===================================================================
  // متحكمات الواجهة والرسوم المتحركة
  // ===================================================================

  /// متحكم التبويبات الرئيسية في الشاشة
  /// يدير التنقل بين: الملخص، الدرجات، الإحصائيات، التقارير
  late TabController _tabController;

  /// متحكم الرسوم المتحركة للمخططات والمؤشرات
  /// يستخدم لإظهار البيانات بشكل متحرك وجذاب
  late AnimationController _chartAnimationController;

  /// الرسم المتحرك لظهور المخططات
  late Animation<double> _chartAnimation;

  /// متحكم البحث في النتائج
  /// يسمح للطالب بالبحث عن امتحان أو مادة معينة
  final TextEditingController _searchController = TextEditingController();

  // ===================================================================
  // متغيرات البيانات الرئيسية
  // ===================================================================

  /// قائمة جميع درجات الطالب
  /// تحتوي على درجات جميع الامتحانات مع التفاصيل
  List<StudentGradeModel> _studentGrades = [];

  /// قائمة الامتحانات التي شارك فيها الطالب
  /// مرتبطة بالدرجات لعرض معلومات شاملة
  List<ExamModel> _studentExams = [];

  /// خريطة الدرجات مجمعة حسب المادة
  /// المفتاح: معرف المادة، القيمة: قائمة الدرجات
  Map<String, List<StudentGradeModel>> _gradesBySubject = {};

  /// خريطة الدرجات مجمعة حسب نوع الامتحان
  /// المفتاح: نوع الامتحان، القيمة: قائمة الدرجات
  Map<ExamType, List<StudentGradeModel>> _gradesByExamType = {};

  /// خريطة متوسطات الصف لكل امتحان
  /// المفتاح: معرف الامتحان، القيمة: متوسط الصف
  Map<String, double> _classAverages = {};

  // ===================================================================
  // متغيرات الإحصائيات والتحليل
  // ===================================================================

  /// المعدل العام للطالب (GPA)
  double _overallGPA = 0.0;

  /// المعدل الفصلي الحالي
  double _currentSemesterGPA = 0.0;

  /// أعلى درجة حصل عليها الطالب
  double _highestGrade = 0.0;

  /// أقل درجة حصل عليها الطالب
  double _lowestGrade = 0.0;

  /// متوسط درجات الطالب
  double _averageGrade = 0.0;

  /// عدد الامتحانات المجتازة
  int _passedExams = 0;

  /// عدد الامتحانات الراسبة
  int _failedExams = 0;

  /// إجمالي عدد الامتحانات
  int _totalExams = 0;

  /// ترتيب الطالب في الصف
  int _classRank = 0;

  /// إجمالي عدد الطلاب في الصف
  int _totalStudentsInClass = 0;

  /// نسبة التحسن مقارنة بالفصل السابق
  double _improvementPercentage = 0.0;

  /// قائمة المواد التي يتفوق فيها الطالب
  List<String> _strongSubjects = [];

  /// قائمة المواد التي تحتاج تحسين
  List<String> _weakSubjects = [];

  /// المعدل العام للطالب (من الإحصائيات الحقيقية)
  double _overallAverage = 0.0;

  /// أفضل مادة للطالب (من الإحصائيات الحقيقية)
  String? _bestSubject;

  /// أسوأ مادة للطالب (من الإحصائيات الحقيقية)
  String? _worstSubject;

  // ===================================================================
  // متغيرات حالة التحميل والتفاعل
  // ===================================================================

  /// حالة تحميل البيانات الأولية
  bool _isLoading = false;

  /// حالة تحديث البيانات
  bool _isRefreshing = false;

  /// حالة تصدير التقرير
  bool _isExporting = false;

  /// نص البحث الحالي
  String _searchQuery = '';

  /// فلتر الفترة الزمنية (الكل، الفصل الحالي، الشهر الماضي)
  TimeFilter _timeFilter = TimeFilter.all;

  /// فلتر نوع الامتحان (الكل، نهائي، نصفي، شهري، واجب)
  ExamType? _examTypeFilter;

  /// فلتر المادة (الكل، مادة محددة)
  String? _subjectFilter;

  /// ترتيب النتائج (التاريخ، الدرجة، المادة)
  ResultSort _resultSort = ResultSort.date;

  /// اتجاه الترتيب (تصاعدي، تنازلي)
  bool _sortAscending = false;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم التبويبات مع 4 تبويبات رئيسية
    _tabController = TabController(length: 4, vsync: this);

    // إنشاء متحكم الرسوم المتحركة للمخططات
    _chartAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000), // مدة أطول للمخططات
      vsync: this,
    );

    // إنشاء الرسم المتحرك للمخططات مع منحنى مرن
    _chartAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _chartAnimationController,
        curve: Curves.elasticOut, // منحنى مرن وجذاب
      ),
    );

    // تحميل البيانات الأولية عند فتح الشاشة
    _loadStudentResults();

    // إضافة مستمع لتغييرات التبويبات
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    // تنظيف الموارد عند إغلاق الشاشة
    _tabController.dispose();
    _chartAnimationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع التبويبات والإجراءات
      appBar: AppBar(
        title: const Text(
          'نتائجي الدراسية',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.teal[800], // لون مميز لشاشة النتائج
        elevation: 2,

        // التبويبات السفلية في شريط التطبيق
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 13,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.dashboard, size: 20), text: 'الملخص'),
            Tab(icon: Icon(Icons.grade, size: 20), text: 'الدرجات'),
            Tab(icon: Icon(Icons.analytics, size: 20), text: 'الإحصائيات'),
            Tab(icon: Icon(Icons.description, size: 20), text: 'التقارير'),
          ],
        ),

        // أزرار الإجراءات في شريط التطبيق
        actions: [
          // زر البحث في النتائج
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () => _showSearchDialog(),
            tooltip: 'البحث في النتائج',
          ),

          // زر الفلاتر والترتيب
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: () => _showFilterDialog(),
            tooltip: 'فلترة وترتيب النتائج',
          ),

          // زر تصدير التقرير
          if (_isExporting)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.file_download, color: Colors.white),
              onPressed: () => _exportReport(),
              tooltip: 'تصدير التقرير',
            ),
        ],
      ),

      // محتوى التبويبات الرئيسية
      body: TabBarView(
        controller: _tabController,
        children: [
          // تبويب الملخص العام - نظرة سريعة على الأداء
          _buildSummaryTab(),

          // تبويب الدرجات التفصيلية - جميع النتائج
          _buildGradesTab(),

          // تبويب الإحصائيات - تحليل الأداء والمقارنات
          _buildStatisticsTab(),

          // تبويب التقارير - الشهادات والوثائق
          _buildReportsTab(),
        ],
      ),

      // شريط المعلومات السفلي يعرض إحصائيات سريعة
      bottomNavigationBar: _buildBottomStatsBar(),

      // زر عائم لمشاركة النتائج أو طباعتها
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showShareDialog(),
        backgroundColor: Colors.teal[600],
        icon: const Icon(Icons.share, color: Colors.white),
        label: const Text(
          'مشاركة النتائج',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  // ===================================================================
  // دوال تحميل البيانات والتحليل
  // ===================================================================

  /// تحميل نتائج الطالب من قاعدة البيانات
  ///
  /// هذه الدالة تقوم بتحميل جميع البيانات المطلوبة:
  /// - درجات الطالب في جميع الامتحانات
  /// - معلومات الامتحانات المرتبطة
  /// - متوسطات الصف للمقارنة
  /// - حساب الإحصائيات والتحليلات
  Future<void> _loadStudentResults() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل البيانات الحقيقية من Firebase باستخدام الـ Providers الجديدة

      // 1. جلب الدرجات التفصيلية للطالب من قاعدة البيانات
      final detailedGrades = await ref.read(
        studentDetailedGradesProvider(widget.studentId).future,
      );

      // 2. جلب إحصائيات الأداء الشاملة المحسوبة
      final performanceStats = await ref.read(
        studentPerformanceStatsProvider(widget.studentId).future,
      );

      // تحويل البيانات الحقيقية إلى نموذج StudentGradeModel
      _convertRealDataToGradeModels(detailedGrades);

      // استخدام الإحصائيات الحقيقية بدلاً من المحاكاة
      _applyRealPerformanceStats(performanceStats);

      // تجميع البيانات وحساب الإحصائيات
      _groupGradesBySubject();
      _groupGradesByExamType();
      _calculateStatistics();
      _analyzePerformance();

      // بدء الرسوم المتحركة للمخططات
      _chartAnimationController.forward();
    } catch (e) {
      // معالجة الأخطاء وعرض رسالة مناسبة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل النتائج: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _loadStudentResults(),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تحويل البيانات الحقيقية من Firebase إلى نموذج StudentGradeModel
  ///
  /// هذه الدالة تأخذ البيانات الخام من قاعدة البيانات وتحولها
  /// إلى نماذج يمكن استخدامها في الواجهة
  void _convertRealDataToGradeModels(
    List<Map<String, dynamic>> detailedGrades,
  ) {
    _studentGrades =
        detailedGrades.map((gradeData) {
          return StudentGradeModel(
            id: gradeData['id'] as String,
            studentId: gradeData['studentId'] as String,
            examId: gradeData['examId'] as String? ?? '',
            examName: gradeData['examName'] as String? ?? 'امتحان غير محدد',
            subjectId: gradeData['subjectId'] as String? ?? '',
            subjectName:
                gradeData['subjectName'] as String? ?? 'مادة غير محددة',
            teacherId: gradeData['teacherId'] as String? ?? '',
            teacherName: gradeData['teacherName'] as String? ?? 'معلم غير محدد',
            grade: (gradeData['grade'] as num).toDouble(),
            maxGrade: (gradeData['maxGrade'] as num?)?.toDouble() ?? 100.0,
            examDate:
                (gradeData['examDate'] as Timestamp?)?.toDate() ??
                DateTime.now(),
            examType: _parseExamType(
              gradeData['examType'] as String? ?? 'monthly',
            ),
            examDuration: gradeData['examDuration'] as int? ?? 60,
            isPublished: gradeData['isPublished'] as bool? ?? false,
            publishedAt:
                gradeData['publishedAt'] != null
                    ? (gradeData['publishedAt'] as Timestamp).toDate()
                    : null,
            teacherNotes: gradeData['teacherNotes'] as String? ?? '',
            performanceRating: gradeData['performanceRating'] as String?,
            classAverage:
                (gradeData['classAverage'] as num?)?.toDouble() ?? 0.0,
            highestInClass:
                (gradeData['highestInClass'] as num?)?.toDouble() ?? 0.0,
            lowestInClass:
                (gradeData['lowestInClass'] as num?)?.toDouble() ?? 0.0,
            rankInClass: gradeData['rankInClass'] as int? ?? 0,
            totalStudents: gradeData['totalStudents'] as int? ?? 0,
            semester: gradeData['semester'] as String? ?? 'الفصل الأول',
            academicYear: gradeData['academicYear'] as String? ?? '2024-2025',
            createdAt:
                (gradeData['createdAt'] as Timestamp?)?.toDate() ??
                DateTime.now(),
            updatedAt:
                (gradeData['updatedAt'] as Timestamp?)?.toDate() ??
                DateTime.now(),
          );
        }).toList();
  }

  /// تطبيق الإحصائيات الحقيقية من قاعدة البيانات
  ///
  /// هذه الدالة تستخدم الإحصائيات المحسوبة من Firebase
  /// بدلاً من البيانات الوهمية
  void _applyRealPerformanceStats(Map<String, dynamic> performanceStats) {
    _totalExams = performanceStats['totalExams'] as int? ?? 0;
    _passedExams = performanceStats['passedExams'] as int? ?? 0;
    _failedExams = performanceStats['failedExams'] as int? ?? 0;
    _overallAverage =
        (performanceStats['overallAverage'] as num?)?.toDouble() ?? 0.0;
    _classRank = performanceStats['classRank'] as int? ?? 0;
    _totalStudentsInClass =
        performanceStats['totalStudentsInClass'] as int? ?? 0;
    _improvementPercentage =
        (performanceStats['improvementPercentage'] as num?)?.toDouble() ?? 0.0;

    // معلومات أفضل وأسوأ المواد
    _bestSubject = performanceStats['bestSubject'] as String?;
    _worstSubject = performanceStats['worstSubject'] as String?;

    // ملاحظة: تجميع الدرجات سيتم في دوال منفصلة
    // باستخدام البيانات الموجودة في _studentGrades
    // لا نحتاج لتحويل الإحصائيات هنا
  }

  /// دالة مساعدة لتحويل نص نوع الامتحان إلى ExamType
  ///
  /// تحول النص المحفوظ في قاعدة البيانات إلى enum
  ExamType _parseExamType(String examTypeString) {
    switch (examTypeString.toLowerCase()) {
      case 'monthly':
        return ExamType.monthly;
      case 'midterm':
        return ExamType.midterm;
      case 'finalexam':
      case 'final':
        return ExamType.finalExam;
      case 'makeup':
        return ExamType.makeup;
      default:
        return ExamType.monthly; // القيمة الافتراضية
    }
  }

  /// محاكاة بيانات الدرجات للاختبار (مُحتفظ بها للرجوع إليها عند الحاجة)
  ///
  /// هذه الدالة تنشئ بيانات وهمية لاختبار الواجهة
  /// تم استبدالها بالبيانات الحقيقية في _convertRealDataToGradeModels
  void _simulateGradesData() {
    final now = DateTime.now();

    // محاكاة درجات متنوعة
    _studentGrades = [
      StudentGradeModel(
        id: '1',
        studentId: widget.studentId,
        examId: 'exam1',
        examName: 'امتحان الرياضيات النصفي',
        subjectId: 'math',
        subjectName: 'الرياضيات',
        teacherId: 'teacher1',
        teacherName: 'أ. أحمد محمد',
        grade: 85.5,
        maxGrade: 100.0,
        examDate: now.subtract(const Duration(days: 30)),
        examType: ExamType.midterm,
        examDuration: 90,
        isPublished: true,
        publishedAt: now.subtract(const Duration(days: 28)),
        teacherNotes: 'أداء جيد، يحتاج تحسين في الهندسة',
        performanceRating: 'جيد جداً',
        classAverage: 75.0,
        highestInClass: 95.0,
        lowestInClass: 45.0,
        rankInClass: 8,
        totalStudents: 30,
        semester: 'الفصل الأول',
        academicYear: '2024-2025',
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now.subtract(const Duration(days: 28)),
      ),
      StudentGradeModel(
        id: '2',
        studentId: widget.studentId,
        examId: 'exam2',
        examName: 'امتحان العلوم النهائي',
        subjectId: 'science',
        subjectName: 'العلوم',
        teacherId: 'teacher2',
        teacherName: 'أ. فاطمة علي',
        grade: 92.0,
        maxGrade: 100.0,
        examDate: now.subtract(const Duration(days: 25)),
        examType: ExamType.finalExam,
        examDuration: 120,
        isPublished: true,
        publishedAt: now.subtract(const Duration(days: 23)),
        teacherNotes: 'ممتاز، استمر على هذا المستوى',
        performanceRating: 'ممتاز',
        classAverage: 80.5,
        highestInClass: 98.0,
        lowestInClass: 55.0,
        rankInClass: 3,
        totalStudents: 30,
        semester: 'الفصل الأول',
        academicYear: '2024-2025',
        createdAt: now.subtract(const Duration(days: 25)),
        updatedAt: now.subtract(const Duration(days: 23)),
      ),
      StudentGradeModel(
        id: '3',
        studentId: widget.studentId,
        examId: 'exam3',
        examName: 'امتحان اللغة العربية الشهري',
        subjectId: 'arabic',
        subjectName: 'اللغة العربية',
        teacherId: 'teacher3',
        teacherName: 'أ. سارة أحمد',
        grade: 78.0,
        maxGrade: 100.0,
        examDate: now.subtract(const Duration(days: 20)),
        examType: ExamType.monthly,
        examDuration: 60,
        isPublished: true,
        publishedAt: now.subtract(const Duration(days: 18)),
        teacherNotes: 'جيد، ركز أكثر على القواعد',
        performanceRating: 'جيد',
        classAverage: 72.0,
        highestInClass: 88.0,
        lowestInClass: 40.0,
        rankInClass: 12,
        totalStudents: 30,
        semester: 'الفصل الأول',
        academicYear: '2024-2025',
        createdAt: now.subtract(const Duration(days: 20)),
        updatedAt: now.subtract(const Duration(days: 18)),
      ),
    ];

    // محاكاة متوسطات الصف
    _classAverages = {'exam1': 75.0, 'exam2': 80.5, 'exam3': 72.0};
  }

  /// تجميع الدرجات حسب المادة
  ///
  /// ينظم الدرجات في مجموعات حسب المادة لسهولة العرض والتحليل
  void _groupGradesBySubject() {
    _gradesBySubject.clear();

    for (final grade in _studentGrades) {
      final subjectId = grade.subjectId;
      if (_gradesBySubject.containsKey(subjectId)) {
        _gradesBySubject[subjectId]!.add(grade);
      } else {
        _gradesBySubject[subjectId] = [grade];
      }
    }
  }

  /// تجميع الدرجات حسب نوع الامتحان
  ///
  /// ينظم الدرجات حسب نوع الامتحان (نهائي، نصفي، شهري، إلخ)
  void _groupGradesByExamType() {
    _gradesByExamType.clear();

    for (final grade in _studentGrades) {
      final examType = grade.examType;
      if (_gradesByExamType.containsKey(examType)) {
        _gradesByExamType[examType]!.add(grade);
      } else {
        _gradesByExamType[examType] = [grade];
      }
    }
  }

  /// حساب الإحصائيات الأساسية
  ///
  /// يحسب جميع الإحصائيات المطلوبة لعرض الأداء:
  /// - المعدل العام والفصلي
  /// - أعلى وأقل درجة
  /// - عدد الامتحانات المجتازة والراسبة
  /// - متوسط الدرجات
  void _calculateStatistics() {
    if (_studentGrades.isEmpty) return;

    // حساب إجمالي عدد الامتحانات
    _totalExams = _studentGrades.length;

    // حساب أعلى وأقل درجة
    _highestGrade = _studentGrades
        .map((g) => g.grade)
        .reduce((a, b) => a > b ? a : b);
    _lowestGrade = _studentGrades
        .map((g) => g.grade)
        .reduce((a, b) => a < b ? a : b);

    // حساب متوسط الدرجات
    final totalGrades = _studentGrades
        .map((g) => g.grade)
        .reduce((a, b) => a + b);
    _averageGrade = totalGrades / _totalExams;

    // حساب المعدل العام (GPA) - افتراض أن 90+ = A, 80+ = B, إلخ
    double totalGPA = 0.0;
    for (final grade in _studentGrades) {
      totalGPA += _convertGradeToGPA(grade.grade);
    }
    _overallGPA = totalGPA / _totalExams;

    // حساب عدد الامتحانات المجتازة (افتراض أن 60+ نجاح)
    _passedExams = _studentGrades.where((g) => g.grade >= 60.0).length;
    _failedExams = _totalExams - _passedExams;

    // محاكاة ترتيب الطالب (في التطبيق الفعلي سيأتي من قاعدة البيانات)
    _classRank = 5;
    _totalStudentsInClass = 30;

    // محاكاة نسبة التحسن
    _improvementPercentage = 12.5;
  }

  /// تحويل الدرجة إلى نقاط GPA
  ///
  /// [grade] الدرجة المئوية
  /// يرجع نقاط GPA المقابلة (0-4)
  double _convertGradeToGPA(double grade) {
    if (grade >= 90) return 4.0; // A
    if (grade >= 80) return 3.0; // B
    if (grade >= 70) return 2.0; // C
    if (grade >= 60) return 1.0; // D
    return 0.0; // F
  }

  /// تحليل الأداء وتحديد نقاط القوة والضعف
  ///
  /// يحلل أداء الطالب في المواد المختلفة ويحدد:
  /// - المواد التي يتفوق فيها
  /// - المواد التي تحتاج تحسين
  /// - التوصيات للتطوير
  void _analyzePerformance() {
    _strongSubjects.clear();
    _weakSubjects.clear();

    // تحليل الأداء حسب المادة
    _gradesBySubject.forEach((subjectId, grades) {
      final subjectAverage =
          grades.map((g) => g.grade).reduce((a, b) => a + b) / grades.length;

      if (subjectAverage >= 85.0) {
        // المواد التي يتفوق فيها الطالب
        final subjectName = grades.first.subjectName;
        _strongSubjects.add(subjectName);
      } else if (subjectAverage < 75.0) {
        // المواد التي تحتاج تحسين
        final subjectName = grades.first.subjectName;
        _weakSubjects.add(subjectName);
      }
    });
  }

  // ===================================================================
  // معالجات الأحداث والحوارات
  // ===================================================================

  /// معالج تغيير التبويبات
  void _onTabChanged() {
    if (!mounted) return;

    // تحديث البيانات حسب التبويب المحدد
    switch (_tabController.index) {
      case 0: // تبويب الملخص
        break;
      case 1: // تبويب الدرجات
        break;
      case 2: // تبويب الإحصائيات
        // إعادة تشغيل الرسوم المتحركة للمخططات
        _chartAnimationController.reset();
        _chartAnimationController.forward();
        break;
      case 3: // تبويب التقارير
        break;
    }
  }

  /// عرض حوار البحث
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('البحث في النتائج'),
            content: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'ابحث عن مادة أو امتحان...',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('بحث'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار الفلاتر
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('فلترة وترتيب النتائج'),
            content: const Text('سيتم تطبيق خيارات الفلترة والترتيب قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  /// تصدير التقرير
  Future<void> _exportReport() async {
    setState(() {
      _isExporting = true;
    });

    try {
      // TODO: تطبيق تصدير التقرير
      await Future.delayed(const Duration(seconds: 2));

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تصدير التقرير بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تصدير التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  /// عرض حوار المشاركة
  void _showShareDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('مشاركة النتائج'),
            content: const Text('سيتم تطبيق ميزة المشاركة قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  // ===================================================================
  // دوال بناء التبويبات
  // ===================================================================

  /// بناء تبويب الملخص العام
  Widget _buildSummaryTab() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    return RefreshIndicator(
      onRefresh: _loadStudentResults,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // بطاقة المعدل العام
            _buildGPACard(),
            const SizedBox(height: 16),

            // إحصائيات سريعة
            _buildQuickStats(),
            const SizedBox(height: 16),

            // أحدث النتائج
            _buildRecentResults(),
            const SizedBox(height: 16),

            // نقاط القوة والضعف
            _buildStrengthsWeaknesses(),
          ],
        ),
      ),
    );
  }

  /// بناء تبويب الدرجات التفصيلية
  Widget _buildGradesTab() {
    return const Center(
      child: Text(
        'الدرجات التفصيلية\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء تبويب الإحصائيات
  Widget _buildStatisticsTab() {
    return const Center(
      child: Text(
        'الإحصائيات والمقارنات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء تبويب التقارير
  Widget _buildReportsTab() {
    return const Center(
      child: Text(
        'التقارير والشهادات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  // ===================================================================
  // دوال بناء المكونات الفرعية
  // ===================================================================

  /// بناء بطاقة المعدل العام
  Widget _buildGPACard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.teal[600]!, Colors.teal[800]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          const Text(
            'معدلي العام',
            style: TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _overallGPA.toStringAsFixed(2),
            style: const TextStyle(
              fontSize: 36,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'من 4.0',
            style: TextStyle(fontSize: 14, color: Colors.teal[100]),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildGPAInfo('الترتيب', '$_classRank من $_totalStudentsInClass'),
              _buildGPAInfo(
                'التحسن',
                '${_improvementPercentage.toStringAsFixed(1)}%',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء معلومة في بطاقة المعدل
  Widget _buildGPAInfo(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(label, style: TextStyle(fontSize: 12, color: Colors.teal[100])),
      ],
    );
  }

  /// بناء الإحصائيات السريعة
  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'إجمالي الامتحانات',
            _totalExams.toString(),
            Icons.quiz,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'نجحت في',
            _passedExams.toString(),
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'أعلى درجة',
            _highestGrade.toStringAsFixed(1),
            Icons.trending_up,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء أحدث النتائج
  Widget _buildRecentResults() {
    return const Center(
      child: Text(
        'أحدث النتائج\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  /// بناء نقاط القوة والضعف
  Widget _buildStrengthsWeaknesses() {
    return const Center(
      child: Text(
        'نقاط القوة والضعف\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  /// بناء شريط الإحصائيات السفلي
  Widget _buildBottomStatsBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.teal[50],
        border: Border(top: BorderSide(color: Colors.teal[200]!)),
      ),
      child: Row(
        children: [
          Icon(Icons.grade, size: 16, color: Colors.teal[600]),
          const SizedBox(width: 4),
          Text(
            'متوسط: ${_averageGrade.toStringAsFixed(1)}',
            style: TextStyle(fontSize: 12, color: Colors.teal[600]),
          ),
          const Spacer(),
          Icon(Icons.trending_up, size: 16, color: Colors.teal[600]),
          const SizedBox(width: 4),
          Text(
            'تحسن: ${_improvementPercentage.toStringAsFixed(1)}%',
            style: TextStyle(fontSize: 12, color: Colors.teal[600]),
          ),
        ],
      ),
    );
  }
}

/// تعداد فلاتر الفترة الزمنية
enum TimeFilter {
  all, // جميع الفترات
  currentSemester, // الفصل الحالي
  lastMonth, // الشهر الماضي
}

/// تعداد ترتيب النتائج
enum ResultSort {
  date, // حسب التاريخ
  grade, // حسب الدرجة
  subject, // حسب المادة
}
