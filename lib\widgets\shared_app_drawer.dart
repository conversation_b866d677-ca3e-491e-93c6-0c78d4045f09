import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:school_management_system/mobile_screens/public_home_screen.dart';
import 'package:school_management_system/services/firebase_service.dart';
import 'package:school_management_system/parent_screens/parent_screens_exports.dart';
import 'package:school_management_system/shared/app_theme.dart';

/// ويدجت القائمة الجانبية المشتركة والمحسّنة للتطبيق
///
/// تعرض معلومات المستخدم المسجل دخوله وتوفر وصولاً سهلاً
/// إلى الشاشة العامة وزر تسجيل الخروج.
class SharedAppDrawer extends StatefulWidget {
  const SharedAppDrawer({super.key});

  @override
  State<SharedAppDrawer> createState() => _SharedAppDrawerState();
}

class _SharedAppDrawerState extends State<SharedAppDrawer> {
  final User? _user = FirebaseAuth.instance.currentUser;
  String _userName = 'جار التحميل...';
  String _userEmail = '';
  String _userRole = '';

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  /// دالة لجلب بيانات المستخدم (الاسم) من Firestore
  Future<void> _loadUserData() async {
    if (_user == null) {
      if (mounted) setState(() => _userName = 'زائر');
      return;
    }

    _userEmail = _user!.email ?? 'بريد إلكتروني غير متوفر';

    // البحث في المجموعات المختلفة لتحديد نوع المستخدم
    final collectionsToSearch = [
      {'collection': 'guardians', 'role': 'guardian'},
      {'collection': 'students', 'role': 'student'},
      {'collection': 'teachers', 'role': 'teacher'},
      {'collection': 'users', 'role': 'user'},
    ];

    for (Map<String, String> searchItem in collectionsToSearch) {
      try {
        final doc =
            await FirebaseFirestore.instance
                .collection(searchItem['collection']!)
                .doc(_user!.uid)
                .get();
        if (doc.exists && doc.data()!.containsKey('name')) {
          if (mounted) {
            setState(() {
              _userName = (doc.data()! as Map<String, dynamic>)['name'];
              _userRole = searchItem['role']!;
            });
          }
          return; // تم العثور على الاسم والدور، أوقف البحث
        }
      } catch (e) {
        // تجاهل الخطأ إذا كانت المجموعة غير موجودة واستمر
        print('Could not search in collection ${searchItem['collection']}: $e');
      }
    }

    // إذا لم يتم العثور على الاسم في أي مكان
    if (mounted) setState(() => _userName = 'مستخدم');
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          UserAccountsDrawerHeader(
            accountName: Text(
              _userName,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                fontFamily: GoogleFonts.cairo().fontFamily,
              ),
            ),
            accountEmail: Text(
              _userEmail,
              style: TextStyle(fontFamily: GoogleFonts.cairo().fontFamily),
            ),
            currentAccountPicture: CircleAvatar(
              backgroundColor: Colors.white,
              child: Text(
                _userName.isNotEmpty ? _userName[0].toUpperCase() : 'U',
                style: TextStyle(
                  fontSize: 40.0,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
            decoration: BoxDecoration(color: Theme.of(context).primaryColor),
          ),

          // عناصر خاصة بأولياء الأمور
          if (_userRole == 'guardian') ...[
            ListTile(
              leading: const Icon(
                Icons.dashboard_outlined,
                color: AppColors.parentColor,
              ),
              title: const Text('لوحة التحكم المتقدمة'),
              subtitle: const Text('إحصائيات شاملة وتقارير'),
              onTap: () {
                Navigator.of(context).pop();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            ParentDashboardScreen(parentId: _user!.uid),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.analytics_outlined,
                color: AppColors.parentColor,
              ),
              title: const Text('متابعة أداء الأبناء'),
              subtitle: const Text('تقارير مفصلة ومقارنات'),
              onTap: () {
                Navigator.of(context).pop();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            ChildrenPerformanceScreen(parentId: _user!.uid),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.forum_outlined,
                color: AppColors.parentColor,
              ),
              title: const Text('التواصل المتقدم'),
              subtitle: const Text('رسائل، مواعيد، وطلبات'),
              onTap: () {
                Navigator.of(context).pop();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            SchoolCommunicationScreen(parentId: _user!.uid),
                  ),
                );
              },
            ),
            const Divider(),
          ],

          ListTile(
            leading: const Icon(Icons.home_work_outlined),
            title: const Text('الواجهة العامة'),
            onTap: () {
              Navigator.of(context).pop();
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (context) => const PublicHomeScreen(),
                ),
                (Route<dynamic> route) => false,
              );
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.redAccent),
            title: const Text(
              'تسجيل الخروج',
              style: TextStyle(color: Colors.redAccent),
            ),
            onTap: () async {
              Navigator.of(context).pop();
              await FirebaseService().signOut();
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(
                  builder: (context) => const PublicHomeScreen(),
                ),
                (Route<dynamic> route) => false,
              );
            },
          ),
        ],
      ),
    );
  }
}
