import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/admin_screens/widgets/student_form_dialog.dart';
import 'package:school_management_system/providers/services_provider.dart';

class StudentsDataTable extends ConsumerWidget {
  final List<StudentModel> students;

  const StudentsDataTable({super.key, required this.students});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('الاسم')),
          DataColumn(label: Text('الرقم الأكاديمي')),
          DataColumn(label: Text('الصف')),
          DataColumn(label: Text('الجنس')),
          DataColumn(label: Text('ولي الأمر')),
          DataColumn(label: Text('المحافظة')),
          DataColumn(label: Text('البريد الإلكتروني')),
          DataColumn(label: Text('تاريخ الإنشاء')),
          DataColumn(label: Text('إجراءات')),
        ],
        rows:
            students.map((student) {
              return DataRow(
                cells: [
                  // الاسم
                  DataCell(Text(student.name)),
                  // الرقم الأكاديمي
                  DataCell(Text(student.studentNumber)),
                  // الصف
                  DataCell(Text(student.studentClass)),
                  // الجنس
                  DataCell(Text(student.gender ?? 'غير محدد')),
                  // ولي الأمر
                  DataCell(Text(student.guardianName)),
                  // المحافظة
                  DataCell(Text(student.governorate)),
                  // البريد الإلكتروني
                  DataCell(Text(student.email)),
                  // تاريخ الإنشاء
                  DataCell(
                    Text(
                      DateFormat(
                        'yyyy/MM/dd',
                      ).format(student.createdAt.toDate()),
                    ),
                  ),
                  // الإجراءات
                  DataCell(
                    Row(
                      children: [
                        // زر التعديل
                        IconButton(
                          icon: const Icon(Icons.edit, color: Colors.blue),
                          tooltip: 'تعديل بيانات الطالب',
                          onPressed:
                              () => _showStudentFormDialog(
                                context,
                                ref,
                                student: student,
                              ),
                        ),
                        // زر عرض التفاصيل
                        IconButton(
                          icon: const Icon(
                            Icons.visibility,
                            color: Colors.green,
                          ),
                          tooltip: 'عرض تفاصيل الطالب',
                          onPressed:
                              () => _showStudentDetails(context, student),
                        ),
                        // زر الحذف
                        IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          tooltip: 'حذف الطالب',
                          onPressed:
                              () => _confirmDelete(context, ref, student),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            }).toList(),
      ),
    );
  }

  /// عرض نموذج إضافة/تعديل الطالب
  void _showStudentFormDialog(
    BuildContext context,
    WidgetRef ref, {
    StudentModel? student,
  }) {
    showDialog(
      context: context,
      builder: (context) => StudentFormDialog(student: student),
    );
  }

  /// عرض تفاصيل الطالب في نافذة منبثقة
  void _showStudentDetails(BuildContext context, StudentModel student) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('تفاصيل الطالب: ${student.name}'),
            content: SizedBox(
              width: double.maxFinite,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // ===== المعلومات الأساسية =====
                    const Text(
                      'المعلومات الأساسية',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildDetailRow('الاسم الكامل', student.name),
                    _buildDetailRow('الرقم الأكاديمي', student.studentNumber),
                    _buildDetailRow('الصف', student.studentClass),
                    _buildDetailRow('الجنس', student.gender ?? 'غير محدد'),
                    _buildDetailRow('البريد الإلكتروني', student.email),

                    const SizedBox(height: 16),
                    const Divider(),

                    // ===== المعلومات الشخصية =====
                    const Text(
                      'المعلومات الشخصية',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildDetailRow(
                      'تاريخ الميلاد',
                      student.dateOfBirth != null
                          ? '${student.dateOfBirth!.day}/${student.dateOfBirth!.month}/${student.dateOfBirth!.year}'
                          : 'غير متوفر',
                    ),
                    _buildDetailRow(
                      'رقم الهاتف',
                      student.phoneNumber ?? 'غير متوفر',
                    ),
                    _buildDetailRow('العنوان', student.address ?? 'غير متوفر'),
                    _buildDetailRow(
                      'الرقم الوطني',
                      student.nationalId ?? 'غير متوفر',
                    ),

                    const SizedBox(height: 16),
                    const Divider(),

                    // ===== معلومات ولي الأمر =====
                    const Text(
                      'معلومات ولي الأمر',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildDetailRow('اسم ولي الأمر', student.guardianName),
                    _buildDetailRow(
                      'رقم هاتف ولي الأمر',
                      student.guardianPhone ?? 'غير متوفر',
                    ),

                    const SizedBox(height: 16),
                    const Divider(),

                    // ===== المعلومات الإضافية =====
                    const Text(
                      'المعلومات الإضافية',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildDetailRow('المحافظة', student.governorate),
                    _buildDetailRow('الجنسية', student.nationality),
                    _buildDetailRow(
                      'فصيلة الدم',
                      student.bloodType ?? 'غير متوفر',
                    ),
                    _buildDetailRow(
                      'الحالة الصحية',
                      student.healthCondition ?? 'سليم',
                    ),

                    if (student.notes != null && student.notes!.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      const Divider(),
                      const Text(
                        'ملاحظات خاصة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(student.notes!),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _confirmDelete(
    BuildContext context,
    WidgetRef ref,
    StudentModel student,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text(
              'هل أنت متأكد من رغبتك في حذف الطالب ${student.name}؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  ref.read(firebaseServiceProvider).deleteStudent(student);
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  /// بناء صف تفاصيل في نافذة عرض تفاصيل الطالب
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
