import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/admin_screens/widgets/staff_form_dialog.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/providers/staff_providers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

// شاشة إدارة الكادر التعليمي والموظفين في لوحة التحكم
class StaffManagementScreen extends ConsumerWidget {
  const StaffManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final staffAsyncValue = ref.watch(staffStreamProvider);
    final filteredStaff = ref.watch(filteredStaffProvider);

    return Scaffold(
      body: Column(
        children: [
          // --- شريط البحث ---
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              decoration: const InputDecoration(
                labelText:
                    'ابحث بالاسم، البريد، الهاتف، المسمى الوظيفي، أو المحافظة...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
                helperText: 'يمكنك البحث في جميع الحقول',
              ),
              onChanged: (value) {
                ref.read(staffSearchQueryProvider.notifier).state = value;
              },
            ),
          ),
          // --- عرض قائمة الموظفين ---
          Expanded(
            child: staffAsyncValue.when(
              loading: () => const LoadingIndicator(),
              error:
                  (err, stack) =>
                      ErrorMessage(message: 'خطأ في جلب البيانات: $err'),
              data: (staffList) {
                if (staffList.isEmpty) {
                  return const Center(child: Text('لا يوجد موظفون حالياً.'));
                }
                if (filteredStaff.isEmpty) {
                  return const Center(
                    child: Text('لم يتم العثور على نتائج للبحث.'),
                  );
                }

                // --- عرض النتائج في شبكة ---
                return GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                    maxCrossAxisExtent: 400, // أقصى عرض للبطاقة
                    childAspectRatio: 2.5, // نسبة العرض إلى الارتفاع
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: filteredStaff.length,
                  itemBuilder: (context, index) {
                    final user = filteredStaff[index];
                    return _buildStaffCard(context, user, ref);
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed:
            () => showDialog(
              context: context,
              builder: (context) => const StaffFormDialog(),
            ),
        tooltip: 'إضافة موظف',
        child: const Icon(Icons.add),
      ),
    );
  }

  // --- ويدجت بناء بطاقة الموظف المحدثة ---
  Widget _buildStaffCard(BuildContext context, UserModel user, WidgetRef ref) {
    return CustomCard(
      child: InkWell(
        onTap: () => _showStaffDetails(context, user),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // --- الصورة الشخصية ---
              CircleAvatar(
                radius: 40,
                backgroundImage:
                    (user.profileImageUrl != null &&
                            user.profileImageUrl!.isNotEmpty)
                        ? NetworkImage(user.profileImageUrl!)
                        : null,
                child:
                    (user.profileImageUrl == null ||
                            user.profileImageUrl!.isEmpty)
                        ? const Icon(Icons.person, size: 40)
                        : null,
              ),
              const SizedBox(width: 16),
              // --- معلومات الموظف المحدثة مع الحقول الجديدة ---
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // ===== الاسم والمسمى الوظيفي =====
                    Text(
                      user.name,
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    Text(
                      user.jobTitle ?? 'غير محدد',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // ===== معلومات الاتصال =====
                    Row(
                      children: [
                        const Icon(Icons.email, size: 16, color: Colors.grey),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            user.email,
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ),
                      ],
                    ),

                    if (user.phoneNumber != null &&
                        user.phoneNumber!.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(Icons.phone, size: 16, color: Colors.grey),
                          const SizedBox(width: 4),
                          Text(
                            user.phoneNumber!,
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ],

                    const SizedBox(height: 8),

                    // ===== معلومات إضافية =====
                    Row(
                      children: [
                        // الدور
                        Chip(
                          label: Text(
                            user.role == 'teacher' ? 'معلم' : 'إداري',
                          ),
                          backgroundColor:
                              user.role == 'teacher'
                                  ? Colors.blue.shade100
                                  : Colors.green.shade100,
                          materialTapTargetSize:
                              MaterialTapTargetSize.shrinkWrap,
                        ),

                        const SizedBox(width: 8),

                        // الجنس (إذا كان متوفراً)
                        if (user.gender != null && user.gender!.isNotEmpty)
                          Chip(
                            label: Text(user.gender!),
                            backgroundColor: Colors.purple.shade100,
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                          ),
                      ],
                    ),

                    // ===== المحافظة والجنسية (إذا كانت متوفرة) =====
                    if ((user.governorate != null &&
                            user.governorate!.isNotEmpty) ||
                        user.nationality.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          if (user.governorate != null &&
                              user.governorate!.isNotEmpty) ...[
                            const Icon(
                              Icons.location_city,
                              size: 16,
                              color: Colors.grey,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              user.governorate!,
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],

                          if (user.nationality.isNotEmpty) ...[
                            if (user.governorate != null &&
                                user.governorate!.isNotEmpty)
                              const Text(
                                ' • ',
                                style: TextStyle(color: Colors.grey),
                              ),
                            const Icon(
                              Icons.flag,
                              size: 16,
                              color: Colors.grey,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              user.nationality,
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                          ],
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              // --- أزرار الإجراءات ---
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  IconButton(
                    icon: const Icon(Icons.edit, color: Colors.blue),
                    tooltip: 'تعديل',
                    onPressed:
                        () => showDialog(
                          context: context,
                          builder: (context) => StaffFormDialog(staff: user),
                        ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.lock_reset, color: Colors.orange),
                    tooltip: 'إعادة تعيين كلمة المرور',
                    // TODO: Implement password reset logic via a provider/controller
                    onPressed: () {
                      print("Reset password for ${user.email}");
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    tooltip: 'حذف',
                    // TODO: Implement delete logic via a provider/controller
                    onPressed: () {
                      print("Delete user ${user.name}");
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// عرض تفاصيل الموظف في نافذة منبثقة
  void _showStaffDetails(BuildContext context, UserModel user) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('تفاصيل الموظف: ${user.name}'),
            content: SizedBox(
              width: double.maxFinite,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // ===== المعلومات الأساسية =====
                    const Text(
                      'المعلومات الأساسية',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildDetailRow('الاسم الكامل', user.name),
                    _buildDetailRow('البريد الإلكتروني', user.email),
                    _buildDetailRow(
                      'المسمى الوظيفي',
                      user.jobTitle ?? 'غير محدد',
                    ),
                    _buildDetailRow(
                      'الدور',
                      user.role == 'teacher' ? 'معلم' : 'إداري',
                    ),
                    if (user.bio != null && user.bio!.isNotEmpty)
                      _buildDetailRow('النبذة التعريفية', user.bio!),

                    const SizedBox(height: 16),
                    const Divider(),

                    // ===== المعلومات الشخصية =====
                    const Text(
                      'المعلومات الشخصية',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 8),
                    if (user.phoneNumber != null &&
                        user.phoneNumber!.isNotEmpty)
                      _buildDetailRow('رقم الهاتف', user.phoneNumber!),
                    if (user.gender != null && user.gender!.isNotEmpty)
                      _buildDetailRow('الجنس', user.gender!),
                    if (user.dateOfBirth != null)
                      _buildDetailRow(
                        'تاريخ الميلاد',
                        '${user.dateOfBirth!.day}/${user.dateOfBirth!.month}/${user.dateOfBirth!.year}',
                      ),
                    if (user.address != null && user.address!.isNotEmpty)
                      _buildDetailRow('العنوان', user.address!),
                    _buildDetailRow('الجنسية', user.nationality),

                    const SizedBox(height: 16),
                    const Divider(),

                    // ===== المعلومات الإضافية =====
                    const Text(
                      'المعلومات الإضافية',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 8),
                    if (user.nationalId != null && user.nationalId!.isNotEmpty)
                      _buildDetailRow('الرقم الوطني', user.nationalId!),
                    if (user.governorate != null &&
                        user.governorate!.isNotEmpty)
                      _buildDetailRow('المحافظة', user.governorate!),
                    if (user.bloodType != null && user.bloodType!.isNotEmpty)
                      _buildDetailRow('فصيلة الدم', user.bloodType!),
                    if (user.healthCondition != null &&
                        user.healthCondition!.isNotEmpty)
                      _buildDetailRow('الحالة الصحية', user.healthCondition!),
                    if (user.notes != null && user.notes!.isNotEmpty)
                      _buildDetailRow('ملاحظات', user.notes!),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  showDialog(
                    context: context,
                    builder: (context) => StaffFormDialog(staff: user),
                  );
                },
                child: const Text('تعديل'),
              ),
            ],
          ),
    );
  }

  /// بناء صف تفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(child: Text(value, style: const TextStyle(fontSize: 14))),
        ],
      ),
    );
  }
}
