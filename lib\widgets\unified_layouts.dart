import 'package:flutter/material.dart';
import 'package:school_management_system/shared/app_theme.dart';
import 'package:school_management_system/widgets/unified_components.dart';

/// مجموعة تخطيطات موحدة لشاشات النظام
/// 
/// توفر تخطيطات جاهزة ومتناسقة لأنواع الشاشات المختلفة
/// مع تطبيق النظام الموحد للمسافات والتصميم

/// تخطيط شاشة أساسي موحد
/// 
/// يوفر هيكل أساسي للشاشات مع شريط تطبيق وجسم ومنطقة عائمة
class UnifiedScreenLayout extends StatelessWidget {
  /// عنوان الشاشة
  final String title;
  
  /// نوع المستخدم
  final UserType userType;
  
  /// محتوى الشاشة
  final Widget body;
  
  /// زر عائم (اختياري)
  final Widget? floatingActionButton;
  
  /// قائمة جانبية (اختياري)
  final Widget? drawer;
  
  /// شريط تنقل سفلي (اختياري)
  final Widget? bottomNavigationBar;
  
  /// إجراءات شريط التطبيق (اختياري)
  final List<Widget>? appBarActions;
  
  /// إمكانية السحب للتحديث (افتراضي: false)
  final bool enableRefresh;
  
  /// دالة التحديث (مطلوبة إذا كان enableRefresh = true)
  final Future<void> Function()? onRefresh;
  
  /// إظهار مؤشر التحميل (افتراضي: false)
  final bool isLoading;
  
  /// رسالة التحميل (اختياري)
  final String? loadingMessage;

  const UnifiedScreenLayout({
    super.key,
    required this.title,
    required this.userType,
    required this.body,
    this.floatingActionButton,
    this.drawer,
    this.bottomNavigationBar,
    this.appBarActions,
    this.enableRefresh = false,
    this.onRefresh,
    this.isLoading = false,
    this.loadingMessage,
  });

  @override
  Widget build(BuildContext context) {
    Widget screenBody = body;
    
    // إضافة السحب للتحديث إذا كان مفعلاً
    if (enableRefresh && onRefresh != null) {
      screenBody = RefreshIndicator(
        onRefresh: onRefresh!,
        child: screenBody,
      );
    }
    
    // إضافة مؤشر التحميل إذا كان مفعلاً
    if (isLoading) {
      screenBody = Stack(
        children: [
          screenBody,
          Container(
            color: AppColors.background.withValues(alpha: 0.8),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(
                    color: AppColors.primary,
                  ),
                  if (loadingMessage != null) ...[
                    const SizedBox(height: AppSpacing.md),
                    Text(
                      loadingMessage!,
                      style: const TextStyle(
                        fontSize: AppTextSizes.bodyMedium,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      );
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: UnifiedAppBar(
        title: title,
        userType: userType,
        actions: appBarActions,
      ),
      drawer: drawer,
      body: screenBody,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: bottomNavigationBar,
    );
  }
}

/// تخطيط شاشة قائمة موحد
/// 
/// مخصص لعرض قوائم البيانات مع إمكانيات البحث والتصفية
class UnifiedListLayout extends StatelessWidget {
  /// عنوان الشاشة
  final String title;
  
  /// نوع المستخدم
  final UserType userType;
  
  /// قائمة العناصر
  final List<Widget> items;
  
  /// إمكانية البحث (افتراضي: false)
  final bool enableSearch;
  
  /// دالة البحث (مطلوبة إذا كان enableSearch = true)
  final void Function(String)? onSearch;
  
  /// نص البحث الحالي
  final String? searchQuery;
  
  /// زر إضافة (اختياري)
  final VoidCallback? onAdd;
  
  /// نص زر الإضافة (افتراضي: "إضافة")
  final String addButtonText;
  
  /// أيقونة زر الإضافة (افتراضي: add)
  final IconData addButtonIcon;
  
  /// إمكانية السحب للتحديث (افتراضي: true)
  final bool enableRefresh;
  
  /// دالة التحديث
  final Future<void> Function()? onRefresh;
  
  /// حالة فارغة (اختياري)
  final Widget? emptyState;

  const UnifiedListLayout({
    super.key,
    required this.title,
    required this.userType,
    required this.items,
    this.enableSearch = false,
    this.onSearch,
    this.searchQuery,
    this.onAdd,
    this.addButtonText = 'إضافة',
    this.addButtonIcon = Icons.add,
    this.enableRefresh = true,
    this.onRefresh,
    this.emptyState,
  });

  @override
  Widget build(BuildContext context) {
    return UnifiedScreenLayout(
      title: title,
      userType: userType,
      enableRefresh: enableRefresh,
      onRefresh: onRefresh,
      floatingActionButton: onAdd != null
          ? FloatingActionButton.extended(
              onPressed: onAdd,
              icon: Icon(addButtonIcon),
              label: Text(addButtonText),
              backgroundColor: _getUserColor(userType),
              foregroundColor: AppColors.textOnPrimary,
            )
          : null,
      body: Column(
        children: [
          // شريط البحث
          if (enableSearch) ...[
            Container(
              padding: const EdgeInsets.all(AppSpacing.md),
              decoration: const BoxDecoration(
                color: AppColors.surface,
                border: Border(
                  bottom: BorderSide(color: AppColors.border),
                ),
              ),
              child: TextField(
                onChanged: onSearch,
                decoration: InputDecoration(
                  hintText: 'البحث...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: searchQuery?.isNotEmpty == true
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () => onSearch?.call(''),
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppBorderRadius.medium),
                    borderSide: const BorderSide(color: AppColors.border),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppBorderRadius.medium),
                    borderSide: BorderSide(color: _getUserColor(userType)),
                  ),
                ),
              ),
            ),
          ],
          
          // القائمة
          Expanded(
            child: items.isEmpty && emptyState != null
                ? emptyState!
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                    itemCount: items.length,
                    itemBuilder: (context, index) => items[index],
                  ),
          ),
        ],
      ),
    );
  }

  /// الحصول على لون المستخدم
  Color _getUserColor(UserType type) {
    switch (type) {
      case UserType.student:
        return AppColors.studentColor;
      case UserType.parent:
        return AppColors.parentColor;
      case UserType.teacher:
        return AppColors.teacherColor;
      case UserType.admin:
        return AppColors.adminColor;
      case UserType.general:
        return AppColors.primary;
    }
  }
}

/// تخطيط شاشة تفاصيل موحد
/// 
/// مخصص لعرض تفاصيل عنصر واحد مع إمكانيات التعديل
class UnifiedDetailLayout extends StatelessWidget {
  /// عنوان الشاشة
  final String title;
  
  /// نوع المستخدم
  final UserType userType;
  
  /// أقسام التفاصيل
  final List<Widget> sections;
  
  /// إمكانية التعديل (افتراضي: false)
  final bool canEdit;
  
  /// دالة التعديل
  final VoidCallback? onEdit;
  
  /// إمكانية الحذف (افتراضي: false)
  final bool canDelete;
  
  /// دالة الحذف
  final VoidCallback? onDelete;
  
  /// إجراءات إضافية
  final List<Widget>? additionalActions;

  const UnifiedDetailLayout({
    super.key,
    required this.title,
    required this.userType,
    required this.sections,
    this.canEdit = false,
    this.onEdit,
    this.canDelete = false,
    this.onDelete,
    this.additionalActions,
  });

  @override
  Widget build(BuildContext context) {
    final actions = <Widget>[];
    
    // إضافة الإجراءات الإضافية
    if (additionalActions != null) {
      actions.addAll(additionalActions!);
    }
    
    // إضافة زر التعديل
    if (canEdit && onEdit != null) {
      actions.add(
        IconButton(
          icon: const Icon(Icons.edit_outlined),
          tooltip: 'تعديل',
          onPressed: onEdit,
        ),
      );
    }
    
    // إضافة زر الحذف
    if (canDelete && onDelete != null) {
      actions.add(
        IconButton(
          icon: const Icon(Icons.delete_outline),
          tooltip: 'حذف',
          onPressed: () => _showDeleteConfirmation(context),
        ),
      );
    }

    return UnifiedScreenLayout(
      title: title,
      userType: userType,
      appBarActions: actions.isNotEmpty ? actions : null,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.screenPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: sections
              .expand((section) => [
                    section,
                    const SizedBox(height: AppSpacing.sectionSpacing),
                  ])
              .toList()
            ..removeLast(), // إزالة المسافة الأخيرة
        ),
      ),
    );
  }

  /// إظهار تأكيد الحذف
  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من رغبتك في حذف هذا العنصر؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              onDelete?.call();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.textOnPrimary,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}

/// تخطيط شاشة نموذج موحد
/// 
/// مخصص لشاشات إدخال البيانات والنماذج
class UnifiedFormLayout extends StatelessWidget {
  /// عنوان الشاشة
  final String title;
  
  /// نوع المستخدم
  final UserType userType;
  
  /// حقول النموذج
  final List<Widget> fields;
  
  /// دالة الحفظ
  final VoidCallback? onSave;
  
  /// نص زر الحفظ (افتراضي: "حفظ")
  final String saveButtonText;
  
  /// حالة التحميل (افتراضي: false)
  final bool isLoading;
  
  /// إمكانية الحفظ (افتراضي: true)
  final bool canSave;

  const UnifiedFormLayout({
    super.key,
    required this.title,
    required this.userType,
    required this.fields,
    this.onSave,
    this.saveButtonText = 'حفظ',
    this.isLoading = false,
    this.canSave = true,
  });

  @override
  Widget build(BuildContext context) {
    return UnifiedScreenLayout(
      title: title,
      userType: userType,
      isLoading: isLoading,
      loadingMessage: 'جاري الحفظ...',
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.screenPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // حقول النموذج
            ...fields
                .expand((field) => [
                      field,
                      const SizedBox(height: AppSpacing.md),
                    ])
                .toList()
              ..removeLast(), // إزالة المسافة الأخيرة
            
            const SizedBox(height: AppSpacing.sectionSpacing),
            
            // زر الحفظ
            if (onSave != null)
              ElevatedButton.icon(
                onPressed: canSave && !isLoading ? onSave : null,
                icon: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.textOnPrimary,
                          ),
                        ),
                      )
                    : const Icon(Icons.save),
                label: Text(saveButtonText),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    vertical: AppSpacing.lg,
                  ),
                  backgroundColor: _getUserColor(userType),
                  foregroundColor: AppColors.textOnPrimary,
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// الحصول على لون المستخدم
  Color _getUserColor(UserType type) {
    switch (type) {
      case UserType.student:
        return AppColors.studentColor;
      case UserType.parent:
        return AppColors.parentColor;
      case UserType.teacher:
        return AppColors.teacherColor;
      case UserType.admin:
        return AppColors.adminColor;
      case UserType.general:
        return AppColors.primary;
    }
  }
}
