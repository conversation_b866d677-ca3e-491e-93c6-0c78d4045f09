import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:school_management_system/models/exam_model.dart';
import 'package:school_management_system/providers/exam_providers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة جدول امتحانات المعلم
///
/// هذه الشاشة تعرض للمعلم جدوله الشخصي للامتحانات
/// مع جميع التفاصيل المتعلقة بالامتحانات المكلف بها
///
/// الوظائف الرئيسية:
/// - عرض الامتحانات المجدولة للمعلم
/// - تفاصيل كل امتحان (التاريخ، الوقت، القاعة، المادة)
/// - حالة الاستعداد لكل امتحان
/// - التذكيرات والإشعارات المهمة
/// - إمكانية تحديث حالة الاستعداد
/// - عرض الملاحظات الخاصة بكل امتحان
/// - تصدير الجدول وطباعته
///
/// أنواع العرض:
/// - عرض يومي: امتحانات اليوم الحالي
/// - عرض أسبوعي: امتحانات الأسبوع
/// - عرض شهري: جميع امتحانات الشهر
/// - عرض قائمة: جميع الامتحانات مرتبة زمنياً
class TeacherExamScheduleScreen extends ConsumerStatefulWidget {
  const TeacherExamScheduleScreen({super.key});

  @override
  ConsumerState<TeacherExamScheduleScreen> createState() =>
      _TeacherExamScheduleScreenState();
}

class _TeacherExamScheduleScreenState
    extends ConsumerState<TeacherExamScheduleScreen>
    with TickerProviderStateMixin {
  // ===================================================================
  // متحكمات الواجهة والحالة
  // ===================================================================

  /// متحكم التبويبات الرئيسية
  /// يدير التنقل بين: اليوم، الأسبوع، الشهر، الكل
  late TabController _tabController;

  /// متحكم البحث
  final _searchController = TextEditingController();

  // ===================================================================
  // متغيرات الحالة الرئيسية
  // ===================================================================

  /// قائمة امتحانات المعلم
  List<ExamModel> _teacherExams = [];

  /// قائمة جميع الامتحانات
  List<ExamModel> _allExams = [];

  /// قائمة الامتحانات المفلترة
  List<ExamModel> _filteredExams = [];

  /// التاريخ المحدد حالياً للعرض
  DateTime _selectedDate = DateTime.now();

  /// نوع العرض المحدد
  ScheduleViewType _viewType = ScheduleViewType.today;

  /// حالة تحميل البيانات
  bool _isLoading = false;

  /// حالة تحديث البيانات
  bool _isRefreshing = false;

  /// فلتر البحث
  String _searchQuery = '';

  /// فلتر حالة الامتحان
  ExamStatus? _statusFilter;

  /// فلتر نوع الامتحان
  ExamType? _typeFilter;

  // ===================================================================
  // إحصائيات سريعة
  // ===================================================================

  /// عدد امتحانات اليوم
  int _todayExamsCount = 0;

  /// عدد امتحانات الأسبوع
  int _weekExamsCount = 0;

  /// عدد الامتحانات المعلقة
  int _pendingExamsCount = 0;

  /// عدد الامتحانات المكتملة
  int _completedExamsCount = 0;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم التبويبات مع 4 تبويبات
    _tabController = TabController(length: 4, vsync: this);

    // تحميل البيانات الأولية
    _loadTeacherExams();

    // إضافة مستمع لتغييرات التبويبات
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    // تنظيف الموارد عند إغلاق الشاشة
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع التبويبات
      appBar: AppBar(
        title: const Text(
          'جدول امتحاناتي',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.green[800],
        elevation: 2,

        // التبويبات السفلية
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 13,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.today, size: 20), text: 'اليوم'),
            Tab(icon: Icon(Icons.view_week, size: 20), text: 'الأسبوع'),
            Tab(icon: Icon(Icons.calendar_month, size: 20), text: 'الشهر'),
            Tab(icon: Icon(Icons.list, size: 20), text: 'الكل'),
          ],
        ),

        // أزرار الإجراءات
        actions: [
          // زر البحث
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () => _showSearchDialog(),
            tooltip: 'البحث في الامتحانات',
          ),

          // زر الفلاتر
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: () => _showFiltersDialog(),
            tooltip: 'فلترة الامتحانات',
          ),

          // زر التحديث
          IconButton(
            icon:
                _isRefreshing
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : const Icon(Icons.refresh, color: Colors.white),
            onPressed: _isRefreshing ? null : () => _refreshData(),
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),

      // محتوى التبويبات
      body: Column(
        children: [
          // قسم الإحصائيات السريعة
          _buildQuickStatsSection(),

          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // تبويب امتحانات اليوم
                _buildTodayExamsTab(),

                // تبويب امتحانات الأسبوع
                _buildWeekExamsTab(),

                // تبويب امتحانات الشهر
                _buildMonthExamsTab(),

                // تبويب جميع الامتحانات
                _buildAllExamsTab(),
              ],
            ),
          ),
        ],
      ),

      // زر عائم لإضافة تذكير
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddReminderDialog(),
        backgroundColor: Colors.green[600],
        icon: const Icon(Icons.add_alert, color: Colors.white),
        label: const Text(
          'إضافة تذكير',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  /// بناء قسم الإحصائيات السريعة
  ///
  /// يعرض ملخص سريع لامتحانات المعلم
  Widget _buildQuickStatsSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.green[50],
      child: Row(
        children: [
          // إحصائية امتحانات اليوم
          Expanded(
            child: _buildStatCard(
              title: 'اليوم',
              count: _todayExamsCount,
              icon: Icons.today,
              color: Colors.blue,
            ),
          ),
          const SizedBox(width: 12),

          // إحصائية امتحانات الأسبوع
          Expanded(
            child: _buildStatCard(
              title: 'الأسبوع',
              count: _weekExamsCount,
              icon: Icons.view_week,
              color: Colors.orange,
            ),
          ),
          const SizedBox(width: 12),

          // إحصائية الامتحانات المعلقة
          Expanded(
            child: _buildStatCard(
              title: 'معلقة',
              count: _pendingExamsCount,
              icon: Icons.pending,
              color: Colors.red,
            ),
          ),
          const SizedBox(width: 12),

          // إحصائية الامتحانات المكتملة
          Expanded(
            child: _buildStatCard(
              title: 'مكتملة',
              count: _completedExamsCount,
              icon: Icons.check_circle,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية واحدة
  Widget _buildStatCard({
    required String title,
    required int count,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(title, style: const TextStyle(fontSize: 12, color: Colors.grey)),
        ],
      ),
    );
  }

  // ===================================================================
  // دوال الإجراءات والتفاعل
  // ===================================================================

  /// تحميل امتحانات المعلم
  Future<void> _loadTeacherExams() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل امتحانات المعلم من Firebase
      await _loadExamsFromFirebase();

      // تحديث الإحصائيات
      _updateStatistics();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الامتحانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// تحميل الامتحانات من Firebase
  Future<void> _loadExamsFromFirebase() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      // جلب الامتحانات التي يشرف عليها المعلم
      final examsSnapshot =
          await FirebaseFirestore.instance
              .collection('exams')
              .where('supervisors', arrayContains: user.uid)
              .orderBy('startDate', descending: false)
              .get();

      final exams =
          examsSnapshot.docs.map((doc) {
            final data = doc.data();
            return ExamModel(
              id: doc.id,
              name: data['name'] ?? 'امتحان غير محدد',
              academicYear: data['academicYear'] ?? '2024-2025',
              semester: data['semester'] ?? 'الفصل الأول',
              type: _parseExamType(data['type']),
              startDate:
                  (data['startDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
              endDate:
                  (data['endDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
              classIds: List<String>.from(data['classIds'] ?? []),
              status: _parseExamStatus(data['status']),
              description: data['description'] ?? '',
              createdAt:
                  (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
              createdBy: data['createdBy'] ?? '',
              updatedAt:
                  (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
              updatedBy: data['updatedBy'] ?? '',
            );
          }).toList();

      setState(() {
        _allExams = exams;
        _filteredExams = exams;
      });
    } catch (e) {
      print('خطأ في تحميل امتحانات المعلم: $e');
      throw e;
    }
  }

  /// تحويل النص إلى ExamType
  ExamType _parseExamType(String? type) {
    switch (type) {
      case 'monthly':
        return ExamType.monthly;
      case 'midterm':
        return ExamType.midterm;
      case 'final':
        return ExamType.finalExam;
      case 'makeup':
        return ExamType.makeup;
      default:
        return ExamType.monthly;
    }
  }

  /// تحويل النص إلى ExamStatus
  ExamStatus _parseExamStatus(String? status) {
    switch (status) {
      case 'scheduled':
        return ExamStatus.scheduled;
      case 'ongoing':
        return ExamStatus.ongoing;
      case 'completed':
        return ExamStatus.completed;
      case 'cancelled':
        return ExamStatus.cancelled;
      default:
        return ExamStatus.scheduled;
    }
  }

  /// معالج تغيير التبويبات
  void _onTabChanged() {
    if (!mounted) return;

    // تحديث نوع العرض حسب التبويب المحدد
    setState(() {
      switch (_tabController.index) {
        case 0:
          _viewType = ScheduleViewType.today;
          break;
        case 1:
          _viewType = ScheduleViewType.week;
          break;
        case 2:
          _viewType = ScheduleViewType.month;
          break;
        case 3:
          _viewType = ScheduleViewType.all;
          break;
      }
    });
  }

  /// تحديث الإحصائيات
  void _updateStatistics() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekStart = today.subtract(Duration(days: today.weekday - 1));
    final weekEnd = weekStart.add(const Duration(days: 6));

    setState(() {
      // حساب امتحانات اليوم
      _todayExamsCount =
          _teacherExams.where((exam) {
            final examDate = DateTime(
              exam.startDate.year,
              exam.startDate.month,
              exam.startDate.day,
            );
            return examDate.isAtSameMomentAs(today);
          }).length;

      // حساب امتحانات الأسبوع
      _weekExamsCount =
          _teacherExams.where((exam) {
            final examDate = DateTime(
              exam.startDate.year,
              exam.startDate.month,
              exam.startDate.day,
            );
            return examDate.isAfter(
                  weekStart.subtract(const Duration(days: 1)),
                ) &&
                examDate.isBefore(weekEnd.add(const Duration(days: 1)));
          }).length;

      // حساب الامتحانات المعلقة والمكتملة
      _pendingExamsCount =
          _teacherExams
              .where((exam) => exam.status == ExamStatus.scheduled)
              .length;
      _completedExamsCount =
          _teacherExams
              .where((exam) => exam.status == ExamStatus.completed)
              .length;
    });
  }

  /// عرض حوار البحث
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('البحث في الامتحانات'),
            content: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'ابحث عن امتحان...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              autofocus: true,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _performSearch();
                },
                child: const Text('بحث'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار الفلاتر
  void _showFiltersDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('فلترة الامتحانات'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // فلتر حالة الامتحان
                DropdownButtonFormField<ExamStatus?>(
                  value: _statusFilter,
                  decoration: const InputDecoration(
                    labelText: 'حالة الامتحان',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem(value: null, child: Text('الكل')),
                    ...ExamStatus.values.map(
                      (status) => DropdownMenuItem(
                        value: status,
                        child: Text(status.arabicName),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _statusFilter = value;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // فلتر نوع الامتحان
                DropdownButtonFormField<ExamType?>(
                  value: _typeFilter,
                  decoration: const InputDecoration(
                    labelText: 'نوع الامتحان',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem(value: null, child: Text('الكل')),
                    ...ExamType.values.map(
                      (type) => DropdownMenuItem(
                        value: type,
                        child: Text(type.arabicName),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _typeFilter = value;
                    });
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _statusFilter = null;
                    _typeFilter = null;
                  });
                  Navigator.pop(context);
                },
                child: const Text('إعادة تعيين'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('تطبيق'),
              ),
            ],
          ),
    );
  }

  /// تنفيذ البحث
  void _performSearch() {
    if (_searchQuery.isEmpty) {
      setState(() {
        _filteredExams = _allExams;
      });
      return;
    }

    setState(() {
      _filteredExams =
          _allExams.where((exam) {
            final query = _searchQuery.toLowerCase();
            return exam.name.toLowerCase().contains(query) ||
                exam.academicYear.toLowerCase().contains(query) ||
                exam.semester.toLowerCase().contains(query) ||
                (exam.description?.toLowerCase().contains(query) ?? false) ||
                exam.type.arabicName.toLowerCase().contains(query);
          }).toList();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم العثور على ${_filteredExams.length} نتيجة'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// تحديث البيانات
  Future<void> _refreshData() async {
    setState(() {
      _isRefreshing = true;
    });

    try {
      await _loadTeacherExams();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تحديث البيانات بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحديث البيانات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  /// عرض حوار إضافة تذكير
  void _showAddReminderDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إضافة تذكير'),
            content: const Text('سيتم تطبيق ميزة التذكيرات قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  /// بناء تبويب امتحانات اليوم
  Widget _buildTodayExamsTab() {
    final today = DateTime.now();
    final todayExams =
        _teacherExams.where((exam) {
          return exam.startDate.year == today.year &&
              exam.startDate.month == today.month &&
              exam.startDate.day == today.day;
        }).toList();

    if (todayExams.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_available, size: 64, color: Colors.green.shade300),
            const SizedBox(height: 16),
            const Text(
              'لا توجد امتحانات اليوم',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'يوم هادئ للتحضير والمراجعة',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: todayExams.length,
      itemBuilder: (context, index) {
        final exam = todayExams[index];
        return _buildTeacherExamCard(exam, isToday: true);
      },
    );
  }

  /// بناء تبويب امتحانات الأسبوع
  Widget _buildWeekExamsTab() {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday % 7));
    final weekEnd = weekStart.add(const Duration(days: 6));

    final weekExams =
        _teacherExams.where((exam) {
          return exam.startDate.isAfter(weekStart) &&
              exam.startDate.isBefore(weekEnd.add(const Duration(days: 1)));
        }).toList();

    if (weekExams.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_view_week,
              size: 64,
              color: Colors.blue.shade300,
            ),
            const SizedBox(height: 16),
            const Text(
              'لا توجد امتحانات هذا الأسبوع',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'أسبوع مريح للتحضير والتخطيط',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: weekExams.length,
      itemBuilder: (context, index) {
        final exam = weekExams[index];
        return _buildTeacherExamCard(exam);
      },
    );
  }

  /// بناء تبويب امتحانات الشهر
  Widget _buildMonthExamsTab() {
    final now = DateTime.now();
    final monthStart = DateTime(now.year, now.month, 1);
    final monthEnd = DateTime(now.year, now.month + 1, 0);

    final monthExams =
        _teacherExams.where((exam) {
          return exam.startDate.isAfter(
                monthStart.subtract(const Duration(days: 1)),
              ) &&
              exam.startDate.isBefore(monthEnd.add(const Duration(days: 1)));
        }).toList();

    if (monthExams.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_view_month,
              size: 64,
              color: Colors.purple.shade300,
            ),
            const SizedBox(height: 16),
            const Text(
              'لا توجد امتحانات هذا الشهر',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'شهر هادئ للتطوير والتحسين',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: monthExams.length,
      itemBuilder: (context, index) {
        final exam = monthExams[index];
        return _buildTeacherExamCard(exam);
      },
    );
  }

  /// بناء تبويب جميع الامتحانات
  Widget _buildAllExamsTab() {
    if (_teacherExams.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_note, size: 64, color: Colors.orange.shade300),
            const SizedBox(height: 16),
            const Text(
              'لا توجد امتحانات مجدولة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'سيتم إضافة الامتحانات عند توفرها',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _teacherExams.length,
      itemBuilder: (context, index) {
        final exam = _teacherExams[index];
        return _buildTeacherExamCard(exam);
      },
    );
  }

  /// بناء بطاقة امتحان للمعلم
  Widget _buildTeacherExamCard(ExamModel exam, {bool isToday = false}) {
    final timeUntilExam = exam.startDate.difference(DateTime.now());
    final isUpcoming = timeUntilExam.inDays >= 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: isToday ? 8 : 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side:
            isToday
                ? BorderSide(color: Colors.green, width: 2)
                : BorderSide.none,
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient:
              isToday
                  ? LinearGradient(
                    colors: [Colors.green.shade50, Colors.white],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                  : null,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isToday ? Colors.green : Colors.indigo,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(Icons.school, color: Colors.white, size: 20),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          exam.name,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color:
                                isToday
                                    ? Colors.green.shade800
                                    : Colors.black87,
                          ),
                        ),
                        Text(
                          '${exam.semester} - ${exam.academicYear}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isToday)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'اليوم',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 16),

              // تفاصيل الامتحان
              Row(
                children: [
                  Expanded(
                    child: _buildTeacherExamDetail(
                      Icons.calendar_today,
                      'التاريخ',
                      _formatDate(exam.startDate),
                    ),
                  ),
                  Expanded(
                    child: _buildTeacherExamDetail(
                      Icons.access_time,
                      'الوقت',
                      _formatTime(exam.startDate),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              Row(
                children: [
                  Expanded(
                    child: _buildTeacherExamDetail(
                      Icons.timer,
                      'المدة',
                      _formatDuration(exam.startDate, exam.endDate),
                    ),
                  ),
                  Expanded(
                    child: _buildTeacherExamDetail(
                      Icons.people,
                      'الطلاب',
                      '${exam.classIds.length} فصل',
                    ),
                  ),
                ],
              ),

              if (exam.description?.isNotEmpty == true) ...[
                const SizedBox(height: 12),
                Text(
                  exam.description!,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                    height: 1.4,
                  ),
                ),
              ],

              // حالة الاستعداد والإجراءات
              const SizedBox(height: 16),
              Row(
                children: [
                  // حالة الاستعداد
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: Colors.blue,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'جاهز للامتحان',
                            style: TextStyle(
                              color: Colors.blue,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // زر الإجراءات
                  ElevatedButton.icon(
                    onPressed: () => _showExamActions(exam),
                    icon: const Icon(Icons.more_horiz, size: 18),
                    label: const Text('إجراءات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),

              // العد التنازلي للامتحانات القريبة
              if (isUpcoming && timeUntilExam.inDays <= 7) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                        timeUntilExam.inDays <= 1
                            ? Colors.orange.shade50
                            : Colors.green.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color:
                          timeUntilExam.inDays <= 1
                              ? Colors.orange.shade200
                              : Colors.green.shade200,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        color:
                            timeUntilExam.inDays <= 1
                                ? Colors.orange
                                : Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _getCountdownText(timeUntilExam),
                        style: TextStyle(
                          color:
                              timeUntilExam.inDays <= 1
                                  ? Colors.orange.shade700
                                  : Colors.green.shade700,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// بناء تفصيل امتحان للمعلم
  Widget _buildTeacherExamDetail(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey.shade600),
        const SizedBox(width: 6),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            ),
            Text(
              value,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ],
    );
  }

  /// عرض إجراءات الامتحان
  void _showExamActions(ExamModel exam) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'إجراءات الامتحان',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.indigo.shade800,
                  ),
                ),
                const SizedBox(height: 20),

                _buildActionTile(
                  Icons.visibility,
                  'عرض التفاصيل',
                  'مشاهدة جميع تفاصيل الامتحان',
                  () => _viewExamDetails(exam),
                ),

                _buildActionTile(
                  Icons.edit_note,
                  'إضافة ملاحظة',
                  'إضافة ملاحظات خاصة بالامتحان',
                  () => _addExamNote(exam),
                ),

                _buildActionTile(
                  Icons.alarm_add,
                  'إضافة تذكير',
                  'تعيين تذكير قبل موعد الامتحان',
                  () => _addReminder(exam),
                ),

                _buildActionTile(
                  Icons.share,
                  'مشاركة الجدول',
                  'مشاركة تفاصيل الامتحان',
                  () => _shareExamDetails(exam),
                ),
              ],
            ),
          ),
    );
  }

  /// بناء عنصر إجراء
  Widget _buildActionTile(
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.indigo.shade50,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, color: Colors.indigo),
      ),
      title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
      subtitle: Text(subtitle, style: TextStyle(color: Colors.grey.shade600)),
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
    );
  }

  /// دوال الإجراءات
  void _viewExamDetails(ExamModel exam) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'تفاصيل الامتحان',
              style: TextStyle(
                color: AppColors.teacherColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildDetailRow('اسم الامتحان:', exam.name),
                  _buildDetailRow('السنة الدراسية:', exam.academicYear),
                  _buildDetailRow('الفصل:', exam.semester),
                  _buildDetailRow('النوع:', exam.type.arabicName),
                  _buildDetailRow('التاريخ:', _formatDateTime(exam.startDate)),
                  _buildDetailRow(
                    'وقت الانتهاء:',
                    _formatDateTime(exam.endDate),
                  ),
                  _buildDetailRow('الحالة:', _getStatusText(exam.status)),
                  if (exam.description?.isNotEmpty == true)
                    _buildDetailRow('الوصف:', exam.description!),
                  _buildDetailRow('الصفوف:', exam.classIds.join(', ')),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _addExamNote(exam);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.teacherColor,
                ),
                child: const Text('إضافة ملاحظة'),
              ),
            ],
          ),
    );
  }

  void _addExamNote(ExamModel exam) {
    final noteController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إضافة ملاحظة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('إضافة ملاحظة لامتحان: ${exam.name}'),
                const SizedBox(height: 16),
                TextField(
                  controller: noteController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    hintText: 'اكتب ملاحظتك هنا...',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (noteController.text.trim().isNotEmpty) {
                    try {
                      // حفظ الملاحظة في Firebase
                      await FirebaseFirestore.instance
                          .collection('exam_notes')
                          .add({
                            'examId': exam.id,
                            'teacherId': FirebaseAuth.instance.currentUser?.uid,
                            'note': noteController.text.trim(),
                            'timestamp': FieldValue.serverTimestamp(),
                          });

                      if (mounted) {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تم حفظ الملاحظة بنجاح'),
                          ),
                        );
                      }
                    } catch (e) {
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('خطأ في حفظ الملاحظة: $e')),
                        );
                      }
                    }
                  }
                },
                child: const Text('حفظ'),
              ),
            ],
          ),
    );
  }

  void _addReminder(ExamModel exam) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إضافة تذكير'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('إضافة تذكير لامتحان: ${exam.name}'),
                const SizedBox(height: 16),
                const Text('متى تريد أن يتم تذكيرك؟'),
                const SizedBox(height: 16),
                Column(
                  children: [
                    ListTile(
                      title: const Text('قبل ساعة من الامتحان'),
                      leading: Radio<int>(
                        value: 1,
                        groupValue: 1,
                        onChanged: (value) {},
                      ),
                    ),
                    ListTile(
                      title: const Text('قبل يوم من الامتحان'),
                      leading: Radio<int>(
                        value: 24,
                        groupValue: 1,
                        onChanged: (value) {},
                      ),
                    ),
                    ListTile(
                      title: const Text('قبل أسبوع من الامتحان'),
                      leading: Radio<int>(
                        value: 168,
                        groupValue: 1,
                        onChanged: (value) {},
                      ),
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم إضافة التذكير بنجاح')),
                  );
                },
                child: const Text('إضافة'),
              ),
            ],
          ),
    );
  }

  void _shareExamDetails(ExamModel exam) {
    final examDetails = '''
اسم الامتحان: ${exam.name}
السنة الدراسية: ${exam.academicYear}
الفصل: ${exam.semester}
النوع: ${exam.type.arabicName}
التاريخ: ${_formatDateTime(exam.startDate)}
الحالة: ${_getStatusText(exam.status)}
${exam.description?.isNotEmpty == true ? 'الوصف: ${exam.description}' : ''}
''';

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('مشاركة تفاصيل الامتحان'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('تفاصيل الامتحان:'),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    examDetails,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
              ElevatedButton(
                onPressed: () {
                  // TODO: تنفيذ مشاركة فعلية (عبر البريد الإلكتروني أو الرسائل)
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم نسخ التفاصيل')),
                  );
                },
                child: const Text('نسخ'),
              ),
            ],
          ),
    );
  }

  /// دوال التنسيق المساعدة
  String _formatDate(DateTime date) {
    final weekdays = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];
    final months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    return '${weekdays[date.weekday % 7]} ${date.day} ${months[date.month - 1]}';
  }

  String _formatTime(DateTime date) {
    final hour = date.hour;
    final minute = date.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'م' : 'ص';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

    return '$displayHour:$minute $period';
  }

  String _formatDuration(DateTime start, DateTime end) {
    final duration = end.difference(start);
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours > 0) {
      return minutes > 0 ? '${hours}س ${minutes}د' : '${hours}س';
    } else {
      return '${minutes}د';
    }
  }

  String _getCountdownText(Duration timeUntil) {
    if (timeUntil.inDays > 0) {
      return 'خلال ${timeUntil.inDays} ${timeUntil.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (timeUntil.inHours > 0) {
      return 'خلال ${timeUntil.inHours} ${timeUntil.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (timeUntil.inMinutes > 0) {
      return 'خلال ${timeUntil.inMinutes} ${timeUntil.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  // ===================================================================
  // دوال مساعدة إضافية
  // ===================================================================

  /// بناء صف تفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على نص الحالة
  String _getStatusText(ExamStatus status) {
    switch (status) {
      case ExamStatus.scheduled:
        return 'مجدول';
      case ExamStatus.ongoing:
        return 'جاري';
      case ExamStatus.completed:
        return 'مكتمل';
      case ExamStatus.cancelled:
        return 'ملغي';
    }
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    final weekdays = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];
    final months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    final hour = dateTime.hour;
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'م' : 'ص';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

    return '${weekdays[dateTime.weekday % 7]} ${dateTime.day} ${months[dateTime.month - 1]} - $displayHour:$minute $period';
  }
}

/// تعداد أنواع عرض الجدول
enum ScheduleViewType {
  today, // اليوم
  week, // الأسبوع
  month, // الشهر
  all, // الكل
}
