import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/exam_model.dart';
import 'package:school_management_system/providers/exam_providers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/utils/async_error_handler.dart';

/// شاشة جدول امتحانات الطالب
///
/// هذه الشاشة تعرض للطالب جدوله الشخصي للامتحانات القادمة
/// مع جميع التفاصيل والمعلومات المهمة للاستعداد
///
/// الوظائف الرئيسية:
/// - عرض الامتحانات القادمة مرتبة زمنياً
/// - تفاصيل كل امتحان (التاريخ، الوقت، القاعة، المدة)
/// - حالة استعداد الطالب لكل امتحان
/// - التذكيرات والإشعارات المهمة
/// - العد التنازلي للامتحانات القريبة
/// - إمكانية إضافة ملاحظات شخصية
/// - ربط مع شاشة الاستعداد للامتحان
/// - تصدير الجدول وطباعته
/// - مشاركة الجدول مع الأهل
///
/// أنواع العرض:
/// - عرض يومي: امتحانات اليوم الحالي
/// - عرض أسبوعي: امتحانات الأسبوع
/// - عرض شهري: جميع امتحانات الشهر
/// - عرض قائمة: جميع الامتحانات مرتبة زمنياً
///
/// التصميم والواجهة:
/// - ألوان مريحة ومحفزة للطلاب
/// - تنظيم واضح للمعلومات
/// - مؤشرات بصرية للأولوية والوقت
/// - تصميم متجاوب لجميع الأحجام
/// - تفاعل سلس ومريح
class StudentExamScheduleScreen extends ConsumerStatefulWidget {
  /// معرف الطالب الذي يعرض جدوله
  /// يستخدم لتحميل الامتحانات الخاصة بالطالب فقط
  final String studentId;

  const StudentExamScheduleScreen({super.key, required this.studentId});

  @override
  ConsumerState<StudentExamScheduleScreen> createState() =>
      _StudentExamScheduleScreenState();
}

class _StudentExamScheduleScreenState
    extends ConsumerState<StudentExamScheduleScreen>
    with TickerProviderStateMixin {
  // ===================================================================
  // متحكمات الواجهة والرسوم المتحركة
  // ===================================================================

  /// متحكم التبويبات الرئيسية
  /// يدير التنقل بين: اليوم، الأسبوع، الشهر، الكل
  late TabController _tabController;

  /// متحكم الرسوم المتحركة للعد التنازلي
  /// يستخدم لإظهار الوقت المتبقي بشكل متحرك وجذاب
  late AnimationController _countdownAnimationController;

  /// الرسم المتحرك للعد التنازلي
  late Animation<double> _countdownAnimation;

  /// متحكم البحث في الامتحانات
  final TextEditingController _searchController = TextEditingController();

  /// متحكم إدخال الملاحظات
  final TextEditingController _noteController = TextEditingController();

  // ===================================================================
  // متغيرات البيانات الرئيسية
  // ===================================================================

  /// قائمة امتحانات الطالب
  List<ExamModel> _studentExams = [];

  /// التاريخ المحدد حالياً للعرض
  DateTime _selectedDate = DateTime.now();

  /// نوع العرض المحدد (اليوم، الأسبوع، الشهر، الكل)
  ScheduleViewType _viewType = ScheduleViewType.today;

  /// خريطة ملاحظات الطالب لكل امتحان
  /// المفتاح: معرف الامتحان، القيمة: نص الملاحظة
  Map<String, String> _examNotes = {};

  /// خريطة حالة استعداد الطالب لكل امتحان
  /// المفتاح: معرف الامتحان، القيمة: مستوى الاستعداد (1-5)
  Map<String, int> _preparationLevel = {};

  /// خريطة التذكيرات المفعلة لكل امتحان
  /// المفتاح: معرف الامتحان، القيمة: قائمة أوقات التذكير
  Map<String, List<DateTime>> _examReminders = {};

  // ===================================================================
  // متغيرات حالة التحميل والتفاعل
  // ===================================================================

  /// حالة تحميل البيانات الأولية
  bool _isLoading = false;

  /// حالة تحديث البيانات
  bool _isRefreshing = false;

  /// حالة حفظ الملاحظات والإعدادات
  bool _isSaving = false;

  /// نص البحث الحالي
  String _searchQuery = '';

  /// فلتر حالة الامتحان (الكل، قادم، اليوم، منتهي)
  ExamStatusFilter _statusFilter = ExamStatusFilter.upcoming;

  /// فلتر نوع الامتحان (الكل، نهائي، نصفي، شهري)
  ExamType? _typeFilter;

  /// فلتر المادة (الكل، مادة محددة)
  String? _subjectFilter;

  /// ترتيب الامتحانات (التاريخ، المادة، الأهمية)
  ExamSort _examSort = ExamSort.date;

  // ===================================================================
  // إحصائيات سريعة
  // ===================================================================

  /// عدد امتحانات اليوم
  int _todayExamsCount = 0;

  /// عدد امتحانات الأسبوع
  int _weekExamsCount = 0;

  /// عدد الامتحانات القادمة
  int _upcomingExamsCount = 0;

  /// عدد الامتحانات المكتملة
  int _completedExamsCount = 0;

  /// الامتحان القادم التالي
  ExamModel? _nextExam;

  /// الوقت المتبقي للامتحان القادم (بالساعات)
  int _hoursUntilNextExam = 0;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم التبويبات مع 4 تبويبات
    _tabController = TabController(length: 4, vsync: this);

    // إنشاء متحكم الرسوم المتحركة للعد التنازلي
    _countdownAnimationController = AnimationController(
      duration: const Duration(seconds: 1), // تحديث كل ثانية
      vsync: this,
    );

    // إنشاء الرسم المتحرك للعد التنازلي
    _countdownAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _countdownAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // تحميل البيانات الأولية
    _loadStudentExams();

    // إضافة مستمع لتغييرات التبويبات
    _tabController.addListener(_onTabChanged);

    // بدء العد التنازلي المتكرر
    _startCountdownTimer();
  }

  @override
  void dispose() {
    // تنظيف الموارد عند إغلاق الشاشة
    _tabController.dispose();
    _countdownAnimationController.dispose();
    _searchController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع التبويبات والإجراءات
      appBar: AppBar(
        title: const Text(
          'جدول امتحاناتي',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.indigo[800], // لون مميز لجدول الطالب
        elevation: 2,

        // التبويبات السفلية في شريط التطبيق
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 13,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.today, size: 20), text: 'اليوم'),
            Tab(icon: Icon(Icons.view_week, size: 20), text: 'الأسبوع'),
            Tab(icon: Icon(Icons.calendar_month, size: 20), text: 'الشهر'),
            Tab(icon: Icon(Icons.list, size: 20), text: 'الكل'),
          ],
        ),

        // أزرار الإجراءات في شريط التطبيق
        actions: [
          // زر البحث في الامتحانات
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () => _showSearchDialog(),
            tooltip: 'البحث في الامتحانات',
          ),

          // زر الفلاتر والترتيب
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: () => _showFilterDialog(),
            tooltip: 'فلترة وترتيب الامتحانات',
          ),

          // زر التذكيرات
          IconButton(
            icon: const Icon(Icons.notifications, color: Colors.white),
            onPressed: () => _showRemindersDialog(),
            tooltip: 'إدارة التذكيرات',
          ),

          // زر التحديث
          if (_isRefreshing)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: () => _refreshData(),
              tooltip: 'تحديث البيانات',
            ),
        ],
      ),

      // محتوى التبويبات الرئيسية
      body: Column(
        children: [
          // قسم الإحصائيات السريعة والامتحان القادم
          _buildQuickStatsSection(),

          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // تبويب امتحانات اليوم
                _buildTodayExamsTab(),

                // تبويب امتحانات الأسبوع
                _buildWeekExamsTab(),

                // تبويب امتحانات الشهر
                _buildMonthExamsTab(),

                // تبويب جميع الامتحانات
                _buildAllExamsTab(),
              ],
            ),
          ),
        ],
      ),

      // شريط المعلومات السفلي
      bottomNavigationBar: _buildBottomInfoBar(),

      // زر عائم لإضافة ملاحظة أو تذكير
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showQuickActionsDialog(),
        backgroundColor: Colors.indigo[600],
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text(
          'إجراء سريع',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  // ===================================================================
  // دوال تحميل البيانات والتحليل
  // ===================================================================

  /// تحميل امتحانات الطالب من قاعدة البيانات
  ///
  /// هذه الدالة تقوم بتحميل جميع البيانات المطلوبة:
  /// - امتحانات الطالب المجدولة
  /// - ملاحظات الطالب المحفوظة
  /// - مستويات الاستعداد المسجلة
  /// - التذكيرات المفعلة
  /// - حساب الإحصائيات
  Future<void> _loadStudentExams() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: تحميل البيانات الفعلية من قاعدة البيانات
      // هنا سيتم استدعاء الخدمات لجلب:
      // 1. امتحانات الطالب من جدول الامتحانات
      // 2. ملاحظات الطالب المحفوظة
      // 3. مستويات الاستعداد والتذكيرات

      // محاكاة تحميل البيانات
      await Future.delayed(const Duration(seconds: 1));

      // محاكاة بيانات الامتحانات
      _simulateExamData();

      // تحديث الإحصائيات
      _updateStatistics();

      // العثور على الامتحان القادم
      _findNextExam();
    } catch (e) {
      // استخدام نظام معالجة الأخطاء المحسن
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('خطأ في تحميل الامتحانات: $e')),
              ],
            ),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _loadStudentExams(),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// محاكاة بيانات الامتحانات للاختبار
  ///
  /// هذه الدالة تنشئ بيانات وهمية لاختبار الواجهة
  /// في التطبيق الفعلي ستحل محلها البيانات من قاعدة البيانات
  void _simulateExamData() {
    final now = DateTime.now();

    // محاكاة امتحانات متنوعة
    _studentExams = [
      ExamModel(
        id: 'exam1',
        name: 'امتحان الفصل الأول - الرياضيات',
        academicYear: '2024-2025',
        semester: 'الفصل الأول',
        type: ExamType.midterm,
        startDate: now.add(const Duration(days: 2)),
        endDate: now.add(const Duration(days: 2, hours: 2)),
        classIds: ['class1'],
        status: ExamStatus.scheduled,
        description: 'امتحان نصف الفصل في مادة الرياضيات',
        createdAt: now.subtract(const Duration(days: 10)),
        createdBy: 'teacher1',
        updatedAt: now.subtract(const Duration(days: 1)),
        updatedBy: 'teacher1',
      ),
      ExamModel(
        id: 'exam2',
        name: 'امتحان الفصل الأول - العلوم',
        academicYear: '2024-2025',
        semester: 'الفصل الأول',
        type: ExamType.monthly,
        startDate: now.add(const Duration(days: 5)),
        endDate: now.add(const Duration(days: 5, hours: 1, minutes: 30)),
        classIds: ['class1'],
        status: ExamStatus.scheduled,
        description: 'امتحان شهري في مادة العلوم',
        createdAt: now.subtract(const Duration(days: 15)),
        createdBy: 'teacher2',
        updatedAt: now.subtract(const Duration(days: 2)),
        updatedBy: 'teacher2',
      ),
      ExamModel(
        id: 'exam3',
        name: 'امتحان الفصل الأول - اللغة العربية',
        academicYear: '2024-2025',
        semester: 'الفصل الأول',
        type: ExamType.finalExam,
        startDate: now.add(const Duration(days: 14)),
        endDate: now.add(const Duration(days: 14, hours: 3)),
        classIds: ['class1'],
        status: ExamStatus.scheduled,
        description: 'امتحان نهاية الفصل في اللغة العربية',
        createdAt: now.subtract(const Duration(days: 20)),
        createdBy: 'teacher3',
        updatedAt: now.subtract(const Duration(days: 3)),
        updatedBy: 'teacher3',
      ),
    ];

    // محاكاة ملاحظات الطالب
    _examNotes = {
      'exam1': 'مراجعة الهندسة والجبر',
      'exam2': 'التركيز على الفيزياء',
      'exam3': 'مراجعة القواعد والأدب',
    };

    // محاكاة مستويات الاستعداد
    _preparationLevel = {
      'exam1': 3, // متوسط
      'exam2': 4, // جيد
      'exam3': 2, // ضعيف
    };
  }

  /// تحديث الإحصائيات والحسابات
  ///
  /// يحسب جميع الإحصائيات المطلوبة لعرض الملخص:
  /// - عدد امتحانات اليوم والأسبوع
  /// - عدد الامتحانات القادمة والمكتملة
  /// - الوقت المتبقي للامتحان القادم
  void _updateStatistics() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekStart = today.subtract(Duration(days: today.weekday - 1));
    final weekEnd = weekStart.add(const Duration(days: 6));

    // حساب امتحانات اليوم
    _todayExamsCount =
        _studentExams.where((exam) {
          final examDate = DateTime(
            exam.startDate.year,
            exam.startDate.month,
            exam.startDate.day,
          );
          return examDate.isAtSameMomentAs(today);
        }).length;

    // حساب امتحانات الأسبوع
    _weekExamsCount =
        _studentExams.where((exam) {
          final examDate = DateTime(
            exam.startDate.year,
            exam.startDate.month,
            exam.startDate.day,
          );
          return examDate.isAfter(
                weekStart.subtract(const Duration(days: 1)),
              ) &&
              examDate.isBefore(weekEnd.add(const Duration(days: 1)));
        }).length;

    // حساب الامتحانات القادمة والمكتملة
    _upcomingExamsCount =
        _studentExams
            .where(
              (exam) =>
                  exam.startDate.isAfter(now) &&
                  exam.status == ExamStatus.scheduled,
            )
            .length;
    _completedExamsCount =
        _studentExams
            .where((exam) => exam.status == ExamStatus.completed)
            .length;

    setState(() {});
  }

  /// العثور على الامتحان القادم التالي
  ///
  /// يحدد الامتحان الأقرب زمنياً ويحسب الوقت المتبقي له
  void _findNextExam() {
    final now = DateTime.now();

    // البحث عن أقرب امتحان قادم
    final upcomingExams =
        _studentExams.where((exam) => exam.startDate.isAfter(now)).toList();

    if (upcomingExams.isNotEmpty) {
      // ترتيب الامتحانات حسب التاريخ
      upcomingExams.sort((a, b) => a.startDate.compareTo(b.startDate));
      _nextExam = upcomingExams.first;

      // حساب الوقت المتبقي بالساعات
      final timeDifference = _nextExam!.startDate.difference(now);
      _hoursUntilNextExam = timeDifference.inHours;
    } else {
      _nextExam = null;
      _hoursUntilNextExam = 0;
    }
  }

  /// بدء مؤقت العد التنازلي المتكرر
  ///
  /// يحدث العد التنازلي كل دقيقة لإظهار الوقت المتبقي بدقة
  void _startCountdownTimer() {
    // تحديث العد التنازلي كل دقيقة
    Future.delayed(const Duration(minutes: 1), () {
      if (mounted) {
        _findNextExam();
        _startCountdownTimer(); // إعادة جدولة التحديث
      }
    });
  }

  // ===================================================================
  // معالجات الأحداث والحوارات
  // ===================================================================

  /// معالج تغيير التبويبات
  void _onTabChanged() {
    if (!mounted) return;

    // تحديث نوع العرض حسب التبويب المحدد
    setState(() {
      switch (_tabController.index) {
        case 0:
          _viewType = ScheduleViewType.today;
          break;
        case 1:
          _viewType = ScheduleViewType.week;
          break;
        case 2:
          _viewType = ScheduleViewType.month;
          break;
        case 3:
          _viewType = ScheduleViewType.all;
          break;
      }
    });
  }

  /// عرض حوار البحث
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('البحث في الامتحانات'),
            content: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'ابحث عن امتحان أو مادة...',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('بحث'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار الفلاتر
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('فلترة وترتيب الامتحانات'),
            content: const Text('سيتم تطبيق خيارات الفلترة والترتيب قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار التذكيرات
  void _showRemindersDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إدارة التذكيرات'),
            content: const Text('سيتم تطبيق ميزة التذكيرات قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  /// تحديث البيانات
  Future<void> _refreshData() async {
    setState(() {
      _isRefreshing = true;
    });

    // استخدام نظام معالجة الأخطاء المحسن
    await AsyncErrorHandler.execute<void>(
      operation: () => _loadStudentExams(),
      context: context,
      successMessage: 'تم تحديث البيانات بنجاح',
      showSuccessMessage: true,
      onError: () {
        // يمكن إضافة منطق إضافي عند الفشل
      },
    );

    // إعادة تعيين حالة التحديث
    if (mounted) {
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  /// عرض حوار الإجراءات السريعة
  void _showQuickActionsDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إجراء سريع'),
            content: const Text('سيتم تطبيق الإجراءات السريعة قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  // ===================================================================
  // دوال بناء المكونات الرئيسية
  // ===================================================================

  /// بناء قسم الإحصائيات السريعة
  Widget _buildQuickStatsSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.indigo[50],
      child: Column(
        children: [
          // الامتحان القادم
          if (_nextExam != null) _buildNextExamCard(),
          const SizedBox(height: 16),

          // الإحصائيات السريعة
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'اليوم',
                  _todayExamsCount,
                  Icons.today,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'الأسبوع',
                  _weekExamsCount,
                  Icons.view_week,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'قادمة',
                  _upcomingExamsCount,
                  Icons.schedule,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'مكتملة',
                  _completedExamsCount,
                  Icons.check_circle,
                  Colors.purple,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الامتحان القادم
  Widget _buildNextExamCard() {
    if (_nextExam == null) return const SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.indigo[600]!, Colors.indigo[800]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الامتحان القادم',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white70,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            _nextExam!.name,
            style: const TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.access_time, color: Colors.indigo[100], size: 16),
              const SizedBox(width: 4),
              Text(
                'خلال $_hoursUntilNextExam ساعة',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.indigo[100],
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Icon(Icons.calendar_today, color: Colors.indigo[100], size: 16),
              const SizedBox(width: 4),
              Text(
                '${_nextExam!.startDate.day}/${_nextExam!.startDate.month}',
                style: TextStyle(fontSize: 14, color: Colors.indigo[100]),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(String title, int count, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(title, style: const TextStyle(fontSize: 11, color: Colors.grey)),
        ],
      ),
    );
  }

  /// بناء التبويبات
  Widget _buildTodayExamsTab() {
    return const Center(
      child: Text(
        'امتحانات اليوم\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  Widget _buildWeekExamsTab() {
    return const Center(
      child: Text(
        'امتحانات الأسبوع\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  Widget _buildMonthExamsTab() {
    return const Center(
      child: Text(
        'امتحانات الشهر\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  Widget _buildAllExamsTab() {
    return const Center(
      child: Text(
        'جميع الامتحانات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء شريط المعلومات السفلي
  Widget _buildBottomInfoBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.indigo[50],
        border: Border(top: BorderSide(color: Colors.indigo[200]!)),
      ),
      child: Row(
        children: [
          Icon(Icons.schedule, size: 16, color: Colors.indigo[600]),
          const SizedBox(width: 4),
          Text(
            'قادمة: $_upcomingExamsCount',
            style: TextStyle(fontSize: 12, color: Colors.indigo[600]),
          ),
          const Spacer(),
          if (_nextExam != null) ...[
            Icon(Icons.access_time, size: 16, color: Colors.indigo[600]),
            const SizedBox(width: 4),
            Text(
              'القادم خلال $_hoursUntilNextExam ساعة',
              style: TextStyle(fontSize: 12, color: Colors.indigo[600]),
            ),
          ],
        ],
      ),
    );
  }
}

/// تعداد أنواع عرض الجدول
enum ScheduleViewType {
  today, // اليوم
  week, // الأسبوع
  month, // الشهر
  all, // الكل
}

/// تعداد فلاتر حالة الامتحان
enum ExamStatusFilter {
  all, // جميع الامتحانات
  upcoming, // القادمة
  today, // اليوم
  completed, // المكتملة
}

/// تعداد ترتيب الامتحانات
enum ExamSort {
  date, // حسب التاريخ
  subject, // حسب المادة
  importance, // حسب الأهمية
}
