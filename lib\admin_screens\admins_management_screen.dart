import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/providers/admin_providers.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/utils/helpers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/admin_screens/widgets/admin_form_dialog.dart';

/// شاشة إدارة حسابات المسؤولين المعاد هيكلتها.
class AdminsManagementScreen extends ConsumerWidget {
  const AdminsManagementScreen({super.key});

  /// دالة لعرض ديالوج تأكيد الحذف
  void _showDeleteConfirmationDialog(
    BuildContext context,
    WidgetRef ref,
    UserModel admin,
  ) async {
    final confirmed = await showConfirmationDialog(
      context: context,
      title: 'تأكيد الحذف',
      content:
          'هل أنت متأكد أنك تريد حذف المسؤول "${admin.name}"؟ هذا الإجراء لا يمكن التراجع عنه.',
    );

    if (confirmed) {
      try {
        await ref.read(firebaseServiceProvider).deleteAdmin(admin.id);
        showSuccessSnackBar(context, 'تم حذف المسؤول بنجاح.');
      } catch (e) {
        showErrorSnackBar(context, 'حدث خطأ: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final adminsAsyncValue = ref.watch(adminsStreamProvider);
    final filteredAdmins = ref.watch(filteredAdminsProvider);

    return Scaffold(
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              decoration: const InputDecoration(
                labelText: 'ابحث بالاسم، البريد، الهاتف، أو المحافظة...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
                helperText: 'يمكنك البحث في جميع الحقول',
              ),
              onChanged: (value) {
                ref.read(adminSearchQueryProvider.notifier).state = value;
              },
            ),
          ),
          Expanded(
            child: adminsAsyncValue.when(
              data: (_) {
                if (filteredAdmins.isEmpty) {
                  return const Center(child: Text('لم يتم العثور على نتائج.'));
                }
                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: filteredAdmins.length,
                  itemBuilder: (context, index) {
                    final admin = filteredAdmins[index];
                    return CustomCard(
                      child: InkWell(
                        onTap: () => _showAdminDetails(context, admin),
                        borderRadius: BorderRadius.circular(8),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Row(
                            children: [
                              // ===== الصورة الشخصية =====
                              CircleAvatar(
                                radius: 30,
                                backgroundImage:
                                    (admin.profileImageUrl != null &&
                                            admin.profileImageUrl!.isNotEmpty)
                                        ? NetworkImage(admin.profileImageUrl!)
                                        : null,
                                child:
                                    (admin.profileImageUrl == null ||
                                            admin.profileImageUrl!.isEmpty)
                                        ? const Icon(
                                          Icons.admin_panel_settings,
                                          size: 30,
                                        )
                                        : null,
                              ),
                              const SizedBox(width: 16),

                              // ===== معلومات المدير =====
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      admin.name,
                                      style:
                                          Theme.of(
                                            context,
                                          ).textTheme.titleLarge,
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        const Icon(
                                          Icons.email,
                                          size: 16,
                                          color: Colors.grey,
                                        ),
                                        const SizedBox(width: 4),
                                        Expanded(
                                          child: Text(
                                            admin.email,
                                            style:
                                                Theme.of(
                                                  context,
                                                ).textTheme.bodySmall,
                                          ),
                                        ),
                                      ],
                                    ),

                                    if (admin.phoneNumber != null &&
                                        admin.phoneNumber!.isNotEmpty) ...[
                                      const SizedBox(height: 4),
                                      Row(
                                        children: [
                                          const Icon(
                                            Icons.phone,
                                            size: 16,
                                            color: Colors.grey,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            admin.phoneNumber!,
                                            style:
                                                Theme.of(
                                                  context,
                                                ).textTheme.bodySmall,
                                          ),
                                        ],
                                      ),
                                    ],

                                    const SizedBox(height: 8),

                                    // ===== رقاقات المعلومات =====
                                    Wrap(
                                      spacing: 6,
                                      runSpacing: 4,
                                      children: [
                                        // رقاقة الدور
                                        Chip(
                                          label: const Text('مدير'),
                                          backgroundColor: Colors.red.shade100,
                                          materialTapTargetSize:
                                              MaterialTapTargetSize.shrinkWrap,
                                          labelStyle: const TextStyle(
                                            fontSize: 12,
                                          ),
                                        ),

                                        // رقاقة الجنس
                                        if (admin.gender != null &&
                                            admin.gender!.isNotEmpty)
                                          Chip(
                                            label: Text(admin.gender!),
                                            backgroundColor:
                                                admin.gender == 'ذكر'
                                                    ? Colors.blue.shade100
                                                    : Colors.pink.shade100,
                                            materialTapTargetSize:
                                                MaterialTapTargetSize
                                                    .shrinkWrap,
                                            labelStyle: const TextStyle(
                                              fontSize: 12,
                                            ),
                                          ),

                                        // رقاقة المحافظة
                                        if (admin.governorate != null &&
                                            admin.governorate!.isNotEmpty)
                                          Chip(
                                            label: Text(admin.governorate!),
                                            backgroundColor:
                                                Colors.green.shade100,
                                            materialTapTargetSize:
                                                MaterialTapTargetSize
                                                    .shrinkWrap,
                                            labelStyle: const TextStyle(
                                              fontSize: 12,
                                            ),
                                          ),

                                        // رقاقة الجنسية
                                        if (admin.nationality != 'يمني')
                                          Chip(
                                            label: Text(admin.nationality),
                                            backgroundColor:
                                                Colors.orange.shade100,
                                            materialTapTargetSize:
                                                MaterialTapTargetSize
                                                    .shrinkWrap,
                                            labelStyle: const TextStyle(
                                              fontSize: 12,
                                            ),
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),

                              // ===== أزرار الإجراءات =====
                              Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    icon: const Icon(
                                      Icons.edit,
                                      color: Colors.blue,
                                    ),
                                    onPressed: () {
                                      showDialog(
                                        context: context,
                                        builder:
                                            (_) =>
                                                AdminFormDialog(admin: admin),
                                      );
                                    },
                                  ),
                                  IconButton(
                                    icon: const Icon(
                                      Icons.delete,
                                      color: Colors.red,
                                    ),
                                    onPressed: () {
                                      _showDeleteConfirmationDialog(
                                        context,
                                        ref,
                                        admin,
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
              loading: () => const LoadingIndicator(),
              error: (err, stack) => ErrorMessage(message: err.toString()),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showDialog(context: context, builder: (_) => const AdminFormDialog());
        },
        tooltip: 'إضافة مسؤول جديد',
        child: const Icon(Icons.add),
      ),
    );
  }

  /// عرض تفاصيل المدير في نافذة منبثقة محسنة
  void _showAdminDetails(BuildContext context, UserModel admin) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              height: MediaQuery.of(context).size.height * 0.8,
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  // ===== العنوان مع الصورة =====
                  Row(
                    children: [
                      // الصورة الشخصية
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.grey.shade300,
                            width: 2,
                          ),
                        ),
                        child: ClipOval(
                          child:
                              admin.profileImageUrl != null &&
                                      admin.profileImageUrl!.isNotEmpty
                                  ? Image.network(
                                    admin.profileImageUrl!,
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      return _buildDefaultAdminAvatar();
                                    },
                                  )
                                  : _buildDefaultAdminAvatar(),
                        ),
                      ),

                      const SizedBox(width: 16),

                      // معلومات العنوان
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              admin.name,
                              style: Theme.of(
                                context,
                              ).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.admin_panel_settings,
                                  color: Colors.blue.shade600,
                                  size: 20,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'مدير النظام',
                                  style: TextStyle(
                                    color: Colors.blue.shade600,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.email,
                                  color: Colors.grey.shade600,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Text(
                                    admin.email,
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // زر الإغلاق
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                        tooltip: 'إغلاق',
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),
                  const Divider(),
                  const SizedBox(height: 16),

                  // ===== محتوى التفاصيل =====
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // ===== المعلومات الأساسية =====
                          _buildSectionHeader(
                            'المعلومات الأساسية',
                            Icons.person,
                            Colors.blue,
                          ),
                          const SizedBox(height: 12),
                          _buildInfoCard([
                            _buildDetailRow('الاسم الكامل', admin.name),
                            _buildDetailRow('البريد الإلكتروني', admin.email),
                            _buildDetailRow('الدور', 'مدير النظام'),
                            if (admin.phoneNumber != null &&
                                admin.phoneNumber!.isNotEmpty)
                              _buildDetailRow('رقم الهاتف', admin.phoneNumber!),
                          ]),

                          const SizedBox(height: 20),

                          // ===== المعلومات الشخصية =====
                          _buildSectionHeader(
                            'المعلومات الشخصية',
                            Icons.contact_page,
                            Colors.green,
                          ),
                          const SizedBox(height: 12),
                          _buildInfoCard([
                            if (admin.gender != null &&
                                admin.gender!.isNotEmpty)
                              _buildDetailRow('الجنس', admin.gender!),
                            if (admin.dateOfBirth != null)
                              _buildDetailRow(
                                'تاريخ الميلاد',
                                '${admin.dateOfBirth!.day}/${admin.dateOfBirth!.month}/${admin.dateOfBirth!.year}',
                              ),
                            _buildDetailRow('الجنسية', admin.nationality),
                            if (admin.governorate != null &&
                                admin.governorate!.isNotEmpty)
                              _buildDetailRow('المحافظة', admin.governorate!),
                            if (admin.address != null &&
                                admin.address!.isNotEmpty)
                              _buildDetailRow('العنوان', admin.address!),
                          ]),

                          const SizedBox(height: 20),

                          // ===== المعلومات الإضافية =====
                          _buildSectionHeader(
                            'المعلومات الإضافية',
                            Icons.info,
                            Colors.orange,
                          ),
                          const SizedBox(height: 12),
                          _buildInfoCard([
                            if (admin.nationalId != null &&
                                admin.nationalId!.isNotEmpty)
                              _buildDetailRow(
                                'الرقم الوطني',
                                admin.nationalId!,
                              ),
                            if (admin.bloodType != null &&
                                admin.bloodType!.isNotEmpty)
                              _buildDetailRow('فصيلة الدم', admin.bloodType!),
                            if (admin.healthCondition != null &&
                                admin.healthCondition!.isNotEmpty)
                              _buildDetailRow(
                                'الحالة الصحية',
                                admin.healthCondition!,
                              ),
                            if (admin.notes != null && admin.notes!.isNotEmpty)
                              _buildDetailRow('ملاحظات', admin.notes!),
                          ]),

                          const SizedBox(height: 20),

                          // ===== معلومات النظام =====
                          _buildSectionHeader(
                            'معلومات النظام',
                            Icons.settings,
                            Colors.purple,
                          ),
                          const SizedBox(height: 12),
                          _buildInfoCard([
                            _buildDetailRow('معرف المستخدم', admin.id),
                            _buildDetailRow('نوع الحساب', 'مدير نظام'),
                            _buildDetailRow('حالة الحساب', 'نشط'),
                          ]),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),
                  const Divider(),

                  // ===== أزرار الإجراءات =====
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('إغلاق'),
                      ),
                      const SizedBox(width: 12),
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.of(context).pop();
                          showDialog(
                            context: context,
                            builder: (context) => AdminFormDialog(admin: admin),
                          );
                        },
                        icon: const Icon(Icons.edit),
                        label: const Text('تعديل'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
    );
  }

  /// بناء صف تفاصيل محسن
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14, color: Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عنوان القسم
  Widget _buildSectionHeader(String title, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة المعلومات
  Widget _buildInfoCard(List<Widget> children) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  /// بناء الأيقونة الافتراضية للمدير
  Widget _buildDefaultAdminAvatar() {
    return Container(
      color: Colors.blue.shade50,
      child: Icon(
        Icons.admin_panel_settings,
        size: 40,
        color: Colors.blue.shade400,
      ),
    );
  }
}
