import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/teacher_screens/exam_syllabus_management_screen.dart';
import 'package:school_management_system/teacher_screens/grade_entry_screen.dart';
import 'package:school_management_system/teacher_screens/teacher_exam_schedule_screen.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/providers/teacher_providers.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// الصفحة الرئيسية للمعلم
///
/// هذه الصفحة تعرض لوحة معلومات شاملة للمعلم تتضمن:
/// - ملخص سريع للإحصائيات المهمة
/// - الامتحانات القادمة
/// - المهام المعلقة
/// - الوصول السريع للوظائف الرئيسية
/// - الإشعارات والتذكيرات
///
/// التصميم:
/// - بطاقات تفاعلية للإحصائيات
/// - قائمة بالمهام السريعة
/// - تصميم متجاوب وجذاب
/// - ألوان متناسقة مع هوية المعلمين
class TeacherHomePage extends ConsumerStatefulWidget {
  const TeacherHomePage({super.key});

  @override
  ConsumerState<TeacherHomePage> createState() => _TeacherHomePageState();
}

class _TeacherHomePageState extends ConsumerState<TeacherHomePage> {
  // ===================================================================
  // متغيرات الحالة
  // ===================================================================

  /// حالة تحميل البيانات
  bool _isLoading = true;

  /// إحصائيات المعلم
  int _totalExams = 0;
  int _pendingGrades = 0;
  int _upcomingExams = 0;
  int _completedSyllabuses = 0;

  /// اسم المعلم
  String _teacherName = 'المعلم';

  @override
  void initState() {
    super.initState();
    _loadTeacherData();
  }

  /// تحميل بيانات المعلم والإحصائيات
  Future<void> _loadTeacherData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // الحصول على معرف المعلم الحالي من Firebase Auth
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('المعلم غير مسجل الدخول');
      }

      // جلب الإحصائيات الحقيقية للمعلم من قاعدة البيانات
      final teacherStats = await ref.read(
        teacherRealStatsProvider(currentUser.uid).future,
      );

      setState(() {
        // استخدام البيانات الحقيقية من Firebase
        _teacherName = teacherStats['teacherName'] as String? ?? 'المعلم';
        _totalExams = teacherStats['totalExams'] as int? ?? 0;
        _pendingGrades = teacherStats['pendingGrades'] as int? ?? 0;
        _upcomingExams = teacherStats['upcomingExams'] as int? ?? 0;
        _completedSyllabuses = teacherStats['completedSyllabuses'] as int? ?? 0;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _loadTeacherData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // قسم الترحيب
              _buildWelcomeSection(),
              const SizedBox(height: 24),

              // قسم الإحصائيات السريعة
              _buildQuickStatsSection(),
              const SizedBox(height: 24),

              // قسم الوصول السريع
              _buildQuickAccessSection(),
              const SizedBox(height: 24),

              // قسم الامتحانات القادمة
              _buildUpcomingExamsSection(),
              const SizedBox(height: 24),

              // قسم المهام المعلقة
              _buildPendingTasksSection(),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء قسم الترحيب
  Widget _buildWelcomeSection() {
    final hour = DateTime.now().hour;
    String greeting;

    if (hour < 12) {
      greeting = 'صباح الخير';
    } else if (hour < 17) {
      greeting = 'مساء الخير';
    } else {
      greeting = 'مساء الخير';
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green[600]!, Colors.green[800]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$greeting، $_teacherName',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'مرحباً بك في لوحة التحكم الخاصة بك',
            style: TextStyle(fontSize: 16, color: Colors.green[100]),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(Icons.today, color: Colors.green[100], size: 20),
              const SizedBox(width: 8),
              Text(
                'اليوم: ${_formatDate(DateTime.now())}',
                style: TextStyle(fontSize: 14, color: Colors.green[100]),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء قسم الإحصائيات السريعة
  Widget _buildQuickStatsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإحصائيات السريعة',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              title: 'إجمالي الامتحانات',
              value: _totalExams.toString(),
              icon: Icons.quiz,
              color: Colors.blue,
            ),
            _buildStatCard(
              title: 'درجات معلقة',
              value: _pendingGrades.toString(),
              icon: Icons.pending,
              color: Colors.orange,
            ),
            _buildStatCard(
              title: 'امتحانات قادمة',
              value: _upcomingExams.toString(),
              icon: Icons.schedule,
              color: Colors.purple,
            ),
            _buildStatCard(
              title: 'مناهج مكتملة',
              value: _completedSyllabuses.toString(),
              icon: Icons.check_circle,
              color: Colors.green,
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية واحدة
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء قسم الوصول السريع
  Widget _buildQuickAccessSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الوصول السريع',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.count(
          crossAxisCount: 3,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 1.2,
          children: [
            _buildQuickAccessCard(
              title: 'إدارة المناهج',
              icon: Icons.book,
              color: Colors.indigo,
              onTap:
                  () => _navigateToScreen(const ExamSyllabusManagementScreen()),
            ),
            _buildQuickAccessCard(
              title: 'إدخال الدرجات',
              icon: Icons.grade,
              color: Colors.teal,
              onTap: () => _navigateToScreen(const GradeEntryScreen()),
            ),
            _buildQuickAccessCard(
              title: 'جدول امتحاناتي',
              icon: Icons.schedule,
              color: Colors.green,
              onTap: () => _navigateToScreen(const TeacherExamScheduleScreen()),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة وصول سريع واحدة
  Widget _buildQuickAccessCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الامتحانات القادمة
  Widget _buildUpcomingExamsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الامتحانات القادمة',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: const Center(
            child: Text(
              'لا توجد امتحانات قادمة\n(سيتم تحميل البيانات الفعلية)',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء قسم المهام المعلقة
  Widget _buildPendingTasksSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المهام المعلقة',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.orange[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.orange[200]!),
          ),
          child: const Center(
            child: Text(
              'لا توجد مهام معلقة\n(سيتم تحميل البيانات الفعلية)',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ),
        ),
      ],
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    const months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  /// التنقل إلى شاشة معينة
  void _navigateToScreen(Widget screen) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => screen));
  }
}
