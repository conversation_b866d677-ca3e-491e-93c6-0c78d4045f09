import 'package:flutter/material.dart';
import 'package:school_management_system/shared/app_theme.dart';
import 'package:school_management_system/widgets/enhanced_widgets.dart';

/// شاشة مثال لتوضيح استخدام نظام التصميم الموحد الجديد
/// 
/// تعرض هذه الشاشة جميع المكونات والألوان والخطوط المتاحة
/// في النظام الجديد مع أمثلة عملية للاستخدام
class DesignSystemExample extends StatelessWidget {
  const DesignSystemExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('نظام التصميم الموحد'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpacing.screenPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // قسم الألوان
            _buildColorsSection(),
            const SizedBox(height: AppSpacing.sectionSpacing),
            
            // قسم الخطوط
            _buildTypographySection(),
            const SizedBox(height: AppSpacing.sectionSpacing),
            
            // قسم الأزرار
            _buildButtonsSection(),
            const SizedBox(height: AppSpacing.sectionSpacing),
            
            // قسم البطاقات
            _buildCardsSection(),
            const SizedBox(height: AppSpacing.sectionSpacing),
            
            // قسم مؤشرات التحميل
            _buildLoadingSection(),
          ],
        ),
      ),
    );
  }

  /// قسم عرض الألوان المتاحة
  Widget _buildColorsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الألوان المتاحة',
          style: TextStyle(
            fontSize: AppTextSizes.headlineLarge,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        
        // الألوان الأساسية
        _buildColorRow('الأساسي', AppColors.primary),
        _buildColorRow('الثانوي', AppColors.secondary),
        _buildColorRow('التمييز', AppColors.accent),
        
        const SizedBox(height: AppSpacing.md),
        
        // ألوان الحالات
        _buildColorRow('نجاح', AppColors.success),
        _buildColorRow('تحذير', AppColors.warning),
        _buildColorRow('خطأ', AppColors.error),
        _buildColorRow('معلومات', AppColors.info),
        
        const SizedBox(height: AppSpacing.md),
        
        // ألوان المدرسة
        _buildColorRow('الطلاب', AppColors.studentColor),
        _buildColorRow('أولياء الأمور', AppColors.parentColor),
        _buildColorRow('المعلمين', AppColors.teacherColor),
        _buildColorRow('الإدارة', AppColors.adminColor),
      ],
    );
  }

  /// بناء صف لعرض لون واحد
  Widget _buildColorRow(String name, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(AppBorderRadius.medium),
              border: Border.all(color: AppColors.border),
            ),
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Text(
              name,
              style: const TextStyle(
                fontSize: AppTextSizes.bodyLarge,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          Text(
            color.value.toRadixString(16).toUpperCase(),
            style: const TextStyle(
              fontSize: AppTextSizes.bodySmall,
              color: AppColors.textSecondary,
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  /// قسم عرض أحجام الخطوط
  Widget _buildTypographySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أحجام الخطوط',
          style: TextStyle(
            fontSize: AppTextSizes.headlineLarge,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        
        _buildTextExample('عنوان رئيسي كبير', AppTextSizes.displayLarge, FontWeight.bold),
        _buildTextExample('عنوان رئيسي متوسط', AppTextSizes.displayMedium, FontWeight.bold),
        _buildTextExample('عنوان كبير', AppTextSizes.headlineLarge, FontWeight.w600),
        _buildTextExample('عنوان متوسط', AppTextSizes.headlineMedium, FontWeight.w600),
        _buildTextExample('عنوان صغير', AppTextSizes.headlineSmall, FontWeight.w600),
        _buildTextExample('عنوان فرعي كبير', AppTextSizes.titleLarge, FontWeight.w500),
        _buildTextExample('عنوان فرعي متوسط', AppTextSizes.titleMedium, FontWeight.w500),
        _buildTextExample('نص أساسي كبير', AppTextSizes.bodyLarge, FontWeight.normal),
        _buildTextExample('نص أساسي متوسط', AppTextSizes.bodyMedium, FontWeight.normal),
        _buildTextExample('نص أساسي صغير', AppTextSizes.bodySmall, FontWeight.normal),
      ],
    );
  }

  /// بناء مثال نص
  Widget _buildTextExample(String text, double fontSize, FontWeight fontWeight) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppSpacing.xs),
      child: Text(
        text,
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }

  /// قسم عرض الأزرار
  Widget _buildButtonsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الأزرار المتاحة',
          style: TextStyle(
            fontSize: AppTextSizes.headlineLarge,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        
        // أزرار مرفوعة
        const Text('أزرار مرفوعة:', style: TextStyle(fontWeight: FontWeight.w600)),
        const SizedBox(height: AppSpacing.sm),
        Wrap(
          spacing: AppSpacing.sm,
          runSpacing: AppSpacing.sm,
          children: [
            EnhancedButton(
              text: 'صغير',
              size: ButtonSize.small,
              onPressed: () {},
            ),
            EnhancedButton(
              text: 'متوسط',
              size: ButtonSize.medium,
              onPressed: () {},
            ),
            EnhancedButton(
              text: 'كبير',
              size: ButtonSize.large,
              onPressed: () {},
            ),
            EnhancedButton(
              text: 'مع أيقونة',
              icon: Icons.star,
              onPressed: () {},
            ),
          ],
        ),
        
        const SizedBox(height: AppSpacing.md),
        
        // أزرار محددة
        const Text('أزرار محددة:', style: TextStyle(fontWeight: FontWeight.w600)),
        const SizedBox(height: AppSpacing.sm),
        Wrap(
          spacing: AppSpacing.sm,
          runSpacing: AppSpacing.sm,
          children: [
            EnhancedButton(
              text: 'محدد',
              type: ButtonType.outlined,
              onPressed: () {},
            ),
            EnhancedButton(
              text: 'مع أيقونة',
              icon: Icons.edit,
              type: ButtonType.outlined,
              onPressed: () {},
            ),
          ],
        ),
        
        const SizedBox(height: AppSpacing.md),
        
        // أزرار نصية
        const Text('أزرار نصية:', style: TextStyle(fontWeight: FontWeight.w600)),
        const SizedBox(height: AppSpacing.sm),
        Wrap(
          spacing: AppSpacing.sm,
          runSpacing: AppSpacing.sm,
          children: [
            EnhancedButton(
              text: 'نصي',
              type: ButtonType.text,
              onPressed: () {},
            ),
            EnhancedButton(
              text: 'مع أيقونة',
              icon: Icons.info,
              type: ButtonType.text,
              onPressed: () {},
            ),
          ],
        ),
      ],
    );
  }

  /// قسم عرض البطاقات
  Widget _buildCardsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'البطاقات المتاحة',
          style: TextStyle(
            fontSize: AppTextSizes.headlineLarge,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        
        // بطاقة عادية
        EnhancedCard(
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'بطاقة عادية',
                style: TextStyle(
                  fontSize: AppTextSizes.titleLarge,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: AppSpacing.sm),
              Text(
                'هذه بطاقة عادية بالتصميم الافتراضي',
                style: TextStyle(color: AppColors.textSecondary),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: AppSpacing.md),
        
        // بطاقة مع حدود
        EnhancedCard(
          borderColor: AppColors.primary,
          borderWidth: 2,
          child: const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'بطاقة مع حدود',
                style: TextStyle(
                  fontSize: AppTextSizes.titleLarge,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
              SizedBox(height: AppSpacing.sm),
              Text(
                'هذه بطاقة مع حدود ملونة',
                style: TextStyle(color: AppColors.textSecondary),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// قسم عرض مؤشرات التحميل
  Widget _buildLoadingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'مؤشرات التحميل',
          style: TextStyle(
            fontSize: AppTextSizes.headlineLarge,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppSpacing.md),
        
        // مؤشر تحميل بسيط
        const EnhancedLoadingIndicator(),
        
        const SizedBox(height: AppSpacing.lg),
        
        // مؤشر تحميل مع رسالة
        const EnhancedLoadingIndicator(
          message: 'جاري تحميل البيانات...',
          size: 50,
        ),
      ],
    );
  }
}
