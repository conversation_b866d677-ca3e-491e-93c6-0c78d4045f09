import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/admin_screens/widgets/student_form_dialog.dart';
import 'package:school_management_system/admin_screens/widgets/students_data_table.dart';
import 'package:school_management_system/admin_screens/widgets/students_list.dart';
import 'package:school_management_system/providers/student_providers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/utils/async_error_handler.dart';
import 'package:school_management_system/widgets/enhanced_error_widget.dart';

class StudentManagementScreen extends ConsumerWidget {
  const StudentManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentsAsyncValue = ref.watch(studentsStreamProvider);
    final filteredStudents = ref.watch(filteredStudentsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الطلاب'),
        automaticallyImplyLeading: false,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          // زر الإحصائيات
          IconButton(
            icon: const Icon(Icons.analytics),
            tooltip: 'إحصائيات الطلاب',
            onPressed: () => _showStudentStatistics(context, ref),
          ),
          // زر التصدير
          IconButton(
            icon: const Icon(Icons.download),
            tooltip: 'تصدير البيانات',
            onPressed: () => _exportStudentData(context, ref),
          ),
          // زر الاستيراد
          IconButton(
            icon: const Icon(Icons.upload),
            tooltip: 'استيراد البيانات',
            onPressed: () => _importStudentData(context, ref),
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والتصفية المحسن
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
            ),
            child: Column(
              children: [
                // شريط البحث
                TextField(
                  onChanged: (value) {
                    ref.read(studentSearchQueryProvider.notifier).state =
                        value.toLowerCase();
                  },
                  decoration: InputDecoration(
                    labelText:
                        'ابحث بالاسم، الرقم الأكاديمي، البريد، ولي الأمر، المحافظة، أو الرقم الوطني...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    helperText: 'يمكنك البحث في جميع الحقول',
                  ),
                ),

                const SizedBox(height: 12),

                // إحصائيات سريعة
                studentsAsyncValue.when(
                  data:
                      (students) => Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildQuickStat(
                            'إجمالي الطلاب',
                            '${students.length}',
                            Icons.people,
                            Colors.blue,
                          ),
                          _buildQuickStat(
                            'الطلاب النشطون',
                            '${students.where((s) => s.isActive).length}',
                            Icons.check_circle,
                            Colors.green,
                          ),
                          _buildQuickStat(
                            'الطلاب المعطلون',
                            '${students.where((s) => !s.isActive).length}',
                            Icons.pause_circle,
                            Colors.orange,
                          ),
                          _buildQuickStat(
                            'نتائج البحث',
                            '${filteredStudents.length}',
                            Icons.filter_list,
                            Colors.purple,
                          ),
                        ],
                      ),
                  loading: () => const SizedBox.shrink(),
                  error: (_, __) => const SizedBox.shrink(),
                ),
              ],
            ),
          ),
          Expanded(
            child: studentsAsyncValue.when(
              data: (_) {
                if (filteredStudents.isEmpty) {
                  return const Center(child: Text('لم يتم العثور على طلاب.'));
                }
                return LayoutBuilder(
                  builder: (context, constraints) {
                    if (constraints.maxWidth > 600) {
                      return StudentsDataTable(students: filteredStudents);
                    } else {
                      return StudentsList(students: filteredStudents);
                    }
                  },
                );
              },
              loading: () => const LoadingIndicator(),
              error: (err, stack) => Center(child: Text('حدث خطأ: $err')),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed:
            () => showDialog(
              context: context,
              builder: (context) => const StudentFormDialog(),
            ),
        tooltip: 'إضافة طالب جديد',
        icon: const Icon(Icons.add),
        label: const Text('إضافة طالب'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
    );
  }

  /// بناء إحصائية سريعة
  Widget _buildQuickStat(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: color.withAlpha((255 * 0.1).round()),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withAlpha((255 * 0.3).round())),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// عرض إحصائيات الطلاب
  void _showStudentStatistics(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              height: MediaQuery.of(context).size.height * 0.6,
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.analytics, color: Colors.blue),
                      const SizedBox(width: 12),
                      const Text(
                        'إحصائيات الطلاب',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),

                  const Divider(),

                  const Expanded(
                    child: Center(
                      child: Text(
                        'ميزة الإحصائيات التفصيلية قيد التطوير',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  /// تصدير بيانات الطلاب
  void _exportStudentData(BuildContext context, WidgetRef ref) {
    // TODO: تنفيذ تصدير بيانات الطلاب
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('ميزة التصدير قيد التطوير')));
  }

  /// استيراد بيانات الطلاب
  void _importStudentData(BuildContext context, WidgetRef ref) {
    // TODO: تنفيذ استيراد بيانات الطلاب
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('ميزة الاستيراد قيد التطوير')));
  }
}
