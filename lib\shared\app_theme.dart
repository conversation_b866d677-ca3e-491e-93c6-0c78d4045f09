import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// نظام الألوان الموحد لتطبيق إدارة المدرسة
///
/// يحتوي على جميع الألوان المستخدمة في التطبيق لضمان التناسق
/// والهوية البصرية الموحدة عبر جميع الشاشات
class AppColors {
  // الألوان الأساسية للتطبيق
  static const Color primary = Color(0xFF1976D2); // الأزرق الأساسي
  static const Color primaryDark = Color(0xFF1565C0); // الأزرق الداكن
  static const Color primaryLight = Color(0xFF42A5F5); // الأزرق الفاتح

  // الألوان الثانوية
  static const Color secondary = Color(0xFF388E3C); // الأخضر للنجاح
  static const Color secondaryDark = Color(0xFF2E7D32); // الأخضر الداكن
  static const Color secondaryLight = Color(0xFF66BB6A); // الأخضر الفاتح

  // ألوان التمييز والتنبيه
  static const Color accent = Color(0xFFFF9800); // البرتقالي للتنبيه
  static const Color warning = Color(0xFFFFC107); // الأصفر للتحذير
  static const Color error = Color(0xFFD32F2F); // الأحمر للخطأ
  static const Color success = Color(0xFF4CAF50); // الأخضر للنجاح
  static const Color info = Color(0xFF2196F3); // الأزرق للمعلومات

  // ألوان الخلفيات
  static const Color background = Color(0xFFFAFAFA); // خلفية فاتحة
  static const Color surface = Color(0xFFFFFFFF); // سطح أبيض
  static const Color surfaceVariant = Color(0xFFF5F5F5); // سطح متغير

  // ألوان النصوص
  static const Color textPrimary = Color(0xFF212121); // نص أساسي
  static const Color textSecondary = Color(0xFF757575); // نص ثانوي
  static const Color textDisabled = Color(0xFFBDBDBD); // نص معطل
  static const Color textOnPrimary = Color(0xFFFFFFFF); // نص على الأساسي

  // ألوان الحدود والفواصل
  static const Color border = Color(0xFFE0E0E0); // حدود
  static const Color divider = Color(0xFFBDBDBD); // فواصل

  // ألوان خاصة بالمدرسة
  static const Color studentColor = Color(0xFF1976D2); // لون الطلاب
  static const Color parentColor = Color(0xFF388E3C); // لون أولياء الأمور
  static const Color teacherColor = Color(0xFF7B1FA2); // لون المعلمين
  static const Color adminColor = Color(0xFFD32F2F); // لون الإدارة

  // ألوان الحالات
  static const Color present = Color(0xFF4CAF50); // حاضر
  static const Color absent = Color(0xFFD32F2F); // غائب
  static const Color late = Color(0xFFFF9800); // متأخر
  static const Color excused = Color(0xFF2196F3); // معذور
}

/// نظام المسافات الموحد لتطبيق إدارة المدرسة
///
/// يحدد جميع المسافات المستخدمة في التطبيق لضمان التناسق
/// في التخطيط والتباعد بين العناصر
class AppSpacing {
  // المسافات الأساسية
  static const double xs = 4.0; // مسافة صغيرة جداً
  static const double sm = 8.0; // مسافة صغيرة
  static const double md = 16.0; // مسافة متوسطة (الافتراضية)
  static const double lg = 24.0; // مسافة كبيرة
  static const double xl = 32.0; // مسافة كبيرة جداً
  static const double xxl = 48.0; // مسافة ضخمة

  // مسافات خاصة
  static const double cardPadding = md; // حشو البطاقات
  static const double screenPadding = md; // حشو الشاشات
  static const double buttonPadding = md; // حشو الأزرار
  static const double sectionSpacing = lg; // مسافة بين الأقسام
  static const double itemSpacing = sm; // مسافة بين العناصر
}

/// نظام أحجام الخطوط الموحد لتطبيق إدارة المدرسة
///
/// يحدد جميع أحجام الخطوط المستخدمة في التطبيق
/// مع مراعاة التدرج الهرمي للمحتوى
class AppTextSizes {
  // أحجام العناوين
  static const double displayLarge = 32.0; // عنوان رئيسي كبير
  static const double displayMedium = 28.0; // عنوان رئيسي متوسط
  static const double headlineLarge = 24.0; // عنوان كبير
  static const double headlineMedium = 20.0; // عنوان متوسط
  static const double headlineSmall = 18.0; // عنوان صغير

  // أحجام العناوين الفرعية
  static const double titleLarge = 18.0; // عنوان فرعي كبير
  static const double titleMedium = 16.0; // عنوان فرعي متوسط
  static const double titleSmall = 14.0; // عنوان فرعي صغير

  // أحجام النصوص
  static const double bodyLarge = 16.0; // نص أساسي كبير
  static const double bodyMedium = 14.0; // نص أساسي متوسط (الافتراضي)
  static const double bodySmall = 12.0; // نص أساسي صغير

  // أحجام النصوص الصغيرة
  static const double labelLarge = 14.0; // تسمية كبيرة
  static const double labelMedium = 12.0; // تسمية متوسطة
  static const double labelSmall = 10.0; // تسمية صغيرة
}

/// نظام الحدود والزوايا الموحد لتطبيق إدارة المدرسة
///
/// يحدد جميع أنصاف أقطار الحدود المستخدمة في التطبيق
/// لضمان التناسق في شكل العناصر
class AppBorderRadius {
  static const double small = 4.0; // زوايا صغيرة
  static const double medium = 8.0; // زوايا متوسطة (الافتراضية)
  static const double large = 12.0; // زوايا كبيرة
  static const double extraLarge = 16.0; // زوايا كبيرة جداً
  static const double circular = 50.0; // دائرية كاملة
}

/// نظام الظلال الموحد لتطبيق إدارة المدرسة
///
/// يحدد جميع مستويات الظلال المستخدمة في التطبيق
/// لإعطاء عمق وتدرج بصري للعناصر
class AppElevation {
  static const double none = 0.0; // بدون ظل
  static const double low = 1.0; // ظل خفيف
  static const double medium = 2.0; // ظل متوسط (الافتراضي)
  static const double high = 4.0; // ظل عالي
  static const double extraHigh = 8.0; // ظل عالي جداً
}

/// فئة الثيم الرئيسية لتطبيق إدارة المدرسة
///
/// تحتوي على التصميم الموحد للتطبيق باستخدام النظم المحددة أعلاه
/// وتطبق الهوية البصرية المتناسقة عبر جميع الشاشات
class AppTheme {
  /// الثيم الفاتح للتطبيق
  ///
  /// يطبق نظام الألوان والخطوط والمسافات الموحد
  /// مع مراعاة إمكانية الوصول والاستخدام السهل
  static ThemeData get lightTheme {
    return ThemeData(
      // تطبيق نظام الألوان الموحد
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColors.primary,
        brightness: Brightness.light,
        primary: AppColors.primary,
        onPrimary: AppColors.textOnPrimary,
        secondary: AppColors.secondary,
        onSecondary: AppColors.textOnPrimary,
        error: AppColors.error,
        onError: AppColors.textOnPrimary,
        surface: AppColors.surface,
        onSurface: AppColors.textPrimary,
      ),

      // تطبيق الخط العربي الموحد
      fontFamily: GoogleFonts.cairo().fontFamily,

      // لون الخلفية الأساسي
      scaffoldBackgroundColor: AppColors.background,

      // تصميم شريط التطبيق الموحد
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textOnPrimary,
        elevation: AppElevation.medium,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontFamily: GoogleFonts.cairo().fontFamily,
          fontSize: AppTextSizes.headlineMedium,
          fontWeight: FontWeight.bold,
          color: AppColors.textOnPrimary,
        ),
        iconTheme: const IconThemeData(color: AppColors.textOnPrimary),
      ),

      // نظام الخطوط الموحد
      textTheme: TextTheme(
        // العناوين الرئيسية
        displayLarge: TextStyle(
          fontSize: AppTextSizes.displayLarge,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
          height: 1.2,
        ),
        displayMedium: TextStyle(
          fontSize: AppTextSizes.displayMedium,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
          height: 1.2,
        ),

        // العناوين الفرعية
        headlineLarge: TextStyle(
          fontSize: AppTextSizes.headlineLarge,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
          height: 1.3,
        ),
        headlineMedium: TextStyle(
          fontSize: AppTextSizes.headlineMedium,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
          height: 1.3,
        ),
        headlineSmall: TextStyle(
          fontSize: AppTextSizes.headlineSmall,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
          height: 1.3,
        ),

        // عناوين الأقسام
        titleLarge: TextStyle(
          fontSize: AppTextSizes.titleLarge,
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary,
          height: 1.4,
        ),
        titleMedium: TextStyle(
          fontSize: AppTextSizes.titleMedium,
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary,
          height: 1.4,
        ),
        titleSmall: TextStyle(
          fontSize: AppTextSizes.titleSmall,
          fontWeight: FontWeight.w500,
          color: AppColors.textSecondary,
          height: 1.4,
        ),

        // النصوص الأساسية
        bodyLarge: TextStyle(
          fontSize: AppTextSizes.bodyLarge,
          fontWeight: FontWeight.normal,
          color: AppColors.textPrimary,
          height: 1.5,
        ),
        bodyMedium: TextStyle(
          fontSize: AppTextSizes.bodyMedium,
          fontWeight: FontWeight.normal,
          color: AppColors.textPrimary,
          height: 1.5,
        ),
        bodySmall: TextStyle(
          fontSize: AppTextSizes.bodySmall,
          fontWeight: FontWeight.normal,
          color: AppColors.textSecondary,
          height: 1.5,
        ),

        // التسميات والنصوص الصغيرة
        labelLarge: TextStyle(
          fontSize: AppTextSizes.labelLarge,
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary,
          height: 1.4,
        ),
        labelMedium: TextStyle(
          fontSize: AppTextSizes.labelMedium,
          fontWeight: FontWeight.w500,
          color: AppColors.textSecondary,
          height: 1.4,
        ),
        labelSmall: TextStyle(
          fontSize: AppTextSizes.labelSmall,
          fontWeight: FontWeight.w500,
          color: AppColors.textSecondary,
          height: 1.4,
        ),
      ),

      // تصميم الأزرار المرفوعة الموحد
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
          elevation: AppElevation.medium,
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.buttonPadding,
            vertical: AppSpacing.md,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          ),
          textStyle: TextStyle(
            fontSize: AppTextSizes.labelLarge,
            fontWeight: FontWeight.w600,
            fontFamily: GoogleFonts.cairo().fontFamily,
          ),
        ),
      ),

      // تصميم الأزرار المحددة الموحد
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primary,
          side: const BorderSide(color: AppColors.primary, width: 1.5),
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.buttonPadding,
            vertical: AppSpacing.md,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          ),
          textStyle: TextStyle(
            fontSize: AppTextSizes.labelLarge,
            fontWeight: FontWeight.w600,
            fontFamily: GoogleFonts.cairo().fontFamily,
          ),
        ),
      ),

      // تصميم الأزرار النصية الموحد
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primary,
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.md,
            vertical: AppSpacing.sm,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          ),
          textStyle: TextStyle(
            fontSize: AppTextSizes.labelLarge,
            fontWeight: FontWeight.w600,
            fontFamily: GoogleFonts.cairo().fontFamily,
          ),
        ),
      ),

      // تصميم حقول الإدخال الموحد
      inputDecorationTheme: InputDecorationTheme(
        // الحدود العادية
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          borderSide: const BorderSide(color: AppColors.border, width: 1.0),
        ),
        // الحدود عند التركيز
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          borderSide: const BorderSide(color: AppColors.primary, width: 2.0),
        ),
        // الحدود عند الخطأ
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          borderSide: const BorderSide(color: AppColors.error, width: 1.0),
        ),
        // الحدود عند التركيز مع خطأ
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          borderSide: const BorderSide(color: AppColors.error, width: 2.0),
        ),
        // الحدود المعطلة
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          borderSide: const BorderSide(
            color: AppColors.textDisabled,
            width: 1.0,
          ),
        ),
        // الحشو الداخلي
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.md,
        ),
        // لون الخلفية
        fillColor: AppColors.surface,
        filled: true,
        // نمط التسمية
        labelStyle: const TextStyle(
          color: AppColors.textSecondary,
          fontSize: AppTextSizes.bodyMedium,
        ),
        // نمط النص المساعد
        helperStyle: const TextStyle(
          color: AppColors.textSecondary,
          fontSize: AppTextSizes.bodySmall,
        ),
        // نمط نص الخطأ
        errorStyle: const TextStyle(
          color: AppColors.error,
          fontSize: AppTextSizes.bodySmall,
        ),
      ),

      // تصميم البطاقات الموحد
      cardTheme: CardTheme(
        elevation: AppElevation.medium,
        margin: const EdgeInsets.all(AppSpacing.cardPadding),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.large),
        ),
        color: AppColors.surface,
        shadowColor: AppColors.textSecondary.withValues(alpha: 0.2),
      ),

      // تصميم شريط التنقل السفلي الموحد
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppColors.surface,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textSecondary,
        type: BottomNavigationBarType.fixed,
        elevation: AppElevation.high,
        selectedLabelStyle: TextStyle(
          fontSize: AppTextSizes.labelSmall,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: AppTextSizes.labelSmall,
          fontWeight: FontWeight.normal,
        ),
      ),

      // تصميم القوائم الموحد
      listTileTheme: const ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        ),
        titleTextStyle: TextStyle(
          fontSize: AppTextSizes.bodyLarge,
          fontWeight: FontWeight.w500,
          color: AppColors.textPrimary,
        ),
        subtitleTextStyle: TextStyle(
          fontSize: AppTextSizes.bodyMedium,
          color: AppColors.textSecondary,
        ),
      ),

      // تصميم الفواصل الموحد
      dividerTheme: const DividerThemeData(
        color: AppColors.divider,
        thickness: 1.0,
        space: AppSpacing.md,
      ),

      // تصميم أشرطة التقدم الموحد
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: AppColors.primary,
        linearTrackColor: AppColors.border,
        circularTrackColor: AppColors.border,
      ),

      // تصميم المفاتيح الموحد
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.primary;
          }
          return AppColors.textSecondary;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.primary.withValues(alpha: 0.5);
          }
          return AppColors.border;
        }),
      ),

      // تصميم مربعات الاختيار الموحد
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.primary;
          }
          return AppColors.surface;
        }),
        checkColor: WidgetStateProperty.all(AppColors.textOnPrimary),
        side: const BorderSide(color: AppColors.border, width: 1.5),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppBorderRadius.small),
        ),
      ),

      // تصميم أزرار الراديو الموحد
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppColors.primary;
          }
          return AppColors.textSecondary;
        }),
      ),
    );
  }

  /// الثيم الداكن للتطبيق (للاستخدام المستقبلي)
  ///
  /// يمكن تطويره لاحقاً لدعم الوضع الليلي
  static ThemeData get darkTheme {
    // TODO: تطوير الثيم الداكن في المستقبل
    return lightTheme;
  }
}
