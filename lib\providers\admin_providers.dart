import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

/// Provider لجلب قائمة المسؤولين من Firestore.
final adminsStreamProvider = StreamProvider.autoDispose<List<UserModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  // سنفترض أن دالة `getUsersByRoleStream` ستتم إضافتها إلى FirebaseService
  return firebaseService.getUsersByRoleStream(['admin']);
});

/// Provider لتخزين نص البحث عن المسؤولين.
final adminSearchQueryProvider = StateProvider<String>((ref) => '');

/// Provider لفلترة قائمة المسؤولين بناءً على نص البحث.
final filteredAdminsProvider = Provider.autoDispose<List<UserModel>>((ref) {
  final adminsAsyncValue = ref.watch(adminsStreamProvider);
  final searchQuery = ref.watch(adminSearchQueryProvider).toLowerCase();

  return adminsAsyncValue.when(
    data: (admins) {
      if (searchQuery.isEmpty) {
        return admins;
      }
      return admins.where((admin) {
        // البحث في الحقول الأساسية
        final nameLower = admin.name.toLowerCase();
        final emailLower = admin.email.toLowerCase();

        // البحث في الحقول الشخصية الجديدة
        final phoneLower = (admin.phoneNumber ?? '').toLowerCase();
        final addressLower = (admin.address ?? '').toLowerCase();
        final governorateLower = (admin.governorate ?? '').toLowerCase();
        final nationalityLower = admin.nationality.toLowerCase();
        final nationalIdLower = (admin.nationalId ?? '').toLowerCase();
        final genderLower = (admin.gender ?? '').toLowerCase();

        return nameLower.contains(searchQuery) ||
            emailLower.contains(searchQuery) ||
            phoneLower.contains(searchQuery) ||
            addressLower.contains(searchQuery) ||
            governorateLower.contains(searchQuery) ||
            nationalityLower.contains(searchQuery) ||
            nationalIdLower.contains(searchQuery) ||
            genderLower.contains(searchQuery);
      }).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});
