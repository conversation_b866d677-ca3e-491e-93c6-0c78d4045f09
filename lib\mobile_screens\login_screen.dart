import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/mobile_screens/forgot_password_screen.dart';
import 'package:school_management_system/providers/auth_providers.dart';
import 'package:school_management_system/shared/app_theme.dart';
import 'package:school_management_system/shared/auth_gate.dart';

/// شاشة تسجيل الدخول المحسنة للتطبيق
///
/// تطبق التصميم الموحد الجديد مع تحسينات في تجربة المستخدم:
/// - تصميم موحد باستخدام النظام الجديد للألوان والمسافات
/// - زر دخول واحد مع تحديد تلقائي لنوع المستخدم
/// - رسائل خطأ واضحة ومفيدة
/// - تحسينات في إمكانية الوصول
class LoginScreen extends ConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = GlobalKey<FormState>();
    final emailController = TextEditingController();
    final passwordController = TextEditingController();

    // الاستماع للتغيرات في حالة المصادقة لعرض رسائل الخطأ
    ref.listen<LoginState>(loginControllerProvider, (previous, next) {
      if (next.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppBorderRadius.medium),
            ),
          ),
        );
      }
    });

    // مراقبة حالة التحميل
    final isLoading = ref.watch(loginControllerProvider).isLoading;

    /// دالة تسجيل الدخول المحسنة
    ///
    /// تتحقق من صحة البيانات وتقوم بتسجيل الدخول
    /// مع التحديد التلقائي لنوع المستخدم من قاعدة البيانات
    void login() async {
      if (formKey.currentState!.validate()) {
        final success = await ref
            .read(loginControllerProvider.notifier)
            .login(emailController.text.trim(), passwordController.text.trim());

        if (success && context.mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const AuthGate()),
          );
        }
      }
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppSpacing.screenPadding),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 400),
              child: Form(
                key: formKey,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // شعار التطبيق
                    Container(
                      padding: const EdgeInsets.all(AppSpacing.lg),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.school,
                        size: 64,
                        color: AppColors.primary,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.sectionSpacing),

                    // عنوان الترحيب
                    Column(
                      children: [
                        Text(
                          'مرحباً بك',
                          textAlign: TextAlign.center,
                          style: Theme.of(
                            context,
                          ).textTheme.displayMedium?.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: AppSpacing.sm),
                        Text(
                          'سجل الدخول للوصول إلى حسابك في نظام إدارة المدرسة',
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.bodyLarge
                              ?.copyWith(color: AppColors.textSecondary),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSpacing.sectionSpacing),
                    // حقل البريد الإلكتروني
                    TextFormField(
                      controller: emailController,
                      decoration: const InputDecoration(
                        labelText: 'البريد الإلكتروني',
                        hintText: 'أدخل بريدك الإلكتروني',
                        prefixIcon: Icon(Icons.email_outlined),
                      ),
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.next,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'الرجاء إدخال البريد الإلكتروني';
                        }
                        if (!value.contains('@') || !value.contains('.')) {
                          return 'الرجاء إدخال بريد إلكتروني صحيح';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppSpacing.md),

                    // حقل كلمة المرور
                    TextFormField(
                      controller: passwordController,
                      decoration: const InputDecoration(
                        labelText: 'كلمة المرور',
                        hintText: 'أدخل كلمة المرور',
                        prefixIcon: Icon(Icons.lock_outline),
                      ),
                      obscureText: true,
                      textInputAction: TextInputAction.done,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'الرجاء إدخال كلمة المرور';
                        }
                        if (value.length < 6) {
                          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: AppSpacing.sectionSpacing),
                    // زر تسجيل الدخول أو مؤشر التحميل
                    if (isLoading)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: AppSpacing.lg,
                        ),
                        child: const Column(
                          children: [
                            CircularProgressIndicator(color: AppColors.primary),
                            SizedBox(height: AppSpacing.md),
                            Text(
                              'جاري تسجيل الدخول...',
                              style: TextStyle(
                                color: AppColors.textSecondary,
                                fontSize: AppTextSizes.bodyMedium,
                              ),
                            ),
                          ],
                        ),
                      )
                    else
                      ElevatedButton.icon(
                        onPressed: login,
                        icon: const Icon(Icons.login),
                        label: const Text('تسجيل الدخول'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            vertical: AppSpacing.lg,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              AppBorderRadius.medium,
                            ),
                          ),
                        ),
                      ),

                    const SizedBox(height: AppSpacing.md),

                    // رابط نسيان كلمة المرور
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const ForgotPasswordScreen(),
                          ),
                        );
                      },
                      child: const Text('هل نسيت كلمة المرور؟'),
                    ),

                    const SizedBox(height: AppSpacing.sectionSpacing),

                    // معلومات إضافية
                    Container(
                      padding: const EdgeInsets.all(AppSpacing.md),
                      decoration: BoxDecoration(
                        color: AppColors.info.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(
                          AppBorderRadius.medium,
                        ),
                        border: Border.all(
                          color: AppColors.info.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: const Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: AppColors.info,
                                size: 20,
                              ),
                              SizedBox(width: AppSpacing.sm),
                              Text(
                                'معلومة مهمة',
                                style: TextStyle(
                                  color: AppColors.info,
                                  fontWeight: FontWeight.w600,
                                  fontSize: AppTextSizes.labelLarge,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: AppSpacing.sm),
                          Text(
                            'سيتم تحديد نوع حسابك (طالب أو ولي أمر) تلقائياً بناءً على بياناتك المسجلة في النظام',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: AppTextSizes.bodySmall,
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
