import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/student_providers.dart';
import 'package:school_management_system/widgets/enhanced_error_widget.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// صفحة الملف الشخصي للطالب
class StudentProfileScreen extends ConsumerWidget {
  final String studentId;
  const StudentProfileScreen({super.key, required this.studentId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentAsyncValue = ref.watch(currentStudentProvider(studentId));

    return Scaffold(
      appBar: AppBar(title: const Text('الملف الشخصي')),
      body: studentAsyncValue.when(
        loading: () => const LoadingIndicator(),
        error:
            (err, stack) => EnhancedErrorWidget(
              error: err,
              customMessage: 'حدث خطأ في تحميل الملف الشخصي',
              onRetry: () => ref.invalidate(currentStudentProvider(studentId)),
              showDetails: true,
            ),
        data: (student) {
          if (student == null) {
            return const EnhancedErrorWidget(
              error: 'لم يتم العثور على بيانات الطالب',
              customMessage: 'لم يتم العثور على بيانات الطالب',
            );
          }
          return _buildProfileView(context, student);
        },
      ),
    );
  }

  Widget _buildProfileView(BuildContext context, StudentModel student) {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        Center(
          child: CircleAvatar(
            radius: 50,
            backgroundImage:
                student.profileImageUrl != null
                    ? NetworkImage(student.profileImageUrl!)
                    : null,
            child:
                student.profileImageUrl == null
                    ? const Icon(Icons.person, size: 50)
                    : null,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          student.name,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 24),

        // ===== قسم المعلومات الأساسية =====
        const Text(
          'المعلومات الأساسية',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildInfoTile(Icons.person, 'الاسم الكامل', student.name),
        _buildInfoTile(Icons.email, 'البريد الإلكتروني', student.email),
        _buildInfoTile(Icons.school, 'الرقم الأكاديمي', student.studentNumber),
        _buildInfoTile(Icons.class_, 'الصف', student.studentClass),
        _buildInfoTile(Icons.wc, 'الجنس', student.gender ?? 'غير محدد'),

        const SizedBox(height: 24),
        const Divider(),

        // ===== قسم المعلومات الشخصية =====
        const Text(
          'المعلومات الشخصية',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildInfoTile(
          Icons.cake,
          'تاريخ الميلاد',
          student.dateOfBirth != null
              ? '${student.dateOfBirth!.day}/${student.dateOfBirth!.month}/${student.dateOfBirth!.year}'
              : 'غير متوفر',
        ),
        _buildInfoTile(
          Icons.phone,
          'رقم الهاتف',
          student.phoneNumber ?? 'غير متوفر',
        ),
        _buildInfoTile(
          Icons.location_on,
          'العنوان',
          student.address ?? 'غير متوفر',
        ),
        _buildInfoTile(
          Icons.credit_card,
          'الرقم الوطني',
          student.nationalId ?? 'غير متوفر',
        ),

        const SizedBox(height: 24),
        const Divider(),

        // ===== قسم معلومات ولي الأمر =====
        const Text(
          'معلومات ولي الأمر',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildInfoTile(
          Icons.person_outline,
          'اسم ولي الأمر',
          student.guardianName,
        ),
        _buildInfoTile(
          Icons.phone_android,
          'رقم هاتف ولي الأمر',
          student.guardianPhone ?? 'غير متوفر',
        ),

        const SizedBox(height: 24),
        const Divider(),

        // ===== قسم المعلومات الإضافية =====
        const Text(
          'المعلومات الإضافية',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildInfoTile(Icons.location_city, 'المحافظة', student.governorate),
        _buildInfoTile(Icons.flag, 'الجنسية', student.nationality),
        _buildInfoTile(
          Icons.bloodtype,
          'فصيلة الدم',
          student.bloodType ?? 'غير متوفر',
        ),
        _buildInfoTile(
          Icons.health_and_safety,
          'الحالة الصحية',
          student.healthCondition ?? 'سليم',
        ),
        if (student.notes != null && student.notes!.isNotEmpty)
          _buildInfoTile(Icons.note, 'ملاحظات خاصة', student.notes!),
      ],
    );
  }

  Widget _buildInfoTile(IconData icon, String title, String subtitle) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: ListTile(
        leading: Icon(icon),
        title: Text(title),
        subtitle: Text(subtitle),
      ),
    );
  }
}
