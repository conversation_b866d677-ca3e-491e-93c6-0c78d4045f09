import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/guardian_model.dart';
import 'package:school_management_system/providers/guardian_providers.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/utils/helpers.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/admin_screens/widgets/guardian_form_dialog.dart';
import 'package:school_management_system/admin_screens/widgets/link_students_dialog.dart';

/// شاشة إدارة أولياء الأمور المعاد هيكلتها.
class GuardianManagementScreen extends ConsumerWidget {
  const GuardianManagementScreen({super.key});

  /// دالة لعرض ديالوج تأكيد الحذف
  void _showDeleteConfirmationDialog(
    BuildContext context,
    WidgetRef ref,
    GuardianModel guardian,
  ) async {
    final confirmed = await showConfirmationDialog(
      context: context,
      title: 'تأكيد الحذف',
      content:
          'هل أنت متأكد أنك تريد حذف ولي الأمر "${guardian.name}"؟ سيؤدي هذا إلى حذف حسابه.',
    );

    if (confirmed) {
      try {
        await ref.read(firebaseServiceProvider).deleteGuardian(guardian.id);
        showSuccessSnackBar(context, 'تم حذف ولي الأمر بنجاح.');
      } catch (e) {
        showErrorSnackBar(context, 'حدث خطأ: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final guardiansAsyncValue = ref.watch(guardiansStreamProvider);
    final filteredGuardians = ref.watch(filteredGuardiansProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة أولياء الأمور'),
        automaticallyImplyLeading: false,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              onChanged: (value) {
                ref.read(guardianSearchQueryProvider.notifier).state = value;
              },
              decoration: const InputDecoration(
                labelText:
                    'ابحث بالاسم، البريد الإلكتروني، رقم الهاتف، المحافظة، أو المهنة...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
                helperText: 'يمكنك البحث في جميع الحقول',
              ),
            ),
          ),
          Expanded(
            child: guardiansAsyncValue.when(
              data: (_) {
                if (filteredGuardians.isEmpty) {
                  return const Center(child: Text('لم يتم العثور على نتائج.'));
                }
                return LayoutBuilder(
                  builder: (context, constraints) {
                    // تحديد ما إذا كان العرض كافياً لعرض الجدول أم البطاقات
                    if (constraints.maxWidth > 1200) {
                      return _buildDataTable(context, ref, filteredGuardians);
                    } else {
                      return _buildCardsList(context, ref, filteredGuardians);
                    }
                  },
                );
              },
              loading: () => const LoadingIndicator(),
              error: (err, stack) => ErrorMessage(message: err.toString()),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showDialog(
            context: context,
            builder: (_) => const GuardianFormDialog(),
          );
        },
        tooltip: 'إضافة ولي أمر',
        child: const Icon(Icons.add),
      ),
    );
  }

  /// ===== بناء جدول البيانات المحدث =====
  Widget _buildDataTable(
    BuildContext context,
    WidgetRef ref,
    List<GuardianModel> guardians,
  ) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columnSpacing: 20,
        horizontalMargin: 16,
        columns: const [
          DataColumn(label: Text('الصورة')),
          DataColumn(label: Text('الاسم')),
          DataColumn(label: Text('البريد الإلكتروني')),
          DataColumn(label: Text('رقم الهاتف')),
          DataColumn(label: Text('المحافظة')),
          DataColumn(label: Text('الجنسية')),
          DataColumn(label: Text('المهنة')),
          DataColumn(label: Text('الطلاب المرتبطون')),
          DataColumn(label: Text('إجراءات')),
        ],
        rows:
            guardians.map((guardian) {
              return DataRow(
                cells: [
                  // الصورة الشخصية
                  DataCell(
                    CircleAvatar(
                      radius: 20,
                      backgroundImage:
                          (guardian.profileImageUrl != null &&
                                  guardian.profileImageUrl!.isNotEmpty)
                              ? NetworkImage(guardian.profileImageUrl!)
                              : null,
                      child:
                          (guardian.profileImageUrl == null ||
                                  guardian.profileImageUrl!.isEmpty)
                              ? const Icon(Icons.person, size: 20)
                              : null,
                    ),
                  ),

                  // الاسم
                  DataCell(
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          guardian.name,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        if (guardian.gender != null)
                          Text(
                            guardian.gender!,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // البريد الإلكتروني
                  DataCell(Text(guardian.email)),

                  // رقم الهاتف
                  DataCell(
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(guardian.phoneNumber),
                        if (guardian.alternativePhoneNumber != null &&
                            guardian.alternativePhoneNumber!.isNotEmpty)
                          Text(
                            guardian.alternativePhoneNumber!,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // المحافظة
                  DataCell(Text(guardian.governorate ?? 'غير محدد')),

                  // الجنسية
                  DataCell(Text(guardian.nationality ?? 'يمني')),

                  // المهنة
                  DataCell(
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(guardian.occupation ?? 'غير محدد'),
                        if (guardian.workplace != null &&
                            guardian.workplace!.isNotEmpty)
                          Text(
                            guardian.workplace!,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                      ],
                    ),
                  ),

                  // الطلاب المرتبطون
                  DataCell(
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color:
                            guardian.linkedStudents.isNotEmpty
                                ? Colors.green.shade100
                                : Colors.orange.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        guardian.linkedStudents.length.toString(),
                        style: TextStyle(
                          color:
                              guardian.linkedStudents.isNotEmpty
                                  ? Colors.green.shade700
                                  : Colors.orange.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  // الإجراءات
                  DataCell(
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // زر عرض التفاصيل
                        IconButton(
                          icon: const Icon(
                            Icons.visibility,
                            color: Colors.blue,
                          ),
                          tooltip: 'عرض التفاصيل',
                          onPressed:
                              () => _showGuardianDetails(context, guardian),
                        ),

                        // زر ربط الطلاب
                        IconButton(
                          icon: const Icon(Icons.link, color: Colors.green),
                          tooltip: 'ربط الطلاب',
                          onPressed:
                              () => _showLinkStudentsDialog(context, guardian),
                        ),

                        // زر التعديل
                        IconButton(
                          icon: const Icon(Icons.edit, color: Colors.orange),
                          tooltip: 'تعديل',
                          onPressed: () => _showEditDialog(context, guardian),
                        ),

                        // زر الحذف
                        IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          tooltip: 'حذف',
                          onPressed:
                              () => _showDeleteConfirmationDialog(
                                context,
                                ref,
                                guardian,
                              ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            }).toList(),
      ),
    );
  }

  /// ===== بناء قائمة البطاقات للشاشات الصغيرة =====
  Widget _buildCardsList(
    BuildContext context,
    WidgetRef ref,
    List<GuardianModel> guardians,
  ) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: guardians.length,
      itemBuilder: (context, index) {
        final guardian = guardians[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          child: InkWell(
            onTap: () => _showGuardianDetails(context, guardian),
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // الصف الأول: الصورة والاسم والإجراءات
                  Row(
                    children: [
                      // الصورة الشخصية
                      CircleAvatar(
                        radius: 30,
                        backgroundImage:
                            (guardian.profileImageUrl != null &&
                                    guardian.profileImageUrl!.isNotEmpty)
                                ? NetworkImage(guardian.profileImageUrl!)
                                : null,
                        child:
                            (guardian.profileImageUrl == null ||
                                    guardian.profileImageUrl!.isEmpty)
                                ? const Icon(Icons.person, size: 30)
                                : null,
                      ),

                      const SizedBox(width: 16),

                      // الاسم والمعلومات الأساسية
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              guardian.name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              guardian.email,
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              guardian.phoneNumber,
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // الإجراءات
                      PopupMenuButton<String>(
                        onSelected: (value) {
                          switch (value) {
                            case 'details':
                              _showGuardianDetails(context, guardian);
                              break;
                            case 'link':
                              _showLinkStudentsDialog(context, guardian);
                              break;
                            case 'edit':
                              _showEditDialog(context, guardian);
                              break;
                            case 'delete':
                              _showDeleteConfirmationDialog(
                                context,
                                ref,
                                guardian,
                              );
                              break;
                          }
                        },
                        itemBuilder:
                            (context) => [
                              const PopupMenuItem(
                                value: 'details',
                                child: Row(
                                  children: [
                                    Icon(Icons.visibility, color: Colors.blue),
                                    SizedBox(width: 8),
                                    Text('عرض التفاصيل'),
                                  ],
                                ),
                              ),
                              const PopupMenuItem(
                                value: 'link',
                                child: Row(
                                  children: [
                                    Icon(Icons.link, color: Colors.green),
                                    SizedBox(width: 8),
                                    Text('ربط الطلاب'),
                                  ],
                                ),
                              ),
                              const PopupMenuItem(
                                value: 'edit',
                                child: Row(
                                  children: [
                                    Icon(Icons.edit, color: Colors.orange),
                                    SizedBox(width: 8),
                                    Text('تعديل'),
                                  ],
                                ),
                              ),
                              const PopupMenuItem(
                                value: 'delete',
                                child: Row(
                                  children: [
                                    Icon(Icons.delete, color: Colors.red),
                                    SizedBox(width: 8),
                                    Text('حذف'),
                                  ],
                                ),
                              ),
                            ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // الصف الثاني: المعلومات الإضافية
                  Wrap(
                    spacing: 16,
                    runSpacing: 8,
                    children: [
                      if (guardian.governorate != null)
                        _buildInfoChip(
                          icon: Icons.location_city,
                          label: guardian.governorate!,
                          color: Colors.blue,
                        ),

                      if (guardian.nationality != null)
                        _buildInfoChip(
                          icon: Icons.flag,
                          label: guardian.nationality!,
                          color: Colors.green,
                        ),

                      if (guardian.occupation != null)
                        _buildInfoChip(
                          icon: Icons.work,
                          label: guardian.occupation!,
                          color: Colors.orange,
                        ),

                      _buildInfoChip(
                        icon: Icons.people,
                        label: '${guardian.linkedStudents.length} طالب',
                        color:
                            guardian.linkedStudents.isNotEmpty
                                ? Colors.green
                                : Colors.grey,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// ===== بناء رقاقة المعلومات =====
  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// ===== عرض تفاصيل ولي الأمر =====
  void _showGuardianDetails(BuildContext context, GuardianModel guardian) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              height: MediaQuery.of(context).size.height * 0.8,
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  // العنوان
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundImage:
                            (guardian.profileImageUrl != null &&
                                    guardian.profileImageUrl!.isNotEmpty)
                                ? NetworkImage(guardian.profileImageUrl!)
                                : null,
                        child:
                            (guardian.profileImageUrl == null ||
                                    guardian.profileImageUrl!.isEmpty)
                                ? const Icon(Icons.person, size: 30)
                                : null,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              guardian.name,
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              guardian.email,
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // المحتوى
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildDetailSection(
                            'المعلومات الأساسية',
                            Icons.person,
                            [
                              _buildDetailRow('الاسم الكامل', guardian.name),
                              _buildDetailRow(
                                'البريد الإلكتروني',
                                guardian.email,
                              ),
                              _buildDetailRow(
                                'رقم الهاتف',
                                guardian.phoneNumber,
                              ),
                              if (guardian.alternativePhoneNumber != null)
                                _buildDetailRow(
                                  'رقم الهاتف البديل',
                                  guardian.alternativePhoneNumber!,
                                ),
                              if (guardian.gender != null)
                                _buildDetailRow('الجنس', guardian.gender!),
                            ],
                          ),

                          const SizedBox(height: 24),

                          _buildDetailSection(
                            'معلومات الاتصال',
                            Icons.location_on,
                            [
                              if (guardian.governorate != null)
                                _buildDetailRow(
                                  'المحافظة',
                                  guardian.governorate!,
                                ),
                              if (guardian.nationality != null)
                                _buildDetailRow(
                                  'الجنسية',
                                  guardian.nationality!,
                                ),
                              if (guardian.address != null)
                                _buildDetailRow('العنوان', guardian.address!),
                            ],
                          ),

                          const SizedBox(height: 24),

                          _buildDetailSection('المعلومات المهنية', Icons.work, [
                            if (guardian.occupation != null)
                              _buildDetailRow('المهنة', guardian.occupation!),
                            if (guardian.workplace != null)
                              _buildDetailRow(
                                'مكان العمل',
                                guardian.workplace!,
                              ),
                            if (guardian.educationLevel != null)
                              _buildDetailRow(
                                'المستوى التعليمي',
                                guardian.educationLevel!,
                              ),
                          ]),

                          const SizedBox(height: 24),

                          _buildDetailSection('المعلومات الشخصية', Icons.info, [
                            if (guardian.dateOfBirth != null)
                              _buildDetailRow(
                                'تاريخ الميلاد',
                                '${guardian.dateOfBirth!.day}/${guardian.dateOfBirth!.month}/${guardian.dateOfBirth!.year}',
                              ),
                            if (guardian.nationalId != null)
                              _buildDetailRow(
                                'الرقم الوطني',
                                guardian.nationalId!,
                              ),
                            if (guardian.maritalStatus != null)
                              _buildDetailRow(
                                'الحالة الاجتماعية',
                                guardian.maritalStatus!,
                              ),
                            if (guardian.bloodType != null)
                              _buildDetailRow(
                                'فصيلة الدم',
                                guardian.bloodType!,
                              ),
                            if (guardian.healthCondition != null)
                              _buildDetailRow(
                                'الحالة الصحية',
                                guardian.healthCondition!,
                              ),
                          ]),

                          if (guardian.notes != null &&
                              guardian.notes!.isNotEmpty) ...[
                            const SizedBox(height: 24),
                            _buildDetailSection('ملاحظات', Icons.note, [
                              _buildDetailRow('ملاحظات خاصة', guardian.notes!),
                            ]),
                          ],

                          const SizedBox(height: 24),

                          _buildDetailSection('معلومات النظام', Icons.settings, [
                            _buildDetailRow(
                              'عدد الطلاب المرتبطين',
                              guardian.linkedStudents.length.toString(),
                            ),
                            _buildDetailRow(
                              'حالة الحساب',
                              guardian.accountStatus ?? 'نشط',
                            ),
                            if (guardian.createdAt != null)
                              _buildDetailRow(
                                'تاريخ الإنشاء',
                                '${guardian.createdAt!.toDate().day}/${guardian.createdAt!.toDate().month}/${guardian.createdAt!.toDate().year}',
                              ),
                            if (guardian.updatedAt != null)
                              _buildDetailRow(
                                'آخر تحديث',
                                '${guardian.updatedAt!.toDate().day}/${guardian.updatedAt!.toDate().month}/${guardian.updatedAt!.toDate().year}',
                              ),
                          ]),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  /// ===== بناء قسم التفاصيل =====
  Widget _buildDetailSection(
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    if (children.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: Colors.blue),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  /// ===== بناء صف التفاصيل =====
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: const TextStyle(color: Colors.black54)),
          ),
        ],
      ),
    );
  }

  /// ===== عرض حوار ربط الطلاب =====
  void _showLinkStudentsDialog(BuildContext context, GuardianModel guardian) {
    showDialog(
      context: context,
      builder: (context) => LinkStudentsDialog(guardian: guardian),
    );
  }

  /// ===== عرض حوار التعديل =====
  void _showEditDialog(BuildContext context, GuardianModel guardian) {
    showDialog(
      context: context,
      builder: (context) => GuardianFormDialog(guardian: guardian),
    );
  }
}
