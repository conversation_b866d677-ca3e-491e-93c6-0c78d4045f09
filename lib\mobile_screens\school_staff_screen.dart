import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/providers/staff_providers.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة عرض قائمة الكادر التعليمي والإداري بالمدرسة في تطبيق الجوال باستخدام Riverpod
class SchoolStaffScreen extends ConsumerWidget {
  const SchoolStaffScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // مشاهدة (watch) الـ provider لجلب بيانات الموظفين
    final staffAsyncValue = ref.watch(publicStaffStreamProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('الكادر التعليمي والإداري')),
      body: staffAsyncValue.when(
        // في حالة تحميل البيانات
        loading: () => const LoadingIndicator(),
        // في حالة حدوث خطأ
        error: (err, stack) => ErrorMessage(message: 'حدث خطأ: $err'),
        // في حالة نجاح جلب البيانات
        data: (staffList) {
          if (staffList.isEmpty) {
            return const Center(child: Text('لا يوجد موظفون لعرضهم حالياً.'));
          }

          return ListView.builder(
            padding: const EdgeInsets.all(8.0),
            itemCount: staffList.length,
            itemBuilder: (context, index) {
              final staffMember = staffList[index];
              return _buildStaffMemberCard(context, staffMember);
            },
          );
        },
      ),
    );
  }

  // --- ويدجت بناء بطاقة الموظف ---
  Widget _buildStaffMemberCard(BuildContext context, UserModel staffMember) {
    return Card(
      elevation: 3,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          // عند النقر، يتم عرض ديالوج بالتفاصيل الكاملة
          _showStaffDetailsDialog(context, staffMember);
        },
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              // --- الصورة الشخصية ---
              CircleAvatar(
                radius: 35,
                backgroundColor: Colors.grey.shade200,
                backgroundImage:
                    (staffMember.profileImageUrl != null &&
                            staffMember.profileImageUrl!.isNotEmpty)
                        ? NetworkImage(staffMember.profileImageUrl!)
                        : null,
                child:
                    (staffMember.profileImageUrl == null ||
                            staffMember.profileImageUrl!.isEmpty)
                        ? const Icon(Icons.person, size: 35, color: Colors.grey)
                        : null,
              ),
              const SizedBox(width: 16),
              // --- المعلومات الأساسية المحدثة ---
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // الاسم الكامل
                    Text(
                      staffMember.name,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),

                    // المسمى الوظيفي
                    Text(
                      staffMember.jobTitle ?? 'عضو هيئة التدريس',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // معلومات إضافية مع أيقونات
                    Row(
                      children: [
                        // الدور
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color:
                                staffMember.role == 'teacher'
                                    ? Colors.blue.shade100
                                    : Colors.green.shade100,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            staffMember.role == 'teacher' ? 'معلم' : 'إداري',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color:
                                  staffMember.role == 'teacher'
                                      ? Colors.blue.shade700
                                      : Colors.green.shade700,
                            ),
                          ),
                        ),

                        const SizedBox(width: 8),

                        // الجنس (إذا كان متوفراً)
                        if (staffMember.gender != null &&
                            staffMember.gender!.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.purple.shade100,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              staffMember.gender!,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.purple.shade700,
                              ),
                            ),
                          ),
                      ],
                    ),

                    // معلومات الاتصال (إذا كانت متوفرة)
                    if (staffMember.phoneNumber != null &&
                        staffMember.phoneNumber!.isNotEmpty) ...[
                      const SizedBox(height: 6),
                      Row(
                        children: [
                          Icon(
                            Icons.phone,
                            size: 16,
                            color: Colors.grey.shade600,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              staffMember.phoneNumber!,
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(color: Colors.grey.shade600),
                            ),
                          ),
                        ],
                      ),
                    ],

                    // المحافظة (إذا كانت متوفرة)
                    if (staffMember.governorate != null &&
                        staffMember.governorate!.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.location_city,
                            size: 16,
                            color: Colors.grey.shade600,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            staffMember.governorate!,
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: Colors.grey.shade600),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, color: Colors.grey, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  // --- ديالوج عرض التفاصيل الكاملة للموظف ---
  void _showStaffDetailsDialog(BuildContext context, UserModel staffMember) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: Center(
            child: Text(
              staffMember.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // --- الصورة الشخصية ---
                CircleAvatar(
                  radius: 50,
                  backgroundColor: Colors.grey.shade200,
                  backgroundImage:
                      (staffMember.profileImageUrl != null &&
                              staffMember.profileImageUrl!.isNotEmpty)
                          ? NetworkImage(staffMember.profileImageUrl!)
                          : null,
                  child:
                      (staffMember.profileImageUrl == null ||
                              staffMember.profileImageUrl!.isEmpty)
                          ? const Icon(
                            Icons.person,
                            size: 50,
                            color: Colors.grey,
                          )
                          : null,
                ),
                const SizedBox(height: 16),
                // --- المسمى الوظيفي ---
                Text(
                  staffMember.jobTitle ?? 'عضو هيئة التدريس',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const Divider(height: 24),
                // --- الدور ---
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color:
                        staffMember.role == 'teacher'
                            ? Colors.blue.shade100
                            : Colors.green.shade100,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    staffMember.role == 'teacher' ? 'معلم' : 'إداري',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color:
                          staffMember.role == 'teacher'
                              ? Colors.blue.shade700
                              : Colors.green.shade700,
                    ),
                  ),
                ),

                const SizedBox(height: 20),

                // --- المعلومات الشخصية ---
                if (staffMember.phoneNumber != null &&
                        staffMember.phoneNumber!.isNotEmpty ||
                    staffMember.gender != null &&
                        staffMember.gender!.isNotEmpty ||
                    staffMember.dateOfBirth != null ||
                    staffMember.address != null &&
                        staffMember.address!.isNotEmpty ||
                    staffMember.governorate != null &&
                        staffMember.governorate!.isNotEmpty) ...[
                  const Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      'المعلومات الشخصية',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),

                  if (staffMember.phoneNumber != null &&
                      staffMember.phoneNumber!.isNotEmpty)
                    _buildDetailRow(
                      Icons.phone,
                      'رقم الهاتف',
                      staffMember.phoneNumber!,
                    ),

                  if (staffMember.gender != null &&
                      staffMember.gender!.isNotEmpty)
                    _buildDetailRow(Icons.person, 'الجنس', staffMember.gender!),

                  if (staffMember.dateOfBirth != null)
                    _buildDetailRow(
                      Icons.cake,
                      'تاريخ الميلاد',
                      '${staffMember.dateOfBirth!.day}/${staffMember.dateOfBirth!.month}/${staffMember.dateOfBirth!.year}',
                    ),

                  if (staffMember.address != null &&
                      staffMember.address!.isNotEmpty)
                    _buildDetailRow(
                      Icons.location_on,
                      'العنوان',
                      staffMember.address!,
                    ),

                  _buildDetailRow(
                    Icons.flag,
                    'الجنسية',
                    staffMember.nationality,
                  ),

                  if (staffMember.governorate != null &&
                      staffMember.governorate!.isNotEmpty)
                    _buildDetailRow(
                      Icons.location_city,
                      'المحافظة',
                      staffMember.governorate!,
                    ),

                  const SizedBox(height: 16),
                ],

                // --- المعلومات الإضافية ---
                if ((staffMember.nationalId != null &&
                        staffMember.nationalId!.isNotEmpty) ||
                    (staffMember.bloodType != null &&
                        staffMember.bloodType!.isNotEmpty) ||
                    (staffMember.healthCondition != null &&
                        staffMember.healthCondition!.isNotEmpty)) ...[
                  const Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      'المعلومات الإضافية',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),

                  if (staffMember.nationalId != null &&
                      staffMember.nationalId!.isNotEmpty)
                    _buildDetailRow(
                      Icons.credit_card,
                      'الرقم الوطني',
                      staffMember.nationalId!,
                    ),

                  if (staffMember.bloodType != null &&
                      staffMember.bloodType!.isNotEmpty)
                    _buildDetailRow(
                      Icons.bloodtype,
                      'فصيلة الدم',
                      staffMember.bloodType!,
                    ),

                  if (staffMember.healthCondition != null &&
                      staffMember.healthCondition!.isNotEmpty)
                    _buildDetailRow(
                      Icons.health_and_safety,
                      'الحالة الصحية',
                      staffMember.healthCondition!,
                    ),

                  const SizedBox(height: 16),
                ],

                // --- النبذة التعريفية ---
                const Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    'النبذة التعريفية',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                ),
                const SizedBox(height: 12),

                if (staffMember.bio != null && staffMember.bio!.isNotEmpty)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Text(
                      staffMember.bio!,
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(height: 1.5),
                    ),
                  )
                else
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: const Text(
                      'لا توجد نبذة تعريفية متاحة حالياً.',
                      style: TextStyle(
                        color: Colors.grey,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),

                // --- الملاحظات (إذا كانت متوفرة) ---
                if (staffMember.notes != null &&
                    staffMember.notes!.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  const Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      'ملاحظات',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.purple.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.purple.shade200),
                    ),
                    child: Text(
                      staffMember.notes!,
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(height: 1.5),
                    ),
                  ),
                ],
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            ),
          ],
        );
      },
    );
  }

  /// بناء صف تفاصيل مع أيقونة
  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey.shade600),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
