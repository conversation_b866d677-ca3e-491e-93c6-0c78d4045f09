/// ملف تجميع شاشات الأولياء
///
/// هذا الملف يجمع جميع شاشات الأولياء في مكان واحد
/// لسهولة الاستيراد والاستخدام في أجزاء أخرى من التطبيق
///
/// الشاشات المتضمنة:
/// - لوحة تحكم الأولياء الرئيسية
/// - شاشة متابعة أداء الأبناء
/// - شاشة التواصل مع المدرسة
///
/// الاستخدام:
/// ```dart
/// import 'package:school_management_system/parent_screens/parent_screens_exports.dart';
///
/// // يمكن الآن استخدام جميع الشاشات مباشرة
/// ParentDashboardScreen(parentId: parentId)
/// ChildrenPerformanceScreen(parentId: parentId)
/// SchoolCommunicationScreen(parentId: parentId)
/// ```

// تصدير لوحة تحكم الأولياء
export 'parent_dashboard_screen.dart';

// تصدير شاشة متابعة أداء الأبناء
export 'children_performance_screen.dart';

// تصدير شاشة التواصل مع المدرسة
export 'school_communication_screen.dart';

// تصدير شاشة إنشاء الطلبات
export 'create_request_screen.dart';

// تصدير شاشة جدول امتحانات الأبناء
export 'children_exam_schedule_screen.dart';
