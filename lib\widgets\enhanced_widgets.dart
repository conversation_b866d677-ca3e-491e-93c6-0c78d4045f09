import 'package:flutter/material.dart';
import 'package:school_management_system/shared/app_theme.dart';

/// مجموعة من الويدجت المحسنة والموحدة لتطبيق إدارة المدرسة
/// 
/// تحتوي على مكونات قابلة لإعادة الاستخدام تطبق التصميم الموحد
/// وتوفر تجربة مستخدم متناسقة عبر جميع الشاشات

/// بطاقة عرض محسنة مع التصميم الموحد
/// 
/// تطبق نظام الألوان والمسافات والحدود الموحد
/// مع إمكانيات تخصيص متقدمة
class EnhancedCard extends StatelessWidget {
  /// المحتوى الذي سيوضع داخل البطاقة
  final Widget child;
  
  /// دالة تُستدعى عند الضغط على البطاقة (اختياري)
  final VoidCallback? onTap;
  
  /// لون خلفية البطاقة (اختياري - افتراضي: سطح أبيض)
  final Color? backgroundColor;
  
  /// مستوى الظل (اختياري - افتراضي: متوسط)
  final double? elevation;
  
  /// الحشو الداخلي (اختياري - افتراضي: حشو البطاقات)
  final EdgeInsetsGeometry? padding;
  
  /// نصف قطر الحدود (اختياري - افتراضي: كبير)
  final double? borderRadius;
  
  /// لون الحدود (اختياري)
  final Color? borderColor;
  
  /// عرض الحدود (اختياري)
  final double? borderWidth;

  const EnhancedCard({
    super.key,
    required this.child,
    this.onTap,
    this.backgroundColor,
    this.elevation,
    this.padding,
    this.borderRadius,
    this.borderColor,
    this.borderWidth,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = backgroundColor ?? AppColors.surface;
    final effectiveElevation = elevation ?? AppElevation.medium;
    final effectivePadding = padding ?? const EdgeInsets.all(AppSpacing.cardPadding);
    final effectiveBorderRadius = borderRadius ?? AppBorderRadius.large;

    return Card(
      color: effectiveBackgroundColor,
      elevation: effectiveElevation,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(effectiveBorderRadius),
        side: borderColor != null
            ? BorderSide(
                color: borderColor!,
                width: borderWidth ?? 1.0,
              )
            : BorderSide.none,
      ),
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(effectiveBorderRadius),
        child: Padding(
          padding: effectivePadding,
          child: child,
        ),
      ),
    );
  }
}

/// زر محسن مع أيقونة وتصميم موحد
/// 
/// يطبق نظام الألوان والخطوط الموحد مع إمكانيات تخصيص
class EnhancedButton extends StatelessWidget {
  /// نص الزر
  final String text;
  
  /// أيقونة الزر (اختياري)
  final IconData? icon;
  
  /// دالة تُستدعى عند الضغط على الزر
  final VoidCallback? onPressed;
  
  /// نوع الزر (مرفوع، محدد، نصي)
  final ButtonType type;
  
  /// لون الزر (اختياري - افتراضي: أساسي)
  final Color? color;
  
  /// حجم الزر (صغير، متوسط، كبير)
  final ButtonSize size;
  
  /// عرض الزر كاملاً (افتراضي: false)
  final bool fullWidth;
  
  /// حالة التحميل (افتراضي: false)
  final bool isLoading;

  const EnhancedButton({
    super.key,
    required this.text,
    this.icon,
    this.onPressed,
    this.type = ButtonType.elevated,
    this.color,
    this.size = ButtonSize.medium,
    this.fullWidth = false,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? AppColors.primary;
    
    // تحديد الحشو بناءً على الحجم
    EdgeInsetsGeometry padding;
    double fontSize;
    
    switch (size) {
      case ButtonSize.small:
        padding = const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.sm,
        );
        fontSize = AppTextSizes.labelMedium;
        break;
      case ButtonSize.medium:
        padding = const EdgeInsets.symmetric(
          horizontal: AppSpacing.buttonPadding,
          vertical: AppSpacing.md,
        );
        fontSize = AppTextSizes.labelLarge;
        break;
      case ButtonSize.large:
        padding = const EdgeInsets.symmetric(
          horizontal: AppSpacing.lg,
          vertical: AppSpacing.lg,
        );
        fontSize = AppTextSizes.titleMedium;
        break;
    }

    Widget buttonChild;
    
    if (isLoading) {
      buttonChild = SizedBox(
        height: 20,
        width: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            type == ButtonType.elevated ? AppColors.textOnPrimary : effectiveColor,
          ),
        ),
      );
    } else if (icon != null) {
      buttonChild = Row(
        mainAxisSize: fullWidth ? MainAxisSize.max : MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: fontSize + 2),
          const SizedBox(width: AppSpacing.sm),
          Text(text),
        ],
      );
    } else {
      buttonChild = Text(text);
    }

    final buttonStyle = _getButtonStyle(type, effectiveColor, padding, fontSize);

    Widget button;
    switch (type) {
      case ButtonType.elevated:
        button = ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: buttonChild,
        );
        break;
      case ButtonType.outlined:
        button = OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: buttonChild,
        );
        break;
      case ButtonType.text:
        button = TextButton(
          onPressed: isLoading ? null : onPressed,
          style: buttonStyle,
          child: buttonChild,
        );
        break;
    }

    return fullWidth
        ? SizedBox(width: double.infinity, child: button)
        : button;
  }

  ButtonStyle _getButtonStyle(
    ButtonType type,
    Color color,
    EdgeInsetsGeometry padding,
    double fontSize,
  ) {
    switch (type) {
      case ButtonType.elevated:
        return ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: AppColors.textOnPrimary,
          padding: padding,
          textStyle: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.w600,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          ),
        );
      case ButtonType.outlined:
        return OutlinedButton.styleFrom(
          foregroundColor: color,
          side: BorderSide(color: color, width: 1.5),
          padding: padding,
          textStyle: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.w600,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          ),
        );
      case ButtonType.text:
        return TextButton.styleFrom(
          foregroundColor: color,
          padding: padding,
          textStyle: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.w600,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.medium),
          ),
        );
    }
  }
}

/// أنواع الأزرار المدعومة
enum ButtonType {
  elevated,  // زر مرفوع
  outlined,  // زر محدد
  text,      // زر نصي
}

/// أحجام الأزرار المدعومة
enum ButtonSize {
  small,   // صغير
  medium,  // متوسط
  large,   // كبير
}

/// مؤشر تحميل محسن مع رسالة
/// 
/// يعرض مؤشر تحميل مع رسالة اختيارية بتصميم موحد
class EnhancedLoadingIndicator extends StatelessWidget {
  /// الرسالة المعروضة مع مؤشر التحميل (اختياري)
  final String? message;
  
  /// حجم مؤشر التحميل (افتراضي: 40)
  final double size;
  
  /// لون مؤشر التحميل (افتراضي: أساسي)
  final Color? color;

  const EnhancedLoadingIndicator({
    super.key,
    this.message,
    this.size = 40,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? AppColors.primary;

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              color: effectiveColor,
              strokeWidth: 3,
            ),
          ),
          if (message != null) ...[
            const SizedBox(height: AppSpacing.md),
            Text(
              message!,
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: AppTextSizes.bodyMedium,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
